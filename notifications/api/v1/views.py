from django.utils import timezone
from drf_yasg.utils import swagger_auto_schema
from rest_framework import permissions, status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response

from lms.utils import get_paginated_serializer
from notifications.api.v1.serializers import UINotificationSerializer
from notifications.models import UINotification


class UINotificationViewSet(viewsets.ModelViewSet):
    serializer_class = UINotificationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        if getattr(self, 'swagger_fake_view', False):
            return UINotification.objects.none()
        return UINotification.objects.filter(user=self.request.user).order_by('-created_at')

    @action(detail=True, methods=['post'])
    def mark_as_read(self, request, pk=None):
        """POST /ui-notifications/{id}/mark_as_read/ — пометить уведомление как прочитанное"""
        notification = self.get_object()
        if not notification.is_read:
            notification.is_read = True
            notification.read_at = timezone.now()
            notification.save(update_fields=["is_read", "read_at"])
        return Response({"success": True}, status=200)

    @action(detail=False, methods=['post'])
    def mark_all_as_read(self, request):
        """POST /ui-notifications/mark_all_as_read/ — пометить все уведомления как прочитанные"""
        updated = UINotification.objects.filter(
            user=request.user, is_read=False
        ).update(is_read=True, read_at=timezone.now())
        return Response({"success": True}, status=200)

    @swagger_auto_schema(
        responses={
            200: get_paginated_serializer(serializer_class),
            }
        )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)