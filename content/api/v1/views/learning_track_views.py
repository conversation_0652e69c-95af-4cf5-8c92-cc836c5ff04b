from drf_yasg.utils import swagger_auto_schema
from rest_framework import status
from rest_framework.exceptions import ValidationError
from rest_framework.generics import (
    ListAPIView, CreateAPIView,
)
from rest_framework.permissions import (
    IsAuthenticated,
)
from rest_framework.response import Response
from rest_framework.views import APIView

from content.api.v1.serializers import (
    OutlineSerializer, LearningTrackAddStageSerializer, LearningTrackDeleteStageSerializer,
    LearningTrackAddCoursesSerializer, LearningTrackUpdateCourseAvailabilitySerializer
)
from content.models import Outline


class LearningTrackOutlineAPIView(ListAPIView):
    serializer_class = OutlineSerializer
    pagination_class = None

    def get_queryset(self):
        return Outline.objects.filter(
            learning_track_id=self.kwargs.get('pk')
        ).select_related('resource', 'access_time')

    @swagger_auto_schema(tags=["LearningTrack"])
    def get(self, request, *args, **kwargs):
        return self.list(request, *args, **kwargs)

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()

        parents = []
        children_map = {}

        for outline in queryset:
            if outline.parent_id is None:
                parents.append(outline)
            else:
                children_map.setdefault(outline.parent_id, []).append(outline)

        ordered = []
        for parent in sorted(parents, key=lambda x: x.order):
            ordered.append(parent)
            for child in sorted(children_map.get(parent.id, []), key=lambda x: x.order):
                ordered.append(child)

        serializer = self.get_serializer(ordered, many=True)
        return Response(serializer.data)


class LearningTrackAddStageAPIView(CreateAPIView):
    queryset = Outline.objects.all()
    serializer_class = LearningTrackAddStageSerializer

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['learning_track_id'] = self.kwargs['pk']
        return context

    @swagger_auto_schema(tags=["LearningTrack"], request_body=LearningTrackAddStageSerializer)
    def post(self, request, *args, **kwargs):
        return super().post(request, *args, **kwargs)


class LearningTrackDeleteStageAPIView(APIView):
    @swagger_auto_schema(tags=["LearningTrack"], request_body=LearningTrackDeleteStageSerializer)
    def delete(self, request, pk, *args, **kwargs):
        learning_track_id = pk
        serializer = LearningTrackDeleteStageSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        stage = serializer.validated_data['stage']

        try:
            outline = Outline.objects.get(id=stage.id, learning_track_id=learning_track_id)
        except Outline.DoesNotExist:
            raise ValidationError("Стадия не найдена в указанном ТО.")

        if outline.type != Outline.TYPE_STAGE:
            raise ValidationError("Удалять можно только стадии (STAGE).")

        outline.delete()

        return Response({"success": True}, status=status.HTTP_204_NO_CONTENT)


class LearningTrackAddCoursesAPIView(CreateAPIView):
    queryset = Outline.objects.all()
    permission_classes = [IsAuthenticated]
    serializer_class = LearningTrackAddCoursesSerializer

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['learning_track_id'] = self.kwargs['pk']
        context['stage_id'] = self.kwargs['stage_id']
        return context

    @swagger_auto_schema(tags=["LearningTrack"], request_body=LearningTrackAddCoursesSerializer)
    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response({'success': True}, status=status.HTTP_201_CREATED)


class LearningTrackUpdateCourseAvailabilityAPIView(APIView):
    def get_serializer_context(self):
        return {
            'learning_track_id': self.kwargs['pk'],
            'stage_id': self.kwargs['stage_id'],
        }

    @swagger_auto_schema(tags=["LearningTrack"], request_body=LearningTrackUpdateCourseAvailabilitySerializer)
    def patch(self, request, *args, **kwargs):
        serializer = LearningTrackUpdateCourseAvailabilitySerializer(
            data=request.data,
            context=self.get_serializer_context()
        )
        serializer.is_valid(raise_exception=True)
        serializer.update_availability()

        return Response({'success': True})