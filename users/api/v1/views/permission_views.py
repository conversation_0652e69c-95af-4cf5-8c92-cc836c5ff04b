from django.utils.translation import gettext_lazy as _
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import generics, status
from rest_framework.response import Response
from rest_framework.views import APIView

from users.api.v1.serializers.permission_serializers import (
    PermissionSerializer,
    RolePermissionsSerializer,
)
from users.api.v1.serializers.role_serializers import RoleQuerySerializer
from users.models import Permission, Role, User
from users.permissions.codes import CanViewGroups


class UserPermissionsView(generics.GenericAPIView):
    queryset = User.objects.all()
    serializer_class = PermissionSerializer
    permission_classes = [CanViewGroups]
    pagination_class = None

    def get(self, request, *args, **kwargs):
        user = self.get_object()
        permissions = Permission.objects.filter(roles__users=user).distinct()
        serializer = self.serializer_class(permissions, many=True)

        return Response(serializer.data, status=status.HTTP_200_OK)


class RolePermissionsView(APIView):
    queryset = Permission.objects.all()
    permission_classes = [CanViewGroups]

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                name="roles",
                in_=openapi.IN_QUERY,
                description="Role UUID",
                type=openapi.TYPE_STRING
            )
        ],
        responses={200: RolePermissionsSerializer(many=True)},
    )
    def get(self, request, *args, **kwargs):
        ids = request.query_params.getlist("roles")
        serializer = RoleQuerySerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        results = Role.objects.filter(
            id__in=ids,
        ).prefetch_related("permissions")

        serializer = RolePermissionsSerializer(results, many=True)

        return Response(serializer.data, status=status.HTTP_200_OK)
