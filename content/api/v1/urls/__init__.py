from django.urls import path, include


urlpatterns = [
    path('projects/', include('content.api.v1.urls.project_urls')),
    path('resources/', include('content.api.v1.urls.resource_urls')),
    path('enrollments/', include('content.api.v1.urls.enrollment_urls')),
    path('learning-track/', include('content.api.v1.urls.learning_track_urls')),
    path('course/', include('content.api.v1.urls.course_urls')),
    path('supervisor-dashboard/', include('content.api.v1.urls.supervisor_dashboard_urls')),
    path('catalogs/', include('content.api.v1.urls.catalog_urls')),
]