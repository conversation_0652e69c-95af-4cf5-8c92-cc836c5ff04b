"""
Файл настроек Celery
https://docs.celeryproject.org/en/stable/django/first-steps-with-django.html
"""
from __future__ import absolute_import, unicode_literals

import os

from celery import Celery
from celery.schedules import crontab
from django.conf import settings
from django.core.mail import EmailMessage
from celery import shared_task

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'lms.settings')
app = Celery("lms")
app.config_from_object('django.conf:settings', namespace='CELERY')

app.autodiscover_tasks()
app.conf.timezone = 'Asia/Atyrau'

app.conf.beat_schedule = {
    'schedule_users': {
        'task': 'users.tasks.import_tasks.users_schedule',
        'schedule': crontab(hour='23', minute='59'),
    },
    'send-pending-notifications-every-minute': {
        'task': 'notifications.tasks.send_pending_notifications',
        'schedule': crontab(minute='*/1'),  # Каждую минуту
    },
    'auto_enrollment_task': {
        'task': 'content.tasks.enrollment.auto_enroll_users',
        'schedule': crontab(hour='23', minute='59'),
    },
    'check_expire_certificate': {
        'task': 'content.tasks.enrollment.check_expire_certificate',
        'schedule': crontab(hour='00', minute='01'),
    },
    'import-companies-from-core': {
        'task': 'users.tasks.import_tasks.import_companies_task',
        'schedule': crontab(hour='05', minute='00'),
        'args': [],
        'kwargs': {'dry_run': False},
    },
    'import-departments-from-core': {
        'task': 'users.tasks.import_tasks.import_departments_task',
        'schedule': crontab(hour='05', minute='05'),
        'args': [],
        'kwargs': {'dry_run': False},
    },
    'import-positions-from-core': {
        'task': 'users.tasks.import_tasks.import_positions_task',
        'schedule': crontab(hour='05', minute='10'),
        'args': [],
        'kwargs': {'dry_run': False},
    },
    'import-department-positions-from-core': {
        'task': 'users.tasks.import_tasks.import_department_positions_task',
        'schedule': crontab(hour='05', minute='15'),
        'args': [],
        'kwargs': {'dry_run': False},
    },
    'import-users-from-core': {
        'task': 'users.tasks.import_tasks.import_users_task',
        'schedule': crontab(hour='06', minute='00'),
        'args': [],
        'kwargs': {'dry_run': False},
    },
    'auto_resource_reassignment': {
        'task': 'content.tasks.enrollment.auto_resource_reassignment',
        'schedule': crontab(hour='23', minute='50'),
    },
    'overdue_enrollments_finish': {
        'task': 'overdue_enrollments_finish',
        'schedule': crontab(hour='03', minute='00')
    }
}

# celery -A lms worker -l info
# celery -A lms.celery beat

@shared_task(name="send_mail_celery")
def send_mail_celery(subject: str, message: str, to: list, cc: list = None, bcc: list = None) -> None:
    msg = EmailMessage(subject=subject, body=message, from_email=settings.DEFAULT_FROM_EMAIL, to=to, cc=cc, bcc=bcc)
    msg.content_subtype = "html"
    msg.encoding = 'utf-8'
    msg.send()
