from django.db import transaction
from jsonschema.exceptions import ValidationError
from rest_framework import serializers

from content.api.v1.serializers import TagSerializer
from content.api.v1.utils import coerce_types
from content.models import Resource, Catalog, Tag, Detail, Preference, Settings
from content.utils.validators import ConditionValueValidator
from users.api.v1.serializers import UserSerializer
from users.models import User
from django.utils.translation import gettext_lazy as _
from jsonschema.validators import validate



class ResourceCreateSerializer(serializers.ModelSerializer):
    name = serializers.CharField(max_length=255)
    type = serializers.ChoiceField(choices=Resource.RESOURCE_CHOICES)
    parent = serializers.PrimaryKeyRelatedField(queryset=Resource.objects.all())
    owner = serializers.HiddenField(default=serializers.CurrentUserDefault())

    class Meta:
        model = Resource
        fields = ("id", "name", "type", "parent", "owner", "payload")

    def create(self, validated_data):
        with transaction.atomic():
            resource = super().create(validated_data)
            Detail.objects.create(
                resource=resource,
                instructor=resource.owner,
            )
        return resource


class DetailUpdateSerializer(serializers.Serializer):
    name = serializers.CharField(required=False, max_length=255, write_only=True)
    description = serializers.CharField(
        required=False, allow_null=True, max_length=32768
    )
    recommended_time = serializers.IntegerField(required=False, min_value=0)
    thumbnail = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    cover = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    instructor = serializers.PrimaryKeyRelatedField(
        required=True, queryset=User.objects.all()
    )
    tags = serializers.ListField(required=False, allow_null=True, allow_empty=True)

    def validate(self, attrs):
        if "name" not in attrs:
            raise serializers.ValidationError({"name": "This field is required."})
        if "instructor" not in attrs:
            raise serializers.ValidationError({"instructor": "This field is required."})
        return attrs

    def update(self, instance, validated_data):
        with transaction.atomic():
            if "name" in validated_data:
                instance.resource.name = validated_data["name"]
            if "description" in validated_data:
                instance.resource.description = validated_data["description"]
            instance.resource.save(update_fields=["name", "description"])

            if "recommended_time" in validated_data:
                instance.recommended_time = validated_data["recommended_time"]
            if "thumbnail" in validated_data:
                instance.thumbnail = validated_data["thumbnail"]
            if "cover" in validated_data:
                instance.cover = validated_data["cover"]
            if "instructor" in validated_data:
                instance.instructor = validated_data["instructor"]
            instance.save()

            if "tags" in validated_data:
                instance.tags.clear()
                tag_names = {str(tag).strip().lower() for tag in validated_data["tags"]}
                existing_tags = {
                    tag.name: tag for tag in Tag.objects.filter(name__in=tag_names)
                }
                new_tags = [
                    Tag(name=name) for name in tag_names if name not in existing_tags
                ]
                if new_tags:
                    Tag.objects.bulk_create(new_tags)
                all_tags = Tag.objects.filter(name__in=tag_names)
                instance.tags.add(*all_tags)

        return instance


class PreferenceSerializer(serializers.ModelSerializer):
    class Meta:
        model = Preference
        fields = ("id", "type", "code", "name", "payload")


class SettingsDetailSerializer(serializers.ModelSerializer):
    class Meta:
        model = Settings
        fields = ("id", "payload", "order", "preference", "resource")


class RecursiveField(serializers.BaseSerializer):
    def to_representation(self, value):
        serializer_class = self.parent.parent.__class__
        serializer = serializer_class(value, context=self.context)
        return serializer.data


class ResourceTreeSerializer(serializers.ModelSerializer):
    children = RecursiveField(many=True, required=False)
    owner = UserSerializer(read_only=True)

    class Meta:
        model = Resource
        fields = (
            "id",
            "name",
            "description",
            "type",
            "order",
            "owner",
            "is_shared",
            "is_deleted",
            "children",
            "created_at",
            "updated_at",
        )


class CatalogSerializer(serializers.ModelSerializer):
    resources_count = serializers.IntegerField()

    class Meta:
        model = Catalog
        fields = (
            "id",
            "name",
            "description",
            "thumbnail",
            "order",
            "resources_count",
        )


class DetailSerializer(serializers.ModelSerializer):
    detail = serializers.SerializerMethodField()
    instructor = UserSerializer()
    tags = TagSerializer(many=True)
    mobile_only = serializers.BooleanField(default=False)

    @staticmethod
    def get_detail(obj):
        return obj.pk

    class Meta:
        model = Detail
        fields = (
            "id",
            "detail",
            "recommended_time",
            "thumbnail",
            "cover",
            "instructor",
            "tags",
            "mobile_only",
        )


class ResourceRetrieveSerializer(serializers.ModelSerializer):
    owner = UserSerializer(read_only=True)
    detail = DetailSerializer(read_only=True, required=False)

    class Meta:
        model = Resource
        fields = (
            "id",
            "name",
            "description",
            "type",
            "parent_id",
            "owner",
            "payload",
            "created_at",
            "updated_at",
            "detail",
            "is_deleted",
        )


class ResourceUpdateSerializer(serializers.ModelSerializer):
    name = serializers.CharField(max_length=255)
    parent = serializers.PrimaryKeyRelatedField(queryset=Resource.objects.all())

    class Meta:
        model = Resource
        fields = (
            "id",
            "name",
            "description",
            "parent",
            "is_shared",
            "order",
        )


class SettingsMapSerializer(serializers.Serializer):
    def to_internal_value(self, data):
        if not isinstance(data, dict):
            raise serializers.ValidationError(_("invalid format"))
        validated = {}
        errors = {}

        for preference_id, payload in data.items():
            payload = coerce_types(payload)
            preference = Preference.objects.filter(id=preference_id).first()
            if not preference:
                errors[preference_id] = _("invalid preference key")
            if "validation" not in preference.payload:
                errors[preference_id] = _("there is no scheme for form validation")
            try:
                validate(instance=payload, schema=preference.payload["validation"])
            except ValidationError as e:
                errors[preference_id] = e.message

            # Доп. проверка condition_value через отдельный класс
            validator = ConditionValueValidator(
                payload=payload, preference_id=preference_id
            )
            cond_errors = validator.validate()
            if cond_errors:
                errors.update(cond_errors)

            validated[preference_id] = payload

        if errors:
            raise serializers.ValidationError(errors)

        return validated

    def create(self, validated_data):
        with transaction.atomic():
            for preference_id, payload in validated_data.items():
                Settings.objects.update_or_create(
                    preference_id=preference_id,
                    resource_id=self.context["resource_id"],
                    defaults={
                        "payload": payload,
                    },
                )
        return validated_data