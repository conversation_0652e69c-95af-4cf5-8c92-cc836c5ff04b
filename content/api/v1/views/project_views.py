from django.db import transaction
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import generics, status
from rest_framework.exceptions import PermissionDenied
from rest_framework.generics import (
    ListAPIView, GenericAPIView, get_object_or_404,
)
from rest_framework.response import Response
from rest_framework.views import APIView

from content.models import Resource, Participant
from content.permissions.codes import CanCreateAndManageProjectInTrainingMaterialsSection, \
    CanViewAndAssignMaterialsToUsers
from ..serializers.project_serializers import (
    ProjectSerializer,
    ProjectCreateSerializer, ProjectRetrieveSerializer, ProjectUpdateSerializer, ParticipantSerializer,
    ParticipantBulkCreateSerializer, ParticipantTypeUpdateSerializer
)


class ProjectCreateView(APIView):
    permission_classes = [CanCreateAndManageProjectInTrainingMaterialsSection]

    @swagger_auto_schema(
        request_body=ProjectCreateSerializer,
    )
    def post(self, request, *args, **kwargs):
        serializer = ProjectCreateSerializer(data=request.data, context=request)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data, status=status.HTTP_201_CREATED)


class ProjectListView(ListAPIView):
    queryset = Resource.objects.all()
    serializer_class = ProjectSerializer
    permission_classes = [CanCreateAndManageProjectInTrainingMaterialsSection]
    pagination_class = None

    def get_queryset(self):
        queryset = self.queryset.filter(
            parent__isnull=True,
            type=Resource.FOLDER,
        ).order_by('name')
        return queryset


class ProjectRetrieveDestroyView(generics.RetrieveDestroyAPIView):
    queryset = Resource.objects.all()
    serializer_class = ProjectRetrieveSerializer
    permission_classes = [CanCreateAndManageProjectInTrainingMaterialsSection]

    def get_queryset(self):
        return self.queryset.filter(
            type=Resource.FOLDER,
        ).select_related('owner')


class ProjectUpdateView(generics.UpdateAPIView):
    queryset = Resource.objects.all()
    serializer_class = ProjectUpdateSerializer
    permission_classes = [CanCreateAndManageProjectInTrainingMaterialsSection]

    def perform_update(self, serializer):
        instance = self.get_object()
        if instance.owner != self.request.user and not self.request.user.is_superuser:
            raise PermissionDenied("You do not have permission to update this resource.")
        serializer.save()


class ParticipantListView(generics.ListAPIView):
    queryset = Participant.objects.all()
    serializer_class = ParticipantSerializer
    permission_classes = [CanViewAndAssignMaterialsToUsers]
    pagination_class = None

    def get_queryset(self):
        if getattr(self, 'swagger_fake_view', False):
            return Participant.objects.none()

        queryset = self.queryset.filter(
            resource_id=self.kwargs['pk'],
        ).select_related('user')
        return queryset


class ParticipantCreateAPIView(GenericAPIView):
    serializer_class = ParticipantBulkCreateSerializer
    permission_classes = [CanViewAndAssignMaterialsToUsers]
    queryset = Participant.objects.none()

    def get_resource(self):
        return get_object_or_404(Resource, pk=self.kwargs['pk'])

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['resource'] = self.get_resource()
        return context

    @swagger_auto_schema(
        request_body=ParticipantBulkCreateSerializer,
        responses={201: openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'success': openapi.Schema(type=openapi.TYPE_BOOLEAN)
            }
        )}
    )
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        resource = self.get_resource()
        availability = serializer.validated_data['availability']
        user_list = serializer.validated_data['user_ids']

        for user in user_list:
            Participant.objects.update_or_create(
                resource=resource,
                user=user,
                defaults={'availability': availability}
            )

        return Response({'success': True}, status=status.HTTP_201_CREATED)


class ProjectParticipantTypeView(APIView):
    serializer_class = ParticipantTypeUpdateSerializer
    permission_classes = [CanViewAndAssignMaterialsToUsers]

    @swagger_auto_schema(
        request_body=ParticipantTypeUpdateSerializer,
    )
    def post(self, request, *args, **kwargs):
        participant = Participant.objects.filter(
            pk=kwargs['participant_id'],
            resource_id=kwargs['pk'],
        ).first()

        if not participant:
            return Response(status=status.HTTP_404_NOT_FOUND)

        serializer = self.serializer_class(data=request.data, instance=participant)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        serializer.save()

        return Response(status=status.HTTP_200_OK)


class ProjectParticipantOwnerView(APIView):
    permission_classes = [CanViewAndAssignMaterialsToUsers]

    def post(self, request, *args, **kwargs):
        participant = Participant.objects.filter(
            pk=kwargs['participant_id'],
            resource_id=kwargs['pk'],
        ).first()

        if not participant:
            return Response(status=status.HTTP_404_NOT_FOUND)

        with transaction.atomic():
            Resource.objects.filter(pk=kwargs['pk']).update(
                owner=participant.user,
            )
            participant.delete()
        return Response(status=status.HTTP_200_OK)


class ProjectParticipantLeaveView(generics.DestroyAPIView):
    serializer_class = ParticipantSerializer
    permission_classes = [CanViewAndAssignMaterialsToUsers]
    queryset = Participant.objects.all()
    lookup_field = 'participant_id'

    def get_queryset(self):
        return self.queryset.filter(
            pk=self.kwargs.get('participant_id'),
            resource_id=self.kwargs.get('pk')
        )

    def get_object(self):
        queryset = self.get_queryset()
        return get_object_or_404(queryset)