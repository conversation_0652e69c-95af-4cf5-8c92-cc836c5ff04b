from .models import ChannelType
from .senders.email import Email<PERSON>ender
from .senders.messenger import MessengerSender
from .senders.sms import SMSSender


class SenderFactory:
    @staticmethod
    def get_sender(notification):
        if notification.channel == ChannelType.EMAIL:
            return EmailSender(notification)
        elif notification.channel == ChannelType.SMS:
            return SMSSender(notification)
        elif notification.channel == ChannelType.MESSENGER:
            return MessengerSender(notification)
        else:
            raise ValueError('Unknown channel')
