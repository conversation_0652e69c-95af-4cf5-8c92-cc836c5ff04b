from django.db import models, transaction
from rest_framework import serializers

from content.api.v1.serializers import ResourceSerializer
from content.models import OutlineAccessTime, Outline, Resource


class OutlineAccessTimeSerializer(serializers.ModelSerializer):
    class Meta:
        model = OutlineAccessTime
        fields = [
            "id",
            "type",
            "interval",
            "period",
            "period_unit",
            "lock_after_expiration",
            "outline_id",
        ]


class OutlineSerializer(serializers.ModelSerializer):
    resource = ResourceSerializer(read_only=True)
    access_time = OutlineAccessTimeSerializer(read_only=True)

    class Meta:
        model = Outline
        fields = [
            "id",
            "learning_track_id",
            "resource",
            "parent_id",
            "type",
            "order",
            "access_time",
        ]


class LearningTrackAddStageSerializer(serializers.ModelSerializer):
    after_stage_id = serializers.PrimaryKeyRelatedField(
        queryset=Outline.objects.all(),
        required=False,
        allow_null=True,
        write_only=True,
    )
    resource_id = serializers.PrimaryKeyRelatedField(
        queryset=Resource.objects.all(),
        source="resource",
        write_only=True,
        label="ID STAGE (Resource)",
    )

    def validate(self, attrs):
        resource = attrs.get("resource")
        learning_track_id = self.context["learning_track_id"]

        if (
            Outline.objects.filter(resource=resource)
            .exclude(learning_track_id=learning_track_id)
            .exists()
        ):
            raise serializers.ValidationError(
                {"resource_id": "Этот ресурс уже используется в другом TO."}
            )

        if resource.type != Resource.STAGE:
            raise serializers.ValidationError(
                {"resource_id": "Ресурс должен быть типа STAGE."}
            )

        if Outline.objects.filter(
            learning_track_id=learning_track_id, resource=resource
        ).exists():
            raise serializers.ValidationError(
                {"resource_id": "Этот ресурс уже добавлен в TO."}
            )

        return attrs

    class Meta:
        model = Outline
        fields = ["id", "after_stage_id", "resource_id"]

    def create(self, validated_data):
        after_stage = validated_data.get("after_stage_id", None)
        resource = validated_data.get("resource")
        learning_track_id = self.context["learning_track_id"]

        if after_stage:
            if after_stage.learning_track_id != learning_track_id:
                raise serializers.ValidationError(
                    "after_stage_id не принадлежит этому learning_track."
                )

            order = after_stage.order + 1

            Outline.objects.filter(
                learning_track_id=learning_track_id, order__gt=after_stage.order
            ).update(order=models.F("order") + 1)
        else:
            max_order = (
                Outline.objects.filter(
                    learning_track_id=learning_track_id,
                ).aggregate(models.Max("order"))["order__max"]
                or 0
            )

            order = max_order + 1

        return Outline.objects.create(
            learning_track_id=learning_track_id,
            order=order,
            type=Outline.TYPE_STAGE,
            resource=resource,
        )

    def to_representation(self, instance):
        return OutlineSerializer(instance).data


class LearningTrackDeleteStageSerializer(serializers.Serializer):
    stage_id = serializers.PrimaryKeyRelatedField(
        queryset=Outline.objects.all(), write_only=True, source="stage"
    )


class LearningTrackAddCoursesSerializer(serializers.Serializer):
    course_ids = serializers.ListField(
        child=serializers.PrimaryKeyRelatedField(
            queryset=Resource.objects.filter(type=Resource.COURSE),
        ),
        allow_empty=False,
        write_only=True,
        source="courses",
    )

    def validate(self, attrs):
        learning_track_id = self.context["learning_track_id"]
        stage_id = self.context["stage_id"]
        courses = attrs["courses"]

        # Проверка, что стадия принадлежит learning track'у
        if not Outline.objects.filter(
            learning_track_id=learning_track_id, id=stage_id
        ).exists():
            raise serializers.ValidationError(
                {"stage_id": "Стадия не найдена в рамках данного learning track."}
            )

        # Проверка: есть ли такие курсы уже в данной стадии
        existing_course_ids = set(
            Outline.objects.filter(
                learning_track_id=learning_track_id,
                parent_id=stage_id,
                resource__in=courses,
            ).values_list("resource_id", flat=True)
        )

        if existing_course_ids:
            duplicate_ids = [
                course.id for course in courses if course.id in existing_course_ids
            ]
            raise serializers.ValidationError(
                {
                    "course_ids": f"Следующие курсы уже добавлены в эту стадию: {duplicate_ids}"
                }
            )

        return attrs

    @transaction.atomic
    def create(self, validated_data):
        learning_track_id = self.context["learning_track_id"]
        stage_id = self.context["stage_id"]
        courses = validated_data.get("courses")

        max_order = (
            Outline.objects.filter(
                learning_track_id=learning_track_id,
                parent_id=stage_id,
            ).aggregate(models.Max("order"))["order__max"]
            or 0
        )

        created_outlines = []
        for index, course in enumerate(courses, start=1):
            outline = Outline.objects.create(
                parent_id=stage_id,
                type=Outline.TYPE_COURSE,
                learning_track_id=learning_track_id,
                resource=course,
                order=max_order + index,
            )

            OutlineAccessTime.objects.create(
                outline=outline,
                type=OutlineAccessTime.TYPE_UNLIMITED,
            )
            created_outlines.append(outline)

        return created_outlines


class LearningTrackUpdateCourseAvailabilitySerializer(serializers.Serializer):
    course_ids = serializers.ListField(
        child=serializers.PrimaryKeyRelatedField(
            queryset=Resource.objects.filter(type=Resource.COURSE),
            help_text="ID курса (Resource.id) с type=COURSE",
        ),
        allow_empty=False,
        write_only=True,
        source="courses",
        help_text="Список ID курсов, для которых нужно обновить доступность",
    )

    type = serializers.ChoiceField(
        choices=OutlineAccessTime.TYPE_CHOICES,
        required=False,
        help_text="Тип доступа: UNLIMITED или RELATIVE",
    )
    interval = serializers.IntegerField(
        required=False, help_text="Интервал начала доступа (используется при RELATIVE)"
    )
    period = serializers.IntegerField(
        required=False, help_text="Длительность доступа (например, 5 недель)"
    )
    period_unit = serializers.ChoiceField(
        choices=OutlineAccessTime.PERIOD_UNIT_CHOICES,
        required=False,
        help_text="Единица периода доступа: DAYS, WEEKS, MONTHS, YEARS",
    )
    lock_after_expiration = serializers.BooleanField(
        required=False,
        help_text="Блокировать ли доступ после окончания периода (true/false)",
    )

    def validate(self, attrs):
        learning_track_id = self.context["learning_track_id"]
        stage_id = self.context["stage_id"]
        courses = attrs["courses"]

        # Найдём Outline'ы, соответствующие условиям
        outlines = Outline.objects.filter(
            learning_track_id=learning_track_id,
            parent_id=stage_id,
            type=Outline.TYPE_COURSE,
            resource__in=courses,
        )

        # Проверим, что нашли все переданные курсы
        if outlines.count() != len(courses):
            found_ids = set(outlines.values_list("resource_id", flat=True))
            missing_ids = [
                course.id for course in courses if course.id not in found_ids
            ]
            raise serializers.ValidationError(
                {
                    "course_ids": f"Следующие курсы не найдены в указанной стадии и learning track: {missing_ids}"
                }
            )

        attrs["outlines"] = outlines
        return attrs

    def update_availability(self):
        fields_to_update = [
            "type",
            "interval",
            "period",
            "period_unit",
            "lock_after_expiration",
        ]
        updates = {
            field: self.validated_data[field]
            for field in fields_to_update
            if field in self.validated_data
        }
        updated_ids = []

        for outline in self.validated_data["outlines"]:
            access_time = getattr(outline, "access_time", None)
            if not access_time:
                continue  # или создать, если нужно
            for field, value in updates.items():
                setattr(access_time, field, value)
            access_time.save()
            updated_ids.append(outline.resource_id)

        return updated_ids