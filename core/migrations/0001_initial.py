# Generated by Django 5.1.2 on 2025-07-30 12:59

import core.models
import django.core.validators
import lms.s3_storage
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Media',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('path', models.FileField(storage=lms.s3_storage.PublicMediaStorage(), upload_to=core.models.upload_to, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'gif', 'pdf', 'xlsx', 'xls', 'csv', 'pptx', 'doc', 'docx', 'mp4', 'mp3', 'avi', 'mov', 'zip', 'tar', 'rar'])])),
                ('size', models.PositiveIntegerField(blank=True, editable=False, help_text='file size in bytes', null=True)),
                ('content_type', models.CharField(blank=True, editable=False, help_text='content type', max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'media',
                'verbose_name_plural': 'medias',
                'db_table': 'core_medias',
                'ordering': ['-created_at'],
            },
        ),
    ]
