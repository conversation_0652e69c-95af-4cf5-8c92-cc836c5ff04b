import mimetypes

from rest_framework import serializers

from core.models import Media


class MediaSerializer(serializers.ModelSerializer):
    relative_path = serializers.SerializerMethodField()

    class Meta:
        model = Media
        fields = ('id', 'path', 'size', 'content_type', 'relative_path')

    def get_relative_path(self, obj):
        if obj.path:
            return obj.path.name
        return None

    def create(self, validated_data):
        obj = super().create(validated_data)
        self.update_media_fields(obj)
        return obj

    def update(self, instance, validated_data):
        obj = super().update(instance, validated_data)
        if 'path' in validated_data:
            self.update_media_fields(obj)
        return obj

    @staticmethod
    def update_media_fields(obj):
        if obj.path:
            content_type, _ = mimetypes.guess_type(obj.path.name)
            obj.size = obj.path.size
            obj.content_type = content_type
            obj.save(update_fields=['size', 'content_type'])
