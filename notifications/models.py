from django.db import models
from django.utils.translation import gettext_lazy as _
from django_ckeditor_5.fields import C<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import <PERSON>ric<PERSON><PERSON><PERSON><PERSON><PERSON>

from core.models import BaseUUIDModel
from users.models import User


class ChannelType(models.TextChoices):
    EMAIL     = 'EMAIL',     _('Email')
    SMS       = 'SMS',       _('SMS')
    MESSENGER = 'MESSENGER', _('Messenger')
    PUSH      = 'PUSH',      _('Push Notification')


class NotificationStatus(models.TextChoices):
    PENDING = 'PENDING', _('Pending')
    SENT    = 'SENT',    _('Sent')
    FAILED  = 'FAILED',  _('Failed')


class Notification(BaseUUIDModel):
    channel = models.CharField(
        max_length=50,
        choices=ChannelType.choices,
        verbose_name=_('Channel'),
    )
    status = models.CharField(
        max_length=50,
        choices=NotificationStatus.choices,
        default=NotificationStatus.PENDING,
        verbose_name=_('Status'),
    )
    recipient = models.TextField(
        verbose_name=_('Recipient'),
    )
    subject = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name=_('Subject'),
    )
    message = models.TextField(
        verbose_name=_('Message'),
    )
    payload = models.JSONField(verbose_name=_('payload'), default=dict, blank=True)
    scheduled_time = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Scheduled time'),
    )
    sent_time = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Sent time'),
    )
    error_message = models.TextField(
        null=True,
        blank=True,
        verbose_name=_('Error message'),
    )
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, verbose_name=_('content_type'), null=True,
                                   blank=True)
    object_id = models.PositiveIntegerField(verbose_name=_('object_id'), null=True, blank=True)
    related_object = GenericForeignKey('content_type', 'object_id')

    def __str__(self):
        return f"[{self.channel.upper()}] {self.subject or self.message[:30]}"

    class Meta:
        verbose_name = _('notification')
        verbose_name_plural = _('notifications')
        ordering = ['id', ]
        db_table = 'notifications'


class NotificationTemplate(BaseUUIDModel):
    label = models.CharField(verbose_name=_('label'), max_length=255)
    channel = models.CharField(verbose_name=_('Channel'), choices=ChannelType.choices, max_length=50)
    title = models.CharField(verbose_name=_('title'), max_length=255, blank=True, null=True)
    code = models.CharField(verbose_name=_('code'), max_length=255)
    content = CKEditor5Field(verbose_name=_('Content'), config_name="extends", null=True, blank=True)
    is_active = models.BooleanField(verbose_name=_('is active'), default=True)
    order = models.IntegerField(verbose_name=_('order'), default=1, blank=True, null=True)

    def __str__(self):
        return f"{self.label} ({self.channel})"

    class Meta:
        verbose_name = _('notification template')
        verbose_name_plural = _('notification templates')
        ordering = ['order']
        db_table = 'notification_templates'


class NotificationReceiver(BaseUUIDModel):
    user = models.ForeignKey(
        to=User,
        verbose_name=_('user'),
        on_delete=models.RESTRICT,
        related_name='user_receivers',
    )
    channel = models.CharField(verbose_name=_('Channel'), choices=ChannelType.choices, max_length=50)
    recipient = models.TextField(
        verbose_name=_('Recipient'),
    )
    code = models.CharField(verbose_name=_('code'), max_length=255)
    is_active = models.BooleanField(verbose_name=_('is active'), default=True)
    order = models.IntegerField(verbose_name=_('order'), default=1, blank=True, null=True)

    def __str__(self):
        return f"[{self.channel}] {self.recipient}"

    class Meta:
        verbose_name = _('notification receiver')
        verbose_name_plural = _('notification receivers')
        ordering = ['order']
        db_table = 'notification_receivers'


class UINotification(BaseUUIDModel):
    user = models.ForeignKey(
        to=User,
        verbose_name=_('user'),
        on_delete=models.RESTRICT,
        related_name='user_ui_notifications',
    )
    subject = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name=_('Subject'),
    )
    message = models.TextField(
        verbose_name=_('Message'),
    )
    payload = models.JSONField(verbose_name=_('payload'), default=dict, blank=True)
    is_read = models.BooleanField(verbose_name=_('is read'), default=False)
    read_at = models.DateTimeField(verbose_name=_('read at'), null=True, blank=True)

    def __str__(self):
        return f"{self.user}: {self.subject[:40]}"

    class Meta:
        verbose_name = _('ui notification')
        verbose_name_plural = _('ui notifications')
        ordering = ['-created_at']
        db_table = 'notification_ui_notifications'
