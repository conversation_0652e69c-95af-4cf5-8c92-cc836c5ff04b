from rest_framework.exceptions import PermissionDenied
from rest_framework.permissions import BasePermission
from rest_framework.request import Request
from rest_framework.views import View

from reviews.models import Comment


class IsCommentAuthor(BasePermission):
    def has_object_permission(self, request: Request, view: View, obj: Comment):
        if obj.user == request.user:
            return True
        raise PermissionDenied("Вы не автор этого комментария")
