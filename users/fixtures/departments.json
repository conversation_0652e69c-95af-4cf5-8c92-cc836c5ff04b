[{"model": "users.department", "pk": "be99d193-d8fd-44be-8366-a2e23c62ab14", "fields": {"name": "Kaspi Group", "code": "kaspi-group", "parent_id": null, "created_at": "2025-01-23 12:34:07.773983+00", "updated_at": "2025-01-23 12:34:07.773983+00"}}, {"model": "users.department", "pk": "f899d5e8-66c9-45db-95e5-1177a53daca4", "fields": {"name": "Kaspi University", "code": "kaspi-university", "parent_id": "be99d193-d8fd-44be-8366-a2e23c62ab14", "created_at": "2025-01-23 12:34:07.773983+00", "updated_at": "2025-01-23 12:34:07.773983+00"}}, {"model": "users.department", "pk": "56fd06eb-43de-4af8-bc3e-f96ff3c03ead", "fields": {"name": "Kaspi.kz", "code": "ka<PERSON><PERSON><PERSON>", "parent_id": "be99d193-d8fd-44be-8366-a2e23c62ab14", "created_at": "2025-01-23 12:34:07.773983+00", "updated_at": "2025-01-23 12:34:07.773983+00"}}, {"model": "users.department", "pk": "be32d874-76d5-495b-a83f-a1be04d35c78", "fields": {"name": "Отдел продаж", "code": "otdel-prodazh", "parent_id": "56fd06eb-43de-4af8-bc3e-f96ff3c03ead", "created_at": "2025-01-23 12:34:07.773983+00", "updated_at": "2025-01-23 12:34:07.773983+00"}}, {"model": "users.department", "pk": "d79ece88-66f8-4696-8d83-97cf81ce9c20", "fields": {"name": "Производственный отдел", "code": "proizvodstvennyj-otdel", "parent_id": "56fd06eb-43de-4af8-bc3e-f96ff3c03ead", "created_at": "2025-01-23 12:34:07.773983+00", "updated_at": "2025-01-23 12:34:07.773983+00"}}, {"model": "users.department", "pk": "d53f3b2e-ffef-4fa7-a7a7-bed0a61ab05b", "fields": {"name": "<PERSON><PERSON><PERSON>", "code": "kolesa-avto", "parent_id": "be99d193-d8fd-44be-8366-a2e23c62ab14", "created_at": "2025-01-23 12:34:07.773983+00", "updated_at": "2025-01-23 12:34:07.773983+00"}}, {"model": "users.department", "pk": "f2d2c942-4beb-4500-91d0-0085cfafde96", "fields": {"name": "Magnum GO", "code": "magnum-go", "parent_id": "be99d193-d8fd-44be-8366-a2e23c62ab14", "created_at": "2025-01-23 12:34:07.773983+00", "updated_at": "2025-01-23 12:34:07.773983+00"}}, {"model": "users.department", "pk": "617c92b4-053f-4abf-bcd4-3da8eefd25cb", "fields": {"name": "Парт<PERSON><PERSON><PERSON><PERSON> Kaspi Pay", "code": "partnyory-kaspi-pay", "parent_id": "be99d193-d8fd-44be-8366-a2e23c62ab14", "created_at": "2025-01-23 12:34:07.773983+00", "updated_at": "2025-01-23 12:34:07.773983+00"}}]