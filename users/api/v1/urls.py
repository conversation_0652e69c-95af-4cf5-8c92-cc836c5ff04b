from django.urls import include, path
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework_simplejwt.views import TokenRefreshView, TokenVerifyView

from users.api.v1.views.department_views import (
    DepartmentTreeView,
    DepartmentUsersView,
    DepartmentViewSet,
    UsersMoveToDepartment,
)
from users.api.v1.views.group_views import (
    GroupUsersView,
    GroupViewSet,
    ListUserGroupsView,
    UpdateUserGroupsView,
    UserTerminateView,
)
from users.api.v1.views.me_views import (
    ChangePasswordView,
    MeDetailedView,
    MeView,
    UserPhotoUpdateView,
)
from users.api.v1.views.permission_views import RolePermissionsView, UserPermissionsView
from users.api.v1.views.role_views import (
    ListUserRolesView,
    RoleViewSet,
    UpdateUserRolesView,
)
from users.api.v1.views.user_views import (
    UserAutocompleteView,
    <PERSON>r<PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ie<PERSON>,
    <PERSON>r<PERSON>ist<PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>rieveVie<PERSON>,
    UserUpdateView,
)
from users.api.v1.views.shared_views import (
    ContentTypeView,
    PatchLogoutView,
    TokenObtainPairView,
    run_tasks,
)

router = DefaultRouter()
router.register(r"roles", RoleViewSet)
router.register(r"departments", DepartmentViewSet)
router.register(r"groups", GroupViewSet)

urlpatterns = [
    # Authorization
    path("auth/", include("rest_framework.urls")),
    path("auth/logout/", PatchLogoutView.as_view()),
    # Token
    path("jwt/token/", TokenObtainPairView.as_view(), name="token_obtain_pair"),
    path("jwt/token/refresh/", TokenRefreshView.as_view(), name="to ken_refresh"),
    path("jwt/token/verify/", TokenVerifyView.as_view(), name="token_verify"),
    # Me
    path("me/", MeView.as_view(), name="me"),
    path("me/detailed/", MeDetailedView.as_view(), name="me-detailed"),
    path("users/<uuid:id>/change-password/", ChangePasswordView.as_view()),
    path("users/<uuid:id>/change-photo/", UserPhotoUpdateView.as_view()),
    # Users
    path("users/", UserListView.as_view()),
    path("users/create/", UserCreateView.as_view()),
    path("users/<uuid:pk>/", UserRetrieveView.as_view()),
    path("users/<uuid:pk>/update/", UserUpdateView.as_view()),
    path("users/<uuid:pk>/destroy/", UserDestroyView.as_view()),
    path("users/autocomplete/", UserAutocompleteView.as_view()),
    # Groups
    path("users/<uuid:pk>/groups/", ListUserGroupsView.as_view()),
    path("users/<uuid:pk>/groups/update/", UpdateUserGroupsView.as_view()),
    path("groups/<uuid:pk>/users/", GroupUsersView.as_view()),
    path("users/terminate/", UserTerminateView.as_view()),
    # Departments
    path("departments/tree/", DepartmentTreeView.as_view()),
    path("departments/<uuid:pk>/users/", DepartmentUsersView.as_view()),
    path("users/move-to-department/", UsersMoveToDepartment.as_view()),
    # Roles
    path("users/<uuid:pk>/roles/", ListUserRolesView.as_view()),
    path("users/<uuid:pk>/roles/update/", UpdateUserRolesView.as_view()),
    # Permissions
    path("users/<uuid:pk>/permissions/", UserPermissionsView.as_view()),
    path("roles/permissions/", RolePermissionsView.as_view()),
    # Miscellaneous
    path("content-types/", ContentTypeView.as_view()),
    path("", include(router.urls)),
    path("tasks/", run_tasks),
]
