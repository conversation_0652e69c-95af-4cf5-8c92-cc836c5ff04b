import logging

from django.db import transaction
from django.db.models import Exists, OuterRef, QuerySet

from users.models import (
    DepartmentPosition,
    DepartmentSupervisor,
    UserCompany,
)

logger = logging.getLogger("app")


class ImportUserIsBossService:
    """
    Сервис для получения списка руководяющих позиций и создание пользователей с такими же позициями в LMS
    """

    def run(self) -> None:
        """
        Основной командный пункт для запуска сервиса. Проверяет:
        - Если есть руководяющие позиции -> создает пользователей с руководяющими позициями
        - Если не найдено руководящих позиций -> ничего не делает
        """
        supervisors: QuerySet[UserCompany] = self._get_supervisors()
        if not supervisors:
            logger.info(
                f"[ImportUserIsBossService]: Emtpy QuerySet -> Supervisors = {supervisors}"
            )
            return None
        else:
            with transaction.atomic():
                self._create_supervisors(supervisors)

    def _get_supervisors(self) -> QuerySet[UserCompany]:
        """
        Получение списка пользователей с руководящими позициями
        - Исключает уже существующих пользователей, чтобы исключить дублирование
        - Проверяет существует такой позиции в департаменте
        """
        return (
            UserCompany.objects.select_related(
                "position", "department", "user", "company"
            )
            .filter(position__is_boss=True)
            .annotate(
                already_exists=Exists(
                    DepartmentSupervisor.objects.filter(
                        supervisor=OuterRef("user"),
                        department=OuterRef("department"),
                        type=DepartmentSupervisor.TYPE_LEAD,
                    )
                ),
                position_exists=Exists(
                    DepartmentPosition.objects.filter(
                        company=OuterRef("company"),
                        department=OuterRef("department"),
                        position=OuterRef("position"),
                    )
                ),
            )
            .filter(
                already_exists=False,
                position_exists=True,
            )
        )

    def _create_supervisors(self, supervisors: QuerySet[UserCompany]) -> None:
        """
        Создание руководителей и связанных департаментов
        """
        department_supervisors_for_bulk_create: list[DepartmentSupervisor] = (
            self._prepare_department_supervisors(supervisors)
        )

        created_department_supervisors = DepartmentSupervisor.objects.bulk_create(
            department_supervisors_for_bulk_create, ignore_conflicts=True
        )

        logger.info(
            f"[ImportUserIsBossService]: создано \
            \nDepartmentSupervisor: {len(created_department_supervisors)}"
        )

    def _prepare_department_supervisors(
        self, supervisors: QuerySet[UserCompany]
    ) -> list[DepartmentSupervisor]:
        return [
            DepartmentSupervisor(
                supervisor=supervisor.user,
                department=supervisor.department,
                type=DepartmentSupervisor.TYPE_LEAD,
            )
            for supervisor in supervisors
        ]
