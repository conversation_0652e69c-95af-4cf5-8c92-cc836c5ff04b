from django.utils.translation import gettext_lazy as _
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import viewsets, mixins, parsers

from core.api.v1.filters import MediaFilter
from core.api.v1.serializers import MediaSerializer
from core.models import Media
from lms.utils import get_paginated_serializer


class MediaViewSet(mixins.ListModelMixin,
                   mixins.RetrieveModelMixin,
                   mixins.DestroyModelMixin,
                   mixins.CreateModelMixin,
                   mixins.UpdateModelMixin,
                   viewsets.GenericViewSet):
    queryset = Media.objects.all()
    serializer_class = MediaSerializer
    parser_classes = [parsers.MultiPartParser, parsers.FormParser]
    filter_backends = [DjangoFilterBackend]
    filterset_class = MediaFilter

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                'path',
                openapi.IN_FORM,
                description=_('upload the file'),
                type=openapi.TYPE_FILE,
                required=True,
            )
        ],
        responses={201: MediaSerializer()},
        consumes=["multipart/form-data"],
    )
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                'path',
                openapi.IN_FORM,
                description='Upload new file (optional)',
                type=openapi.TYPE_FILE,
                required=False,
            )
        ],
        responses={200: MediaSerializer()},
        consumes=["multipart/form-data"],
    )
    def update(self, request, *args, **kwargs):
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(
        responses={
            200: get_paginated_serializer(serializer_class),
        }
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)