from django.db.models import Prefetch
from users.models import Department, DepartmentSupervisor


def prefetch_department_children(depth=5):
    if depth <= 0:
        return Prefetch("children")

    return Prefetch(
        "children",
        queryset=Department.objects.select_related("parent").prefetch_related(
            Prefetch(
                "supervisors_in_department",
                queryset=DepartmentSupervisor.objects.select_related(
                    "supervisor"
                ),
            ),
            prefetch_department_children(depth - 1),
        ),
    )
