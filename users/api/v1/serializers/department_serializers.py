from django.db import transaction
from rest_framework import serializers

from users.models import Company, Department, DepartmentSupervisor, Position, User


class PositionManagementSerializer(serializers.ModelSerializer):
    class Meta:
        model = Position
        fields = (
            "id",
            "company_id",
            "ID1C",
            "name",
            "is_boss",
        )
        read_only_fields = ("id", "ID1C")


class DepartmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Department
        fields = (
            "id",
            "name",
            "parent",
            "code",
        )


class DepartmentManagementSerializer(serializers.ModelSerializer):
    parent = DepartmentSerializer()

    class Meta:
        model = Department
        fields = (
            "id",
            "name",
            "parent",
            "code",
        )


class DepartmentTreeSerializer(serializers.ModelSerializer):
    children = serializers.SerializerMethodField()

    class Meta:
        model = Department
        fields = ("id", "name", "code", "parent", "children")

    def get_children(self, obj):
        depth = self.context.get("depth", 8)
        if depth <= 1:
            return []

        children = obj.children.all()
        context = self.context.copy()
        context["depth"] = depth - 1

        return DepartmentTreeSerializer(children, many=True, context=context).data


class CompanySerializer(serializers.ModelSerializer):
    class Meta:
        model = Company
        fields = ("id", "name", "code")


class UsersMoveToSerializer(serializers.Serializer):
    user_ids = serializers.PrimaryKeyRelatedField(
        queryset=User.objects.all(), many=True
    )
    department_id = serializers.PrimaryKeyRelatedField(
        queryset=Department.objects.all()
    )

    def create(self, validated_data):
        with transaction.atomic():
            user_ids = [user.id for user in validated_data["user_ids"]]
            department_id = validated_data["department_id"]
            User.objects.filter(id__in=user_ids).update(department=department_id)
        return validated_data


class SupervisorSerializer(serializers.ModelSerializer):
    name = serializers.SerializerMethodField()

    @staticmethod
    def get_name(obj):
        return obj.supervisor.get_full_name()

    class Meta:
        model = DepartmentSupervisor
        fields = ("id", "name", "type")


class DepartmentChildrenSerializer(serializers.ModelSerializer):
    children = serializers.SerializerMethodField()
    supervisors = SupervisorSerializer(many=True, source="supervisors_in_department")
    users_count = serializers.IntegerField(
        read_only=True
    )  # Добавил явно, чтобы было понятно

    def get_children(self, obj):
        depth = self.context.get("depth", 5)
        if depth > 0:
            children = obj.children.all()
            return DepartmentChildrenSerializer(
                children, many=True, context={"depth": depth - 1}
            ).data
        return []

    class Meta:
        model = Department
        fields = (
            "id",
            "name",
            "code",
            "parent",
            "children",
            "supervisors",
            "users_count",
        )


class DepartmentRetrieveSerializer(serializers.ModelSerializer):
    supervisors = SupervisorSerializer(many=True, source="supervisors_in_department")

    class Meta:
        model = Department
        fields = ("id", "name", "code", "supervisors")


class DepartmentCreateUpdateSupervisorSerializer(serializers.ModelSerializer):
    class Meta:
        model = DepartmentSupervisor
        fields = ("supervisor", "type")


class CreateUpdateDepartmentSerializer(serializers.ModelSerializer):
    supervisors = DepartmentCreateUpdateSupervisorSerializer(
        many=True, required=False, write_only=True
    )

    class Meta:
        model = Department
        fields = ("name", "code", "parent", "supervisors")

    def create(self, validated_data):
        supervisors_data = validated_data.pop("supervisors", [])
        department = Department.objects.create(**validated_data)
        for supervisor_data in supervisors_data:
            supervisor = supervisor_data["supervisor"]
            supervisor_type = supervisor_data["type"]
            DepartmentSupervisor.objects.create(
                department_id=department.pk,
                supervisor_id=supervisor.pk,
                type=supervisor_type,
            )

        return department

    def update(self, instance, validated_data):
        supervisors_data = validated_data.pop("supervisors", [])

        instance.name = validated_data.get("name", instance.name)
        instance.code = validated_data.get("code", instance.code)
        instance.parent = validated_data.get("parent", instance.parent)
        instance.save()

        instance.supervisors.clear()
        for supervisor_data in supervisors_data:
            supervisor = supervisor_data["supervisor"]
            supervisor_type = supervisor_data["type"]

            DepartmentSupervisor.objects.create(
                department_id=instance.pk,
                supervisor_id=supervisor.pk,
                type=supervisor_type,
            )

        return instance
