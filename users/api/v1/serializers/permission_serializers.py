from rest_framework import serializers

from users.models import Permission, Role


class PermissionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Permission

        fields = ("id", "name", "code", "description")


class RolePermissionsSerializer(serializers.ModelSerializer):
    is_deletable = serializers.BooleanField(read_only=True)
    permissions = PermissionSerializer(many=True)

    class Meta:
        model = Role
        fields = ("id", "name", "code", "description", "is_deletable", "permissions")


class PermissionRecursiveSerializer(serializers.ModelSerializer):
    children = serializers.SerializerMethodField()

    def get_children(self, obj):
        depth = self.context.get("depth", 3)
        if depth > 0:
            children = obj.children.all()
            return PermissionRecursiveSerializer(
                children, many=True, context={"depth": depth - 1}
            ).data
        return []

    class Meta:
        model = Permission
        fields = ("id", "name", "code", "description", "parent", "children")
