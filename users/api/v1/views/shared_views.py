import logging

from django.contrib.auth.views import <PERSON>goutView
from django.db.models import Prefetch
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import generics
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework_simplejwt.views import (
    TokenObtainPairView as BaseTokenObtainPairView,
)

from users import tasks
from users.api.v1.serializers.shared_serializers import ContentTypeSerializer
from users.models import ContentType, Permission
from users.tasks.import_tasks import (
    import_companies_task,
    import_department_positions_task,
    import_departments_task,
    import_positions_task,
    import_user_is_boss,
    import_users_task,
)

logger = logging.getLogger("app")

MAX_DEPTH = 5

token_obtain_response = openapi.Schema(
    title="TokenObtainPair",
    type=openapi.TYPE_OBJECT,
    properties={
        "refresh": openapi.Schema(
            type=openapi.TYPE_STRING, description="Refresh token"
        ),
        "access": openapi.Schema(type=openapi.TYPE_STRING, description="Access token"),
    },
)


class TokenObtainPairView(BaseTokenObtainPairView):
    @swagger_auto_schema(
        operation_description="Получение пары JWT-токенов",
        request_body=TokenObtainPairSerializer,
        responses={200: token_obtain_response},
    )
    def post(self, request, *args, **kwargs):
        return super().post(request, *args, **kwargs)


class PatchLogoutView(LogoutView):
    http_method_names = ["get", "post", "options"]

    def get(self, request, *args, **kwargs):
        return self.post(request, *args, **kwargs)


class ContentTypeView(generics.ListAPIView):
    serializer_class = ContentTypeSerializer
    pagination_class = None

    def get_queryset(self):
        # Предварительно фильтруем разрешения, где parent__isnull=True
        top_level_permissions_prefetch = Prefetch(
            "permissions",  # Связь с моделью Permission
            queryset=Permission.objects.filter(parent__isnull=True).prefetch_related(
                "children"
            ),
        )
        return ContentType.objects.prefetch_related(top_level_permissions_prefetch)


@api_view(["GET"])
def run_tasks(request):
    task = request.GET.get("task", None)
    if task == "users_schedule":
        tasks.users_schedule()
    elif task == "company":
        import_companies_task.delay(dry_run=False)
    elif task == "department":
        import_departments_task.delay(dry_run=False)
    elif task == "position":
        import_positions_task.delay(dry_run=False)
    elif task == "department_position":
        import_department_positions_task.delay(dry_run=False)
    elif task == "users":
        import_users_task.delay(dry_run=False)
    elif task == "import_user_is_boss":
        import_user_is_boss.delay()
    return Response()
