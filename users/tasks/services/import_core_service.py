from collections import defaultdict
import re
from unidecode import unidecode
from django.db import transaction
from django.db.models import Q
from django.conf import settings

from users.models import (
    User, Company, Department, Position,
    DepartmentPosition, UserCompany, Role
)
from ..base import BaseImportTask


class CompanyImportService(BaseImportTask):
    """Сервис импорта компаний"""

    def import_companies(self):
        self.log_start("IMPORT-COMPANIES")

        try:
            data = self.make_api_request('/companies')
        except Exception as e:
            self.logger.exception("[IMPORT-COMPANIES] ❌ Ошибка при запросе: %s", str(e))
            raise e

        if not data:
            self.logger.info("[IMPORT-COMPANIES] ✅ Нет данных для импорта. Завершение.")
            return {"success": True}

        try:
            with transaction.atomic():
                for company_data in data:
                    obj, was_created = Company.objects.update_or_create(
                        code=company_data['code'],
                        defaults={'name': company_data['label']}
                    )
                    if was_created:
                        self.stats['created'] += 1
                    else:
                        self.stats['updated'] += 1

                if self.dry_run:
                    self.logger.warning("[IMPORT-COMPANIES] 🧪 dry_run: откат транзакции")
                    transaction.set_rollback(True)

        except Exception as e:
            self.logger.exception("[IMPORT-COMPANIES] ❌ Ошибка при записи в БД: %s", str(e))
            raise

        self.log_results("IMPORT-COMPANIES")
        return {"success": True}


class DepartmentImportService(BaseImportTask):
    """Сервис импорта департаментов"""

    def import_departments(self):
        self.log_start("IMPORT-DEPARTMENTS")

        try:
            data = self.make_api_request('/departments/tree')
        except Exception as e:
            self.logger.exception(f"[IMPORT-DEPARTMENTS] Ошибка при запросе API: {str(e)}")
            raise e

        existing = {d.code: d for d in Department.objects.select_related('parent')}
        companies = {c.code: c for c in Company.objects.filter(is_active=True)}
        seen_codes = set()

        def import_recursive(tree, parent=None, level=0):
            for node in tree:
                code = node['code']
                name = node['label']
                seen_codes.add(code)

                company_code = (node.get('company') or {}).get('code')
                company = companies.get(company_code)

                if not company:
                    self.logger.warning(
                        "[IMPORT-DEPARTMENTS] ❌ DEP SKIPPED (lvl=%d): code=%s name=%s company_code=%s",
                        level, code, name, company_code
                    )
                    continue

                if code in existing:
                    dept = existing[code]
                    has_changes = (
                            dept.name != name or
                            dept.parent_id != (parent.id if parent else None) or
                            (dept.company and dept.company.code != company_code)
                    )
                    if has_changes:
                        dept.name = name
                        dept.parent = parent
                        dept.company = company
                        if not self.dry_run:
                            dept.save()
                        self.stats['updated'] += 1
                else:
                    dept = Department(
                        code=code,
                        name=name,
                        parent=parent,
                        company=company,
                        ID1C=node.get('ID1C'),
                        IDParent1C=node.get('IDParent1C')
                    )
                    if not self.dry_run:
                        dept.save()
                    self.stats['created'] += 1
                    existing[code] = dept

                import_recursive(node.get('children', []), parent=dept, level=level + 1)

        with transaction.atomic():
            import_recursive(data)
            if self.dry_run:
                transaction.set_rollback(True)

        self.log_results("IMPORT-DEPARTMENTS")
        return {'success': True}


class PositionImportService(BaseImportTask):
    """Сервис импорта позиций"""

    def import_positions(self):
        self.log_start("IMPORT-POSITIONS")

        page = 1
        to_create, to_update = [], []

        while True:
            try:
                data = self.make_api_request(
                    '/positions',
                    params={'page': page, 'page_size': settings.BATCH_SIZE}
                )
            except Exception as e:
                self.logger.exception("[IMPORT-POSITIONS] ❌ Ошибка при запросе страницы %d: %s", page, str(e))
                raise e

            results = data.get('results') or []
            if not results:
                self.logger.info("[IMPORT-POSITIONS] ✅ Нет данных на странице %d. Завершение загрузки.", page)
                break

            ids = [str(item['ID1C']) for item in results]
            company_codes = set((item.get('company') or {}).get('code') for item in results if item.get('company'))

            # Предзагрузка компаний
            existing_companies = {
                c.code: c for c in Company.objects.filter(code__in=company_codes, is_active=True)
            }

            # Предзагрузка позиций по (ID1C, company_code)
            existing = {
                (str(p.ID1C), p.company.code if p.company else None): p
                for p in Position.objects.filter(
                    ID1C__in=ids,
                    company__code__in=company_codes
                ).select_related("company")
            }

            for item in results:
                ID1C = str(item['ID1C'])
                name = item['label']
                is_boss = item['boss']
                company_code = (item.get('company') or {}).get('code')

                if not company_code:
                    self.logger.warning("[IMPORT-POSITIONS] ⚠️ Пропущена позиция без company: %s", item)
                    continue

                company = existing_companies.get(company_code)
                if not company:
                    self.logger.warning("[IMPORT-POSITIONS] ⚠️ Компания %s не найдена. Позиция ID1C=%s пропущена.",
                                        company_code, ID1C)
                    continue

                key = (ID1C, company_code)
                if key in existing:
                    pos = existing[key]
                    if pos.name != name or pos.is_boss != is_boss:
                        pos.name = name
                        pos.is_boss = is_boss
                        to_update.append(pos)
                else:
                    to_create.append(Position(ID1C=ID1C, name=name, is_boss=is_boss, company=company))

            if not data.get("next"):
                break

            page += 1

        self.logger.info("[IMPORT-POSITIONS] 💾 Подготовлено к записи: создать %d, обновить %d", len(to_create),
                         len(to_update))

        if self.dry_run:
            self.logger.warning("[IMPORT-POSITIONS] 🧪 dry-run: изменения НЕ будут сохранены")
            self.stats['created'] = len(to_create)
            self.stats['updated'] = len(to_update)
        else:
            try:
                with transaction.atomic():
                    if to_create:
                        Position.objects.bulk_create(to_create, batch_size=settings.BATCH_SIZE)
                        self.stats['created'] = len(to_create)
                        self.logger.info("[IMPORT-POSITIONS] ✅ Создано %d позиций", len(to_create))

                    if to_update:
                        Position.objects.bulk_update(
                            to_update,
                            fields=["name", "is_boss"],
                            batch_size=settings.BATCH_SIZE
                        )
                        self.stats['updated'] = len(to_update)
                        self.logger.info("[IMPORT-POSITIONS] ✅ Обновлено %d позиций", len(to_update))
            except Exception as e:
                self.logger.exception("[IMPORT-POSITIONS] ❌ Ошибка при сохранении данных в БД: %s", str(e))
                raise

        self.log_results("IMPORT-POSITIONS")
        return {"success": True}


class DepartmentPositionImportService(BaseImportTask):
    """Сервис импорта связей департамент-позиция"""

    def import_department_positions(self):
        self.log_start("IMPORT-DP")

        page = 1
        total_created = 0
        total_skipped = 0

        while True:
            try:
                data = self.make_api_request(
                    '/department-positions',
                    params={'page': page, 'page_size': settings.BATCH_SIZE}
                )
            except Exception as e:
                self.logger.exception("[IMPORT-DP] ❌ Ошибка при запросе страницы %d: %s", page, str(e))
                raise e

            items = data.get('results') or []
            if not items:
                self.logger.info("[IMPORT-DP] ✅ Последняя страница достигнута. Завершение.")
                break

            # Собираем уникальные коды
            dep_codes = {
                (item.get('department') or {}).get('code')
                for item in items if item.get('department') and item['department'].get('code')
            }
            pos_ids = {
                str((item.get('position') or {}).get('ID1C'))
                for item in items if item.get('position') and item['position'].get('ID1C')
            }
            company_codes = {
                (item.get('company') or {}).get('code')
                for item in items if item.get('company') and item['company'].get('code')
            }

            # Загружаем сущности из БД
            departments = {d.code: d for d in Department.objects.filter(code__in=dep_codes)}
            positions = {str(p.ID1C): p for p in Position.objects.filter(ID1C__in=pos_ids)}
            companies = {c.code: c for c in Company.objects.filter(code__in=company_codes)}

            to_create = []
            page_skipped = 0

            for item in items:
                dep_data = item.get('department') or {}
                pos_data = item.get('position') or {}
                comp_data = item.get('company') or {}

                dep_code = dep_data.get('code')
                pos_id1c = str(pos_data.get('ID1C'))
                comp_code = comp_data.get('code')

                department = departments.get(dep_code)
                position = positions.get(pos_id1c)
                company = companies.get(comp_code)

                if not department or not position or not company:
                    self.logger.warning(
                        "[IMPORT-DP] ⚠️ Пропуск: department=%s, position=%s, company=%s",
                        dep_code or '-', pos_id1c or '-', comp_code or '-'
                    )
                    page_skipped += 1
                    continue

                to_create.append(DepartmentPosition(
                    department=department,
                    position=position,
                    company=company
                ))

            self.logger.info("[IMPORT-DP] 📦 Страница %d: всего %d записей, к созданию %d, пропущено %d",
                             page, len(items), len(to_create), page_skipped)

            total_skipped += page_skipped

            if self.dry_run:
                self.logger.warning("[IMPORT-DP] 🧪 dry-run: изменения НЕ будут сохранены на стр. %d", page)
                total_created += len(to_create)
            else:
                try:
                    with transaction.atomic():
                        if to_create:
                            DepartmentPosition.objects.bulk_create(
                                to_create,
                                batch_size=settings.BATCH_SIZE,
                                ignore_conflicts=True
                            )
                            total_created += len(to_create)
                            self.logger.info("[IMPORT-DP] ✅ Попыток создания: %d (дубликаты проигнорированы базой)",
                                             len(to_create))
                except Exception as e:
                    self.logger.exception("[IMPORT-DP] ❌ Ошибка при сохранении на стр. %d: %s", page, str(e))
                    raise

            if not data.get("next"):
                break
            page += 1

        self.stats['created'] = total_created
        self.stats['skipped'] = total_skipped

        self.log_results("IMPORT-DP")
        return {
            'success': True,
            'created_attempts': total_created,
            'skipped_missing': total_skipped
        }


class UserImportService(BaseImportTask):
    """Сервис импорта пользователей"""

    def __init__(self, dry_run=False):
        super().__init__(dry_run)
        self.existing_usernames = set()
        self.used_usernames = set()
        self.companies = {}
        self.departments = {}
        self.positions_by_id = {}
        self.positions_by_label = {}
        self.associations = defaultdict(list)

    def import_users(self):
        self.log_start("IMPORT-USERS")

        # Предзагрузка справочников
        self._preload_data()

        to_create = []
        to_update = []
        created_keys = []
        created_usernames = []

        page = 1
        while True:
            try:
                data = self.make_api_request(
                    '/employees',
                    params={"page": page, "page_size": settings.BATCH_SIZE}
                )
            except Exception as e:
                self.logger.exception("❌ Ошибка загрузки страницы %d: %s", page, e)
                raise e

            results = data.get("results", [])
            if not results:
                break

            # Обработка страницы данных
            page_data = self._process_page(results)
            to_create.extend(page_data['to_create'])
            to_update.extend(page_data['to_update'])
            created_keys.extend(page_data['created_keys'])
            created_usernames.extend(page_data['created_usernames'])

            if not data.get("next"):
                break
            page += 1

        self.logger.info("📋 К записям: создать %d, обновить %d", len(to_create), len(to_update))

        if not self.dry_run:
            self._save_users(to_create, to_update, created_usernames)

        self.stats['created'] = len(to_create)
        self.stats['updated'] = len(to_update)

        self.log_results("IMPORT-USERS")
        return {"success": True}

    def _preload_data(self):
        """Предзагрузка справочников"""
        self.existing_usernames = set(User.objects.values_list('username', flat=True))
        self.used_usernames = set(self.existing_usernames)
        self.companies = {c.code: c for c in Company.objects.all()}
        self.departments = {d.code: d for d in Department.objects.all()}

        # Предзагрузка позиций по ID1C и по (company_code, name)
        for p in Position.objects.select_related('company').all():
            if p.ID1C:
                self.positions_by_id[str(p.ID1C)] = p
            self.positions_by_label[(p.company.code, p.name)] = p

    def _process_page(self, results):
        """Обработка одной страницы данных"""
        # Загружаем существующих пользователей по ld_account и iin
        page_usernames = {u.get("ld_account") for u in results if u.get("ld_account")}
        page_iins = {u.get("iin") for u in results if u.get("iin")}

        existing_qs = User.objects.filter(
            Q(username__in=page_usernames) | Q(iin__in=page_iins)
        ).only("id", "username", "iin", "first_name", "last_name", "email", "phone", "birth_date", "middle_name")

        existing = {u.username: u for u in existing_qs}
        for u in existing_qs:
            if u.iin:
                existing[u.iin] = u

        to_create = []
        to_update = []
        created_keys = []
        created_usernames = []

        for item in results:
            user_data = self._process_user_item(item, existing)
            if user_data:
                if user_data['action'] == 'create':
                    to_create.append(user_data['user'])
                    created_keys.append(user_data['key'])
                    created_usernames.append(user_data['username'])
                elif user_data['action'] == 'update':
                    to_update.append(user_data['user'])

        return {
            'to_create': to_create,
            'to_update': to_update,
            'created_keys': created_keys,
            'created_usernames': created_usernames
        }

    def _process_user_item(self, item, existing):
        """Обработка одного пользователя"""
        ld_account = item.get("ld_account")
        iin = (item.get("iin") or "").strip()
        key = ld_account or iin

        # Пропустить, если нет ни ld_account, ни iin
        if not key:
            self.add_error(f"❗ Пропуск: нет ни ld_account, ни iin (name={item.get('name')})")
            return None

        # Собираем поля пользователя
        first_name = item.get("first_name", "").strip()
        last_name = item.get("last_name", "").strip()
        middle_name = item.get("middle_name", "")
        email = (item.get("email") or "").strip()
        phone = (item.get("mobile_phone") or "").strip()
        birth_date = item.get("birthday") or None
        created_at = item.get("created_at") or None

        # Справочники: company, department
        comp_code = (item.get("company") or {}).get("code")
        company = self.companies.get(comp_code)
        if comp_code and not company:
            self.add_error(f"⚠ Не найдена компания: {comp_code} (user={key})")

        dept_code = (item.get("department") or {}).get("code")
        department = self.departments.get(dept_code)
        if dept_code and not department:
            self.add_error(f"⚠ Не найден департамент: {dept_code} (user={key})")

        # Позиция: сначала по ID1C, если нет — по label, если нет — создаём
        position = self._get_or_create_position(item, company, comp_code)

        # Генерация username, если нет ld_account
        username_to_use = self._generate_username(ld_account, first_name, last_name, iin)

        # Получаем или создаём User
        user = existing.get(key)
        if user:
            changed = (
                    user.first_name != first_name or
                    user.last_name != last_name or
                    user.middle_name != middle_name or
                    user.email != email or
                    user.phone != phone or
                    user.birth_date != birth_date or
                    user.iin != iin
            )
            if changed:
                user.first_name = first_name
                user.last_name = last_name
                user.middle_name = middle_name
                user.email = email
                user.phone = phone
                user.birth_date = birth_date
                user.iin = iin
                return {'action': 'update', 'user': user}
        else:
            new_user = User(
                username=username_to_use,
                iin=iin or None,
                email=email or "",
                first_name=first_name,
                last_name=last_name,
                middle_name=middle_name,
                phone=phone,
                birth_date=birth_date,
                is_staff=True,
                is_active=True,
                date_joined=created_at,
            )
            new_user.set_unusable_password()

            # Накопление связей UserCompany
            if company and department and position:
                self.associations[key].append((company, department, position))

            return {'action': 'create', 'user': new_user, 'key': key, 'username': username_to_use}

        # Если пользователь существует, но изменений нет - добавляем связи
        if company and department and position:
            self.associations[key].append((company, department, position))

        return None

    def _get_or_create_position(self, item, company, comp_code):
        """Получение или создание позиции"""
        pos_info = item.get("position") or {}
        pos_id = pos_info.get("ID1C") or pos_info.get("id")
        label = (pos_info.get("label") or "").strip()

        position = None

        # Поиск по ID1C
        if pos_id:
            position = self.positions_by_id.get(str(pos_id))

        # Поиск по label и компании
        if not position and comp_code and label:
            position = self.positions_by_label.get((comp_code, label))

        # Создание новой позиции
        if not position and label and company:
            if not self.dry_run:
                position, created = Position.objects.get_or_create(
                    company=company,
                    name=label,
                    defaults={'ID1C': pos_id or None, 'is_boss': False}
                )
                # Обновляем кэши
                self.positions_by_label[(comp_code, label)] = position
                if pos_id:
                    self.positions_by_id[str(pos_id)] = position

                if created:
                    self.logger.info("📌 Создана должность %r для компании %r", label, comp_code)
            else:
                self.add_error(f"⚠ (dry_run) Нужно создать должность {label} для компании {comp_code}")

        return position

    def _generate_username(self, ld_account, first_name, last_name, iin):
        """Генерация username"""
        if ld_account:
            return ld_account

        # Генерация на основе имени и фамилии
        latin_fn = unidecode(first_name)
        latin_ln = unidecode(last_name)
        base = f"{latin_fn}_{latin_ln}".lower()
        base = re.sub(r"\s+", "_", base)
        base = re.sub(r"[^\w_]", "", base) or iin

        # Проверка уникальности
        candidate = base
        suffix = 1
        while candidate in self.used_usernames:
            suffix += 1
            candidate = f"{base}_{suffix}"

        self.used_usernames.add(candidate)
        return candidate

    def _save_users(self, to_create, to_update, created_usernames):
        """Сохранение пользователей и связей"""
        with transaction.atomic():
            # Bulk-create новых пользователей
            if to_create:
                count_before = User.objects.count()
                User.objects.bulk_create(
                    to_create,
                    batch_size=settings.BATCH_SIZE,
                    ignore_conflicts=True,
                )
                # Определяем, сколько реально создалось
                created_cnt = User.objects.count() - count_before
                skipped_cnt = len(to_create) - created_cnt
                if skipped_cnt:
                    self.logger.warning(
                        "⚠ Пропущено %d пользователей (username уже существует)", skipped_cnt
                    )
                # Назначаем роль learner тем, кто только что создался
                self._assign_learner_role(created_usernames)

            # Bulk-update существующих пользователей
            if to_update:
                User.objects.bulk_update(
                    to_update,
                    fields=["first_name", "last_name", "middle_name", "email", "phone", "birth_date", "iin"],
                    batch_size=settings.BATCH_SIZE,
                )

            # Пересоздание UserCompany-связей
            self._recreate_user_company_relations()

    def _assign_learner_role(self, created_usernames):
        """Назначение роли learner новым пользователям"""
        try:
            learner = Role.objects.get(code="learner")
            thru = User.roles.through

            new_ids = list(
                User.objects.filter(username__in=created_usernames)
                .values_list("id", flat=True)
            )

            # Удаляем существующие связи с ролью learner (на всякий случай)
            thru.objects.filter(user_id__in=new_ids, role_id=learner.id).delete()

            # Создаем новые связи
            links = [thru(user_id=uid, role_id=learner.id) for uid in new_ids]
            thru.objects.bulk_create(links, batch_size=100)

        except Role.DoesNotExist:
            self.logger.warning("⚠ Роль 'learner' не найдена. Пропуск назначения ролей.")
        except Exception as e:
            self.logger.exception("❌ Ошибка при назначении ролей: %s", str(e))

    def _recreate_user_company_relations(self):
        """Пересоздание связей UserCompany чанками"""
        if not self.associations:
            return

        chunk_size = 1000
        keys = list(self.associations.keys())

        for i in range(0, len(keys), chunk_size):
            batch_keys = keys[i:i + chunk_size]

            # Получаем ID пользователей
            qs = User.objects.filter(
                Q(username__in=batch_keys) | Q(iin__in=batch_keys)
            ).only("id", "username", "iin")

            id_map = {u.username: u.id for u in qs}
            for u in qs:
                if u.iin:
                    id_map[u.iin] = u.id

            user_ids = list(id_map.values())

            # Удаляем старые связи
            UserCompany.objects.filter(user_id__in=user_ids).delete()

            # Создаем новые связи
            new_links = []
            for key in batch_keys:
                uid = id_map.get(key)
                if not uid:
                    continue

                for comp, dept, pos in self.associations[key]:
                    new_links.append(
                        UserCompany(
                            user_id=uid,
                            company=comp,
                            department=dept,
                            position=pos,
                        )
                    )

            if new_links:
                UserCompany.objects.bulk_create(new_links, batch_size=settings.BATCH_SIZE)
                self.logger.info("✅ Создано %d связей UserCompany для чанка", len(new_links))