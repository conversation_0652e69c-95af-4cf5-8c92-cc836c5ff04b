from core.permissions import HasPermissionCode


class CanViewUsers(HasPermissionCode):
    permission_code = 'viewing-users'


class CanCreateImportUsers(HasPermissionCode):
    permission_code = 'creating-and-importing-users'


class CanDeleteUsers(HasPermissionCode):
    permission_code = 'deleting-users'


class CanChangeUsersAccessLevel(HasPermissionCode):
    permission_code = 'changing-the-users-access-level'


class HasAccessToUsersPortal(HasPermissionCode):
    permission_code = 'access-to-the-users-portal'


class CanCreateGroups(HasPermissionCode):
    permission_code = 'creating-groups'


class CanViewGroups(HasPermissionCode):
    permission_code = 'viewing-groups'


class CanEditGroups(HasPermissionCode):
    permission_code = 'editing-groups'


class CanDeleteGroups(HasPermissionCode):
    permission_code = 'deleting-groups'


class CanCreateAndEditDepartments(HasPermissionCode):
    permission_code = 'creating-and-editing-divisions'


class CanDeleteDepartments(HasPermissionCode):
    permission_code = 'deleting-departments'


class CanViewDepartments(HasPermissionCode):
    permission_code = 'viewing-divisions'


class CanChangePersonalUserInformation(HasPermissionCode):
    permission_code = 'changing-personal-information-about-a-user'


class CanViewUserCompetenceAssessments(HasPermissionCode):
    permission_code = 'viewing-user-competence-assessments'


class CanCreateSelfRegistrationProfiles(HasPermissionCode):
    permission_code = 'creating-self-registration-profiles'


class CanModerateNewEmployees(HasPermissionCode):
    permission_code = 'moderation-of-new-employees'


class CanViewEvents(HasPermissionCode):
    permission_code = 'viewing-events'


class CanCreateEvent(HasPermissionCode):
    permission_code = 'creating-events'


class CanEditEvent(HasPermissionCode):
    permission_code = 'editing-events'


class CanDeleteEvent(HasPermissionCode):
    permission_code = 'deleting-events'


class CanRegisterParticipants(HasPermissionCode):
    permission_code = 'registration-of-participants'


class CanMarkAttendance(HasPermissionCode):
    permission_code = 'attendance-mark'


class CanManageNewsAndBanners(HasPermissionCode):
    permission_code = 'news-and-banner-management'

