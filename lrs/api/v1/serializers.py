from rest_framework import serializers
from tincan import Agent<PERSON>ccount, Agent, Activity, ActivityDefinition, Verb, LanguageMap, Statement


class AgentAccountSerializer(serializers.Serializer):
    homePage = serializers.CharField()
    name = serializers.CharField()

    def to_object(self):
        return AgentAccount(
            home_page=self.validated_data['homePage'],
            name=self.validated_data['name']
        )


class AgentSerializer(serializers.Serializer):
    account = AgentAccountSerializer()

    def to_object(self) -> Agent:
        account_serializer = self.fields['account']
        account_serializer._validated_data = self.validated_data['account']
        account = account_serializer.to_object()
        return Agent(account=account)


class GetStateRequestSerializer(serializers.Serializer):
    agent = AgentSerializer()
    stateId = serializers.CharField()
    activityId = serializers.CharField()

    def get_agent(self) -> Agent:
        agent_data = self.validated_data['agent']
        account_data = agent_data['account']

        account = AgentAccount(
            home_page=account_data['homePage'],
            name=account_data['name']
        )
        return Agent(account=account)

    def get_activity(self) -> Activity:
        return Activity(
            id=self.validated_data['activityId'],
            definition=ActivityDefinition()
        )


class SetStateRequestSerializer(GetStateRequestSerializer):
    state = serializers.DictField()


class VerbSerializer(serializers.Serializer):
    id = serializers.CharField()


class ObjectSerializer(serializers.Serializer):
    id = serializers.CharField()


class StatementSerializer(serializers.Serializer):
    actor = AgentSerializer()
    verb = VerbSerializer()
    timestamp = serializers.DateTimeField()
    object = ObjectSerializer()

    def get_actor(self) -> Agent:
        agent_data = self.validated_data['actor']
        account_data = agent_data['account']

        account = AgentAccount(
            home_page=account_data['homePage'],
            name=account_data['name']
        )
        return Agent(account=account)

    def get_verb(self) -> Verb:
        verb_data = self.validated_data['verb']
        return Verb(
            id=verb_data['id'],
            display=LanguageMap({'en-US': 'experienced'})
        )

    def get_object(self) -> Activity:
        object_data = self.validated_data['object']
        return Activity(
            id=object_data['id'],
            definition=ActivityDefinition()
        )


class StatementRequestSerializer(serializers.Serializer):
    statement = StatementSerializer()

    def get_statement(self) -> Statement:
        stmt_data = self.validated_data['statement']
        stmt_serializer = StatementSerializer(data=stmt_data)
        stmt_serializer.is_valid(raise_exception=True)

        return Statement(
            actor=stmt_serializer.get_actor(),
            verb=stmt_serializer.get_verb(),
            object=stmt_serializer.get_object(),
            timestamp=stmt_data['timestamp']
        )