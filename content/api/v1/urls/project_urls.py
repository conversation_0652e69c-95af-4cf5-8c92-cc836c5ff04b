from django.urls import path

from ..views.project_views import (
    ProjectListView, ProjectCreateView, ProjectRetrieveDestroyView, ProjectUpdateView, \
    ParticipantListView, ParticipantCreateAPIView, ProjectParticipantOwnerView, ProjectParticipantLeaveView, \
    ProjectParticipantTypeView
)

urlpatterns = [
    path('', ProjectListView.as_view()),
    path('create/', ProjectCreateView.as_view()),
    path('<uuid:pk>/', ProjectRetrieveDestroyView.as_view()),
    path('<uuid:pk>/update/', ProjectUpdateView.as_view()),
    path('<uuid:pk>/participants/', ParticipantListView.as_view()),
    path('<uuid:pk>/participants/create/', ParticipantCreateAPIView.as_view()),
    path('<uuid:pk>/participants/<uuid:participant_id>/type/', ProjectParticipantTypeView.as_view()),
    path('<uuid:pk>/participants/<uuid:participant_id>/owner/', ProjectParticipantOwnerView.as_view()),
    path('<uuid:pk>/participants/<uuid:participant_id>/leave/', ProjectParticipantLeaveView.as_view()),
]