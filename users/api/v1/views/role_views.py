from drf_yasg.utils import swagger_auto_schema
from rest_framework import generics, status, viewsets
from rest_framework.response import Response

from users.api.v1.serializers.permission_serializers import RolePermissionsSerializer
from users.api.v1.serializers.role_serializers import (
    RoleCreateSerializer,
    RoleListSerializer,
    RoleRetrieveSerializer,
    RoleSerializer,
    RoleUpdateSerializer,
    UserRolesRequestSerializer,
)
from users.models import Role, User
from users.permissions.codes import CanEditGroups, CanViewGroups


class ListUserRolesView(generics.GenericAPIView):
    serializer_class = RoleSerializer
    queryset = User.objects.all()
    permission_classes = [CanViewGroups]
    pagination_class = None

    def get(self, request, *args, **kwargs):
        roles = self.get_object().roles.all()
        serializer = self.serializer_class(roles, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class UpdateUserRolesView(generics.GenericAPIView):
    queryset = User.objects.all()
    serializer_class = UserRolesRequestSerializer
    permission_classes = [CanEditGroups]

    @swagger_auto_schema(request_body=UserRolesRequestSerializer)
    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(
            data=request.data, instance=self.get_object()
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data, status=status.HTTP_200_OK)


class RoleViewSet(viewsets.ModelViewSet):
    queryset = (
        Role.objects.prefetch_related("permissions", "users").order_by("id").all()
    )
    serializer_class = RolePermissionsSerializer
    action_serializers = {
        "list": RoleListSerializer,
        "retrieve": RoleRetrieveSerializer,
        "create": RoleCreateSerializer,
        "update": RoleUpdateSerializer,
    }

    def get_serializer_class(self):
        if hasattr(self, "action_serializers"):
            return self.action_serializers.get(self.action, self.serializer_class)

        return super(RoleViewSet, self).get_serializer_class()
