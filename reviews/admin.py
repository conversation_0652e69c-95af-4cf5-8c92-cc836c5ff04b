from django.contrib import admin

from reviews.models import Review, Comment


@admin.register(Review)
class ReviewAdmin(admin.ModelAdmin):
    list_display = ('resource', 'user', 'rating', 'title')
    list_filter = ('resource',)
    search_fields = ('user__email', 'resource__name')
    autocomplete_fields = ('user', 'resource')


@admin.register(Comment)
class CommentAdmin(admin.ModelAdmin):
    list_display = ('review', 'user', 'parent', 'content')
    search_fields = ('user__email', 'review__resource__name')
    autocomplete_fields = ('user', 'review')
