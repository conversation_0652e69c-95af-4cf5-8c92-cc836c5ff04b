import logging

from content.models import Enrollment
from django.utils import timezone

logger = logging.getLogger('app')


class OverdueEnrollmentsService:
    """
    Сервис для автоматической смены статуса/траектории для просроченных курсов/лонгридов/ресурсов

    Условия принудительного перехода на статус "Не завершен":
    - Пользователь не начал курс: Enrollment.status = Не начат
    - Пользователь не завершил курс: Enrollment.status = В прогрессе
    - Дедлайн был до сегодня
    """
    def run(self):
        today = timezone.now()
        updated_count = Enrollment.objects.filter(
            status__in=[Enrollment.IN_PROGRESS, Enrollment.NOT_STARTED],
            access_date__lt=today,
        ).update(status=Enrollment.FINISHED_MANUALLY)

        logger.info(
            f"[OverdueEnrollmentsService]: Обновлено {updated_count} записей: "
            f"статус -> FINISHED_MANUALLY, дедлайн до {today}"
        )
