"""
URL configuration for lms project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from debug_toolbar.toolbar import debug_toolbar_urls
from django.conf import settings
from django.contrib import admin
from django.urls import path, include
from drf_yasg import openapi
from drf_yasg.views import get_schema_view
from rest_framework.permissions import AllowAny

urlpatterns = [
    path('edu/admin/', admin.site.urls),
    path('edu/api/v1/users/', include('users.api.v1.urls')),
    path('edu/api/v1/content/', include('content.api.v1.urls')),
    path('edu/api/v1/notifications/', include('notifications.api.v1.urls')),
    path('edu/api/v1/core/', include('core.api.v1.urls')),
    path('edu/api/v1/lrs/', include('lrs.api.v1.urls')),
    path('edu/api/v1/qa/', include('qa.api.v1.urls')),
    path('edu/api/v1/reviews/', include('reviews.api.v1.urls')),
]

if settings.DEBUG:
    schema_view = get_schema_view(
        openapi.Info(
            title='LMS API',
            default_version='v1',
            description='Backend service API',
        ),
        public=True,
        permission_classes=[AllowAny, ],
    )

    urlpatterns += debug_toolbar_urls()
    urlpatterns += [
        path('edu/swagger/', schema_view.with_ui('swagger', cache_timeout=0)),
        path('edu/redoc/', schema_view.with_ui('redoc', cache_timeout=0)),
        path('ckeditor5/', include('django_ckeditor_5.urls')),
    ]
