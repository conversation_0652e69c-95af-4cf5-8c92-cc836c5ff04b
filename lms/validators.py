from django.core.exceptions import ValidationError
from django.utils.translation import gettext as _


class SpecialCharacterPasswordValidator:
    def validate(self, password, user=None):
        if not any(char in '!@#$%^&*()_+-=' for char in password):
            raise ValidationError(self.get_help_text(), code='no_special_character')

    @staticmethod
    def get_help_text():
        return _("The password must contain at least one special character: !@#$%^&*()_+-"),


class UppercasePasswordValidator:

    def validate(self, password, user=None):
        if not any(char.isupper() for char in password):
            raise ValidationError(self.get_help_text(), code='no_uppercase_letter')

    @staticmethod
    def get_help_text():
        return _("Your password must contain at least one uppercase letter.")


class NumberPasswordValidator:

    def validate(self, password, user=None):
        if not any(char.isdigit() for char in password):
            raise ValidationError(self.get_help_text(), code='no_digit')

    @staticmethod
    def get_help_text():
        return _("The password must contain at least one digit.")
