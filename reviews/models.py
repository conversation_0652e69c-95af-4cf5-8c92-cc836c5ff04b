from django.db import models
from django.utils.translation import gettext_lazy as _
from core.models import BaseUUIDModel, upload_to

from content.models import Resource
from users.models import User


class Review(BaseUUIDModel):
    resource = models.ForeignKey(Resource, on_delete=models.CASCADE, verbose_name=_('resource'),
                                 related_name='resource_reviews')
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name=_('user'), related_name='user_reviews')
    rating = models.PositiveSmallIntegerField(verbose_name=_('rating'))  # от 1 до 5
    title = models.TextField(max_length=255, verbose_name=_('title'), blank=True, null=True)
    content = models.TextField(verbose_name=_('Content'), null=True, blank=True)

    def __str__(self):
        return f"{self.resource.name} - {self.user.email}"

    class Meta:
        verbose_name = _('resource review')
        verbose_name_plural = _('resource reviews')
        unique_together = ('user', 'resource')
        db_table = 'reviews'


class Comment(BaseUUIDModel):
    review = models.ForeignKey(Review, on_delete=models.CASCADE, related_name='review_comments')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='user_comments')
    parent = models.ForeignKey('self', null=True, blank=True, on_delete=models.CASCADE, related_name='replies')
    content = models.TextField(verbose_name=_('Content'), null=True, blank=True)

    def __str__(self):
        return f"{self.review.resource.name} - {self.user.email}"

    class Meta:
        verbose_name = _('review comment')
        verbose_name_plural = _('review comments')
        db_table = 'review_comments'

