# Generated by Django 5.1.2 on 2025-07-30 12:59

import django.db.models.deletion
import django_ckeditor_5.fields
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
    ]

    operations = [
        migrations.CreateModel(
            name='NotificationReceiver',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('channel', models.CharField(choices=[('EMAIL', 'Email'), ('SMS', 'SMS'), ('MESSENGER', 'Messenger'), ('PUSH', 'Push Notification')], max_length=50, verbose_name='Channel')),
                ('recipient', models.TextField(verbose_name='Recipient')),
                ('code', models.CharField(max_length=255, verbose_name='code')),
                ('is_active', models.BooleanField(default=True, verbose_name='is active')),
                ('order', models.IntegerField(blank=True, default=1, null=True, verbose_name='order')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'notification receiver',
                'verbose_name_plural': 'notification receivers',
                'db_table': 'notification_receivers',
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='NotificationTemplate',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('label', models.CharField(max_length=255, verbose_name='label')),
                ('channel', models.CharField(choices=[('EMAIL', 'Email'), ('SMS', 'SMS'), ('MESSENGER', 'Messenger'), ('PUSH', 'Push Notification')], max_length=50, verbose_name='Channel')),
                ('title', models.CharField(blank=True, max_length=255, null=True, verbose_name='title')),
                ('code', models.CharField(max_length=255, verbose_name='code')),
                ('content', django_ckeditor_5.fields.CKEditor5Field(blank=True, null=True, verbose_name='Content')),
                ('is_active', models.BooleanField(default=True, verbose_name='is active')),
                ('order', models.IntegerField(blank=True, default=1, null=True, verbose_name='order')),
            ],
            options={
                'verbose_name': 'notification template',
                'verbose_name_plural': 'notification templates',
                'db_table': 'notification_templates',
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='UINotification',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('subject', models.CharField(blank=True, max_length=255, null=True, verbose_name='Subject')),
                ('message', models.TextField(verbose_name='Message')),
                ('payload', models.JSONField(blank=True, default=dict, verbose_name='payload')),
                ('is_read', models.BooleanField(default=False, verbose_name='is read')),
                ('read_at', models.DateTimeField(blank=True, null=True, verbose_name='read at')),
            ],
            options={
                'verbose_name': 'ui notification',
                'verbose_name_plural': 'ui notifications',
                'db_table': 'notification_ui_notifications',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('channel', models.CharField(choices=[('EMAIL', 'Email'), ('SMS', 'SMS'), ('MESSENGER', 'Messenger'), ('PUSH', 'Push Notification')], max_length=50, verbose_name='Channel')),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('SENT', 'Sent'), ('FAILED', 'Failed')], default='PENDING', max_length=50, verbose_name='Status')),
                ('recipient', models.TextField(verbose_name='Recipient')),
                ('subject', models.CharField(blank=True, max_length=255, null=True, verbose_name='Subject')),
                ('message', models.TextField(verbose_name='Message')),
                ('payload', models.JSONField(blank=True, default=dict, verbose_name='payload')),
                ('scheduled_time', models.DateTimeField(blank=True, null=True, verbose_name='Scheduled time')),
                ('sent_time', models.DateTimeField(blank=True, null=True, verbose_name='Sent time')),
                ('error_message', models.TextField(blank=True, null=True, verbose_name='Error message')),
                ('object_id', models.PositiveIntegerField(blank=True, null=True, verbose_name='object_id')),
                ('content_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype', verbose_name='content_type')),
            ],
            options={
                'verbose_name': 'notification',
                'verbose_name_plural': 'notifications',
                'db_table': 'notifications',
                'ordering': ['-created_at'],
            },
        ),
    ]
