# Generated by Django 5.1.2 on 2025-07-30 12:59

import django.contrib.auth.models
import django.contrib.auth.validators
import django.db.models.deletion
import django.utils.timezone
import lms.s3_storage
import lms.utils
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='Company',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255, verbose_name='company_name')),
                ('code', models.CharField(max_length=255, unique=True, verbose_name='company_code')),
                ('is_active', models.BooleanField(default=True, verbose_name='is_active')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'company',
                'verbose_name_plural': 'companies',
                'db_table': 'users_companies',
            },
        ),
        migrations.CreateModel(
            name='Country',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=50, unique=True, verbose_name='name')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'country',
                'verbose_name_plural': 'countries',
                'db_table': 'users_country',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Status',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=50, verbose_name='name')),
                ('file', models.FileField(blank=True, null=True, upload_to='users/statuses/%Y/%m/%d/', validators=[lms.utils.validate_file_extension], verbose_name='file')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'status',
                'verbose_name_plural': 'statuses',
                'db_table': 'users_status',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False,
                                        verbose_name='unique identifier')),
                ('iin', models.CharField(blank=True, max_length=12, null=True, verbose_name='iin')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='email address')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='phone')),
                ('middle_name', models.CharField(blank=True, max_length=150, null=True, verbose_name='middle name')),
                ('birth_date', models.DateField(blank=True, null=True, verbose_name='birth date')),
                ('about', models.TextField(blank=True, null=True, verbose_name='about')),
                ('photo', models.ImageField(blank=True, null=True, storage=lms.s3_storage.PublicMediaStorage(), upload_to='users/avatars/%Y/%m/%d/', verbose_name='photo')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
                ('country', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='users', to='users.country', verbose_name='country')),
            ],
            options={
                'verbose_name': 'user',
                'verbose_name_plural': 'users',
                'db_table': 'users_user',
                'ordering': ['-created_at'],
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('ID1C', models.UUIDField(blank=True, editable=False, null=True)),
                ('IDParent1C', models.UUIDField(blank=True, editable=False, null=True)),
                ('name', models.CharField(max_length=255, verbose_name='department_name')),
                ('code', models.CharField(help_text='a unique code for importing users from a file', max_length=255, unique=True, verbose_name='code')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='users.company')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='users.department', verbose_name='parent')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'department',
                'verbose_name_plural': 'departments',
                'db_table': 'users_department',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DepartmentSupervisor',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('type', models.CharField(choices=[('LEAD', 'lead'), ('FUNCTIONAL_LEAD', 'functional manager')], max_length=20, verbose_name='type')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='supervisors_in_department', to='users.department', verbose_name='department')),
                ('supervisor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='supervisor_in_department', to=settings.AUTH_USER_MODEL, verbose_name='supervisor')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'supervisor of the Department',
                'verbose_name_plural': 'supervisors of the Department',
                'db_table': 'users_department_supervisors',
                'unique_together': {('supervisor', 'department', 'type')},
            },
        ),
        migrations.AddField(
            model_name='department',
            name='supervisors',
            field=models.ManyToManyField(blank=True, related_name='departments', through='users.DepartmentSupervisor', to=settings.AUTH_USER_MODEL, verbose_name='supervisors'),
        ),
        migrations.CreateModel(
            name='Permission',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255, verbose_name='name')),
                ('code', models.CharField(max_length=255, unique=True, verbose_name='code')),
                ('description', models.TextField(blank=True, null=True, verbose_name='description')),
                ('dependencies', models.ManyToManyField(blank=True, related_name='dependencies_in_permission', to='users.permission', verbose_name='dependencies')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='users.permission', verbose_name='parent')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'permission',
                'verbose_name_plural': 'permissions',
                'db_table': 'users_permission',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ContentType',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=50, verbose_name='name')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='code')),
                ('permissions', models.ManyToManyField(blank=True, related_name='content_types', to='users.permission', verbose_name='permission')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'content type',
                'verbose_name_plural': 'content types',
                'db_table': 'users_content_type',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Position',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('ID1C', models.UUIDField(blank=True, editable=False, null=True)),
                ('name', models.CharField(max_length=255, verbose_name='position_name')),
                ('is_boss', models.BooleanField(default=False, verbose_name='is boss')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='users.company')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'position',
                'verbose_name_plural': 'positions',
                'db_table': 'users_positions',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='DepartmentPosition',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='users.company', verbose_name='company')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='users.department', verbose_name='department')),
                ('position', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='users.position', verbose_name='position')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'users_department_position',
            },
        ),
        migrations.CreateModel(
            name='Role',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255, verbose_name='name')),
                ('code', models.CharField(max_length=255, unique=True, verbose_name='code')),
                ('description', models.TextField(verbose_name='description')),
                ('is_deletable', models.BooleanField(default=True, verbose_name='is deletable')),
                ('has_department', models.BooleanField(default=True, verbose_name='has department')),
                ('permissions', models.ManyToManyField(blank=True, related_name='roles', to='users.permission', verbose_name='permissions')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'role',
                'verbose_name_plural': 'roles',
                'db_table': 'users_role',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='user',
            name='roles',
            field=models.ManyToManyField(blank=True, related_name='users', to='users.role', verbose_name='roles'),
        ),
        migrations.CreateModel(
            name='UserCompany',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='users.company', verbose_name='company')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='users.department', verbose_name='department')),
                ('position', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='users.position', verbose_name='position')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='companies', to=settings.AUTH_USER_MODEL, verbose_name='user')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'user_company',
                'verbose_name_plural': 'user_companies',
                'db_table': 'users_user_companies',
            },
        ),
        migrations.CreateModel(
            name='UserRoleDepartment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='role_user_departments', to='users.department', verbose_name='department')),
                ('role', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_role_departments', to='users.role', verbose_name='role')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='role_departments', to=settings.AUTH_USER_MODEL, verbose_name='user')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'users_user_role_departments',
            },
        ),
        migrations.CreateModel(
            name='UserSchedule',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('finish_at', models.DateField(verbose_name='finish at')),
                ('event', models.CharField(choices=[('DEACTIVATE', 'block'), ('REMOVE', 'remove'), ('DISMISS', 'dismiss'), ('ACTIVATE', 'activate')], max_length=20, verbose_name='event')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='deactivate_schedule', to=settings.AUTH_USER_MODEL, verbose_name='user')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'user blocking schedule',
                'verbose_name_plural': 'user blocking schedules',
                'db_table': 'users_user_schedule',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='UserStatus',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('start_at', models.DateField(verbose_name='start at')),
                ('end_at', models.DateField(blank=True, null=True, verbose_name='end at')),
                ('status', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='users.status', verbose_name='status')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='statuses', to=settings.AUTH_USER_MODEL, verbose_name='user')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'user status',
                'verbose_name_plural': 'user statuses',
                'db_table': 'users_user_statuses',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='UserSupervisor',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('type', models.CharField(choices=[('LEAD', 'lead'), ('FUNCTIONAL_LEAD', 'functional manager')], max_length=20, verbose_name='Type')),
                ('supervisor', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='supervisors_in_supervisor', to=settings.AUTH_USER_MODEL, verbose_name='supervisor')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='supervisors_in_user', to=settings.AUTH_USER_MODEL, verbose_name='user')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': "the user's supervisor",
                'verbose_name_plural': 'supervisors of users',
                'db_table': 'users_user_supervisors',
            },
        ),
        migrations.AddField(
            model_name='user',
            name='supervisors',
            field=models.ManyToManyField(related_name='users', through='users.UserSupervisor', to=settings.AUTH_USER_MODEL, verbose_name='supervisors'),
        ),
        migrations.CreateModel(
            name='UserSupervisorSettings',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('expiring_certificates', models.BooleanField(default=False)),
                ('expired_certificates', models.BooleanField(default=False)),
                ('inactive_on_portal', models.BooleanField(default=False)),
                ('min_training_percentage', models.PositiveSmallIntegerField(default=0)),
                ('max_training_percentage', models.PositiveSmallIntegerField(default=100)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='supervisor_settings', to=settings.AUTH_USER_MODEL)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddConstraint(
            model_name='position',
            constraint=models.UniqueConstraint(fields=('ID1C', 'company'), name='uniq_position_company'),
        ),
        migrations.AlterUniqueTogether(
            name='departmentposition',
            unique_together={('department', 'position', 'company')},
        ),
        migrations.AlterUniqueTogether(
            name='usercompany',
            unique_together={('user', 'company', 'department', 'position')},
        ),
        migrations.AlterUniqueTogether(
            name='usersupervisor',
            unique_together={('user', 'supervisor', 'type')},
        ),
    ]
