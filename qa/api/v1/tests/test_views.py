from django.contrib.auth import get_user_model
from django.db import models
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase

from content.models import Resource
from qa.models import Question, Answer

User = get_user_model()


def create_and_get_resource(owner: models.Model) -> Resource:
    return Resource.objects.create(
        name="test", description="test", owner=owner, type=Resource.COURSE
    )


def create_and_get_user() -> models.Model:
    return User.objects.create_user(
        username="testuser",
        email="<EMAIL>",
        password="Test.123",
        first_name="Test",
        last_name="Test",
        iin="123456789012",
        is_superuser=False,
    )


def create_and_get_question(author, resource: Resource) -> Question:
    return Question.objects.create(
        author=author, resource=resource, text="Test question"
    )


def create_and_get_hacker_user() -> models.Model:
    return User.objects.create_user(
        username="hacker",
        email="<EMAIL>",
        password="VeryStrongPassword",
        first_name="Hacker",
        last_name="Hackerovich",
        iin="777777777777",
        is_superuser=False,
    )


def create_and_get_answer(question: Question, author: models.Model):
    return Answer.objects.create(
        question=question,
        author=author,
        text="Test answer"
    )


class QuestionViewTest(APITestCase):
    """
    Тесты для views модели Question
    """
    def setUp(self):
        """
        Initial setUp
        """
        self.user: models.Model = create_and_get_user()
        self.resource: Resource = create_and_get_resource(owner=self.user)
        self.question: Question = create_and_get_question(
            author=self.user, resource=self.resource
        )
        self.body_data_for_question: dict = {
            "text": "Test question",
            "resource_id": self.resource.id,
        }
        self.query_params_data_for_question: dict = {
            "resource": self.resource.id,
            "author": self.user.id,
        }

    def test_authenticated_user_can_create_question(self):
        # preparing post request
        self.url = reverse("question-create")
        self.client.force_login(user=self.user)
        response = self.client.post(
            self.url, self.body_data_for_question, format="json"
        )

        # testing
        data = response.json()
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(
            data["resource"]["id"],
            self.body_data_for_question.get("resource_id"),
        )
        self.assertEqual(
            data["text"], self.body_data_for_question.get("text")
        )
        self.assertEqual(data["author"]["username"], self.user.username)

    def test_not_authenticated_user_cannot_create_question(self):
        # preparing post but without auth'ing
        self.url = reverse("question-create")
        response = self.client.post(
            self.url, self.body_data_for_question, format="json"
        )

        # testing
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_authenticated_user_list_questions(self):
        # preparing list-get request
        self.url = reverse("question-list")
        self.client.force_login(user=self.user)
        response = self.client.get(self.url)

        # testing
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_not_authenticated_user_list_questions(self):
        # preparing list-get request without auth'ing
        self.url = reverse("question-list")
        response = self.client.get(self.url)

        # testing
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_list_questions_with_filters(self):
        # preparing list-get with filters by [author, resource]
        self.url = reverse("question-list")
        self.client.force_login(user=self.user)
        response = self.client.get(
            self.url, query_params=self.query_params_data_for_question
        )

        # testing
        data = response.json()
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(data["results"][0]["resource"]["id"], self.resource.id)
        self.assertEqual(data["results"][0]["author"]["id"], self.user.id)
        self.assertEqual(data["results"][0]["id"], self.question.id)

    def test_get_question_detail_view(self):
        # preparing detail-get request
        self.url = reverse("question-detail", kwargs={"pk": self.question.pk})
        self.client.force_login(user=self.user)
        response = self.client.get(self.url)

        # testing
        data = response.json()
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(data["id"], self.question.id)
        self.assertEqual(data["author"]["id"], self.user.id)
        self.assertEqual(data["resource"]["id"], self.resource.id)

    def test_put_question_detail_view(self):
        # preparing detail-put request
        self.url = reverse("question-detail", kwargs={"pk": self.question.pk})
        self.client.force_login(user=self.user)
        data_for_update = {"text": "SUCCESS"}
        response = self.client.put(self.url, data=data_for_update, format="json")

        # testing
        data = response.json()
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(data["text"], data_for_update["text"])

    def test_patch_question_detail_view(self):
        # preparing detail-patch request
        self.url = reverse("question-detail", kwargs={"pk": self.question.pk})
        self.client.force_login(user=self.user)
        data_for_update = {"text": "SUCCESS"}
        response = self.client.patch(self.url, data=data_for_update, format="json")

        # testing
        data = response.json()
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(data["text"], data_for_update["text"])

    def test_not_authorized_user_cannot_put_question(self):
        # preparing detail-put request
        self.url = reverse("question-detail", kwargs={"pk": self.question.pk})
        data_for_update = {"text": "SUCCESS"}
        response = self.client.put(self.url, data=data_for_update, format="json")

        # testing
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_not_authorized_user_can_patch_question(self):
        # preparing detail-put request
        self.url = reverse("question-detail", kwargs={"pk": self.question.pk})
        data_for_update = {"text": "SUCCESS"}
        response = self.client.patch(self.url, data=data_for_update, format="json")

        # testing
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_not_author_cannot_change_question(self):
        self.url = reverse("question-detail", kwargs={"pk": self.question.pk})
        hacker = create_and_get_hacker_user()
        self.client.force_login(user=hacker)
        data_for_update = {"text": "SUCCESS"}
        response = self.client.put(self.url, data=data_for_update, format="json")

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)


class AnswerViewTest(APITestCase):
    def setUp(self):
        self.user = create_and_get_user()
        self.resource: Resource = create_and_get_resource(owner=self.user)
        self.question: Question = create_and_get_question(
            author=self.user, resource=self.resource
        )
        self.answer: Answer = create_and_get_answer(author=self.user, question=self.question)
        self.body_data_for_answer: dict = {
            "text": "Test answer",
            "question_id": self.question.id
        }
        self.query_params_data_for_answer: dict = {
            "question": self.question.id,
            "author": self.user.id
        }

    def test_authenticated_user_can_create_answer(self):
        # preparing post request
        self.url = reverse("answer-create")
        self.client.force_login(user=self.user)
        response = self.client.post(
            self.url, self.body_data_for_answer, format="json"
        )

        # testing
        data = response.json()
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(
            data["question"]["id"], self.question.id
        )
        self.assertEqual(
            data["text"], self.body_data_for_answer.get("text")
        )
        self.assertEqual(data["author"]["username"], self.user.username)

    def test_not_authenticated_user_cannot_create_answer(self):
        # preparing post without auth'ing
        self.url = reverse("answer-create")
        response = self.client.post(
            self.url, self.body_data_for_answer, format="json"
        )

        # testing
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_authenticated_user_list_answers(self):
        # preparing list-get for answers
        self.url = reverse("answer-list")
        self.client.force_login(user=self.user)
        response = self.client.get(self.url)

        data = response.json()
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(data["results"][0]["question"]["id"], self.question.id)
        self.assertEqual(data["results"][0]["author"]["id"], self.user.id)
        self.assertEqual(data["results"][0]["id"], self.answer.id)

    def test_not_authenticated_user_list_answers(self):
        # preparing list-get request without auth'ing
        self.url = reverse("answer-list")
        response = self.client.get(self.url)

        # testing
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_list_answers_with_filters(self):
        # preparing list-get with filters by [author, resource]
        self.url = reverse("answer-list")
        self.client.force_login(user=self.user)
        response = self.client.get(
            self.url, query_params=self.query_params_data_for_answer
        )

        # testing
        data = response.json()
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(data["results"][0]["question"]["id"], self.question.id)
        self.assertEqual(data["results"][0]["author"]["id"], self.user.id)
        self.assertEqual(data["results"][0]["id"], self.answer.id)

    def test_get_answer_detail_view(self):
        # preparing detail-get answer
        self.url = reverse("answer-detail", kwargs={"pk": self.answer.id})
        self.client.force_login(user=self.user)
        response = self.client.get(self.url)

        # testing
        data = response.json()
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(data["id"], self.answer.id)
        self.assertEqual(data["author"]["id"], self.user.id)
        self.assertEqual(data["question"]["id"], self.question.id)

    def test_put_answer_detail_view(self):
        # preparing detail-put request
        self.url = reverse("answer-detail", kwargs={"pk": self.answer.pk})
        self.client.force_login(user=self.user)
        data_for_update = {"text": "SUCCESS"}
        response = self.client.put(self.url, data=data_for_update, format="json")

        # testing
        data = response.json()
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(data["text"], data_for_update["text"])

    def test_patch_answer_detail_view(self):
        # preparing detail-patch request
        self.url = reverse("answer-detail", kwargs={"pk": self.answer.pk})
        self.client.force_login(user=self.user)
        data_for_update = {"text": "SUCCESS"}
        response = self.client.patch(self.url, data=data_for_update, format="json")

        # testing
        data = response.json()
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(data["text"], data_for_update["text"])

    def test_not_authorized_user_cannot_put_answer(self):
        # preparing detail-put request
        self.url = reverse("answer-detail", kwargs={"pk": self.answer.pk})
        data_for_update = {"text": "SUCCESS"}
        response = self.client.put(self.url, data=data_for_update, format="json")

        # testing
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_not_authorized_user_cannot_patch_answer(self):
        # preparing detail-patch request
        self.url = reverse("answer-detail", kwargs={"pk": self.answer.pk})
        data_for_update = {"text": "SUCCESS"}
        response = self.client.patch(self.url, data=data_for_update, format="json")

        # testing
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_not_author_cannot_change_answer(self):
        self.url = reverse("answer-detail", kwargs={"pk": self.answer.pk})
        hacker = create_and_get_hacker_user()
        self.client.force_login(user=hacker)
        data_for_update = {"text": "SUCCESS"}
        response = self.client.put(self.url, data=data_for_update, format="json")

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)