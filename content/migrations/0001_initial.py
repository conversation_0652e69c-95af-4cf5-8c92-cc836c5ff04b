# Generated by Django 5.1.2 on 2025-07-30 12:59

import core.models
import django.core.validators
import django.db.models.deletion
import lms.s3_storage
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Catalog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255, verbose_name='name')),
                ('description', models.TextField(blank=True, null=True, verbose_name='description')),
                ('thumbnail', models.ImageField(blank=True, upload_to='catalogs/thumbnails', verbose_name='thumbnail')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='order')),
                ('is_public', models.BooleanField(default=True, verbose_name='is public')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'catalogs',
                'verbose_name_plural': 'catalogs',
                'db_table': 'catalogs',
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='CatalogAccessGroup',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'catalog_access_groups',
            },
        ),
        migrations.CreateModel(
            name='CatalogResource',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('access_by_claim', models.BooleanField(default=False, verbose_name='access by claim')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'catalog resource',
                'verbose_name_plural': 'catalog resources',
                'db_table': 'content_resource_catalogs',
            },
        ),
        migrations.CreateModel(
            name='Certification',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('path', models.FileField(storage=lms.s3_storage.PublicMediaStorage(), upload_to=core.models.upload_to, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['pdf'])])),
                ('issue_date', models.DateField(blank=True, null=True, verbose_name='certificate_issue_date')),
                ('expired_date', models.DateField(blank=True, null=True, verbose_name='certificate_expired_date')),
                ('status', models.CharField(blank=True, choices=[('ACTIVE', 'active'), ('EXPIRED', 'expired')], default='ACTIVE', max_length=20, null=True, verbose_name='type')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'certificate',
                'verbose_name_plural': 'certificates',
            },
        ),
        migrations.CreateModel(
            name='Detail',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('recommended_time', models.PositiveIntegerField(default=0, verbose_name='recommended time')),
                ('thumbnail', models.ImageField(blank=True, max_length=255, storage=lms.s3_storage.PublicMediaStorage(), upload_to='resources/thumbnails/%Y/%m/%d/', verbose_name='thumbnail')),
                ('cover', models.ImageField(blank=True, max_length=255, storage=lms.s3_storage.PublicMediaStorage(), upload_to='resources/covers/%Y/%m/%d/', verbose_name='cover')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'details',
                'verbose_name_plural': 'details',
                'db_table': 'content_details',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DueDateSettings',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('type', models.CharField(choices=[('UNLIMITED', 'due_date_setting_unlimited'), ('DEFAULT', 'due_date_setting_default'), ('DUE_DATE', 'due_date_setting_due_date'), ('DUE_PERIOD', 'due_date_setting_due_period')], max_length=20, verbose_name='due_date_settings_type')),
                ('period', models.IntegerField(default=0, verbose_name='period')),
                ('period_unit', models.CharField(blank=True, choices=[('DAYS', 'period_days'), ('WEEKS', 'period_weeks'), ('MONTHS', 'period_months'), ('YEARS', 'period_years')], max_length=10, null=True, verbose_name='period_unit')),
                ('date', models.DateField(blank=True, null=True, verbose_name='due_date_setting_date')),
                ('lock_after_due_date', models.BooleanField(default=False, verbose_name='lock_after_due_date')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'due_date_settings',
                'verbose_name_plural': 'due_date_settings',
                'db_table': 'content_due_date_settings',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Enrollment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('access_date', models.DateTimeField(verbose_name='enrollment_access_date')),
                ('status', models.CharField(choices=[('notStarted', 'enrollment_not_started'), ('FINISHED_MANUALLY', 'enrollment_finished_manually'), ('NOT_FINISHED_MANUALLY', 'enrollment_not_finished_manually'), ('finished', 'enrollment_finished'), ('inProgress', 'enrollment_in_progress')], default='notStarted', max_length=32, verbose_name='enrollment_status')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='enrollment_completed_at')),
                ('change_reason', models.TextField(blank=True, null=True, verbose_name='enrollment_change_reason')),
                ('progress', models.PositiveIntegerField(default=0, verbose_name='enrollment_progress')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'enrollment',
                'verbose_name_plural': 'enrollments',
                'db_table': 'content_enrollments',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='EnrollmentProgress',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('status', models.CharField(choices=[('notStarted', 'enrollment_not_started'), ('FINISHED_MANUALLY', 'enrollment_finished_manually'), ('NOT_FINISHED_MANUALLY', 'enrollment_not_finished_manually'), ('finished', 'enrollment_finished'), ('inProgress', 'enrollment_in_progress')], default='notStarted', max_length=32, verbose_name='status')),
                ('progress', models.PositiveIntegerField(default=0, verbose_name='enrollment_progress')),
                ('started_at', models.DateTimeField(blank=True, null=True, verbose_name='started_at')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='enrollment_completed_at')),
                ('lrs_statement_id', models.CharField(blank=True, max_length=255, null=True, verbose_name='lrs_statement_id')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'enrollment_progress',
                'verbose_name_plural': 'enrollment_progresses',
            },
        ),
        migrations.CreateModel(
            name='Outline',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('type', models.CharField(choices=[('STAGE', 'Stage'), ('COURSE', 'Course')], default='STAGE', max_length=20, verbose_name='type')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='order')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'outline',
                'verbose_name_plural': 'outlines',
                'db_table': 'content_outlines',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='OutlineAccessTime',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('type', models.CharField(choices=[('UNLIMITED', 'unlimited'), ('RELATIVE', 'relative')], max_length=20, verbose_name='type')),
                ('interval', models.PositiveIntegerField(default=0, verbose_name='outline_access_interval')),
                ('period', models.IntegerField(default=0, verbose_name='outline_access_period')),
                ('period_unit', models.CharField(blank=True, choices=[('DAYS', 'days'), ('WEEKS', 'weeks'), ('MONTHS', 'months'), ('YEARS', 'years')], max_length=10, null=True, verbose_name='outline_access_unit')),
                ('lock_after_expiration', models.BooleanField(default=False, verbose_name='lock_after_expiration')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'outline_access_time',
                'verbose_name_plural': 'outline_access_times',
                'db_table': 'content_outline_access_times',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Participant',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('availability', models.CharField(choices=[('VIEW', 'preview'), ('EDITOR', 'editing')], max_length=10, verbose_name='availability')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'participant',
                'verbose_name_plural': 'participants',
                'db_table': 'content_participants',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Preference',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('type', models.CharField(choices=[('FOLDER', 'folder'), ('LEARNING_TRACK', 'learning track'), ('LEARNING_PATH', 'learning path'), ('LINK', 'link'), ('HOMEWORK', 'homework'), ('ONLINE_QUIZ', 'online quiz'), ('LONGREAD', 'longread'), ('CHAPTER', 'chapter'), ('STAGE', 'stage'), ('COURSE', 'course'), ('FILE', 'file')], max_length=20, verbose_name='type')),
                ('section', models.CharField(choices=[('STRUCTURE', 'structure'), ('DETAIL', 'detail'), ('NOTIFICATION', 'notification'), ('ACCESS_CONTROL', 'access control'), ('COMPLETION', 'completion'), ('ENROLLMENT', 'enrollment'), ('REPORT', 'report'), ('FEEDBACK', 'feedback')], max_length=20, verbose_name='section')),
                ('code', models.CharField(max_length=255, unique=True, verbose_name='code')),
                ('name', models.CharField(max_length=255, verbose_name='name')),
                ('payload', models.JSONField(blank=True, default=dict, help_text='validation via the jsonschema package', verbose_name='payload')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'preference',
                'verbose_name_plural': 'preferences',
                'db_table': 'content_preferences',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Resource',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255, verbose_name='name')),
                ('description', models.TextField(blank=True, verbose_name='description')),
                ('is_shared', models.BooleanField(default=False, verbose_name='is shared')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='is_deleted')),
                ('payload', models.JSONField(blank=True, default=dict, verbose_name='payload')),
                ('type', models.CharField(choices=[('FOLDER', 'folder'), ('LEARNING_TRACK', 'learning track'), ('LEARNING_PATH', 'learning path'), ('LINK', 'link'), ('HOMEWORK', 'homework'), ('ONLINE_QUIZ', 'online quiz'), ('LONGREAD', 'longread'), ('CHAPTER', 'chapter'), ('STAGE', 'stage'), ('COURSE', 'course'), ('FILE', 'file')], max_length=20, verbose_name='type')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='order')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'resource',
                'verbose_name_plural': 'resources',
                'db_table': 'content_resources',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ResourceApplication',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('APPROVED', 'Approved'), ('REJECTED', 'Rejected'), ('CANCELED', 'Canceled')], default='PENDING', max_length=20, verbose_name='status')),
                ('processed_at', models.DateTimeField(blank=True, null=True, verbose_name='processed at')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'resource application',
                'verbose_name_plural': 'resource applications',
                'db_table': 'content_resource_applications',
            },
        ),
        migrations.CreateModel(
            name='Settings',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('payload', models.JSONField(blank=True, default=dict, verbose_name='payload')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='order')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'settings',
                'verbose_name_plural': 'settings',
                'db_table': 'content_settings',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Tag',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255, unique=True, verbose_name='name')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'tag',
                'verbose_name_plural': 'tags',
                'db_table': 'content_tags',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CatalogAccessDepartment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('catalog', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='content.catalog')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'catalog_access_departments',
            },
        ),
    ]
