import logging
from datetime import timedelta
from content.models import Settings, Preference, Enrollment
from content.utils.utils import format_human_russian
from lms import settings
from notifications.models import ChannelType
from notifications.services import NotificationService

logger = logging.getLogger("app")


class EnrollmentDueDateNotificationService:

    def __init__(self, enrollment_id):
        self.notification_service = NotificationService()
        self.enrollment_id = enrollment_id
        self.enrollment = None
        self.preferences = {}
        self.settings = {}

    def _find_enrollment(self):
        self.enrollment = Enrollment.objects.select_related('resource', 'user', 'due_date_settings').get(id=self.enrollment_id)

    def _load_preferences(self):
        """ Загружает обе настройки: до и после дедлайна. """
        base_code = self.enrollment.resource.type
        codes = [
            f"{base_code}_REMINDER_BEFORE_THE_COMPLETION_DATE",
            f"{base_code}_REMINDER_AFTER_THE_COMPLETION_DATE",
        ]

        self.preferences = {
            p.code: p
            for p in Preference.objects.filter(section=Preference.NOTIFICATION, code__in=codes)
        }

    def _load_settings(self):
        """ Загружает Settings по каждому Preference. """
        for code, pref in self.preferences.items():
            setting = Settings.objects.filter(
                resource_id=self.enrollment.resource.id,
                preference_id=pref.id
            ).first()
            if setting:
                self.settings[code] = setting

    def _get_frontend_my_course_url(self):
        return f"{settings.FRONTEND_BASE_URL}/mobile/my-courses"

    def send_reminder_due_date(self):
        """ Создает уведомления в зависимости от настройки ресурса: до и после дедлайна,
        Напоминание перед сроком завершения и Напоминание после срока завершения """
        self._find_enrollment()
        self._load_preferences()
        self._load_settings()

        due_date = self.enrollment.due_date_settings.due_date
        if not due_date:
            logger.warning(f"Enrollment {self.enrollment.id} has no due date")
            return

        for code, setting in self.settings.items():
            payload = setting.payload
            data = payload.get('data', {})
            notify = payload.get('notify', {})

            if not notify:
                continue

            try:
                intervals = data.get('intervals', [])
                notification = data.get('notification', {})
                for interval in intervals:
                    units = interval.get('units')
                    value = interval.get('value')
                    scheduled_time = None

                    if units == "m":
                        delta = timedelta(minutes=value)
                    elif units == "h":
                        delta = timedelta(hours=value)
                    elif units == "d":
                        delta = timedelta(days=value)
                    elif units == "w":
                        delta = timedelta(weeks=value)
                    else:
                        continue

                    if "BEFORE" in code:
                        scheduled_time = due_date - delta
                    elif "AFTER" in code:
                        scheduled_time = due_date + delta

                    subject = notification.get('subject')
                    message = notification.get('message')
                    context = {
                        'username':        self.enrollment.user.full_name,
                        'content_title':   self.enrollment.resource.name,
                        'due_date':        format_human_russian(self.enrollment.due_date_settings.due_date),
                        'course_info_url': self._get_frontend_my_course_url(),
                    }

                    self.notification_service.create_notifications_batch(
                        channel=ChannelType.EMAIL,
                        users=[self.enrollment.user],
                        subject=subject,
                        content=message,
                        context=context,
                        scheduled_time=scheduled_time,
                        content_object=self.enrollment
                    )
                logger.info(
                    f"[EnrollmentDueDateNotificationService] Notification '{code}' scheduled for user {self.enrollment.user.email}")
            except Exception as e:
                logger.error(
                    f"[EnrollmentDueDateNotificationService] Error processing code={code} for enrollment={self.enrollment.id}: {e}",
                    exc_info=True)
