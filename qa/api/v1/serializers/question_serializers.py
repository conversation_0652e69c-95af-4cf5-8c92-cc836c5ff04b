from rest_framework import serializers

from content.api.v1.serializers.shared_serializers import ResourceSerializer
from content.models import Resource
from qa.models import Question
from users.api.v1.serializers.user_serializers import UserSerializer


class QuestionListSerializer(serializers.ModelSerializer):
    author = UserSerializer(read_only=True)
    resource = ResourceSerializer(read_only=True)
    answers_count = serializers.IntegerField(read_only=True)

    class Meta:
        model = Question
        fields = [
            "id",
            "author",
            "resource",
            "text",
            "is_answered",
            "created_at",
            "updated_at",
            "answers_count",
        ]


class QuestionCreateSerializer(serializers.ModelSerializer):
    author = UserSerializer(read_only=True)
    resource = ResourceSerializer(read_only=True)
    resource_id = serializers.PrimaryKeyRelatedField(
        queryset=Resource.objects.all(), write_only=True, source="resource"
    )

    class Meta:
        model = Question
        fields = ["id", "text", "is_answered", "author", "resource", "resource_id"]
        read_only_fields = ["is_answered"]


class QuestionDetailSerializer(serializers.ModelSerializer):
    author = UserSerializer(read_only=True)
    resource = ResourceSerializer(read_only=True)

    class Meta:
        model = Question
        fields = ["id", "author", "resource", "text", "is_answered"]
