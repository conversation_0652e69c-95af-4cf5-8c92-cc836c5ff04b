from django.contrib import admin

from qa.models import Answer, Question


@admin.register(Question)
class QuestionAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "author",
        "resource",
        "text",
        "is_answered",
        "created_at",
        "updated_at",
    )
    readonly_fields = ("text",)
    list_display_links = ("author", "resource")
    autocomplete_fields = ("author", "resource")
    show_full_result_count = False
    list_filter = ("is_answered",)
    ordering = ("is_answered", "resource")
    search_fields = ("id",)


@admin.register(Answer)
class AnswerAdmin(admin.ModelAdmin):
    list_display = ("id", "question", "author", "text", "created_at", "updated_at")
    list_display_links = ("author", "question")
    autocomplete_fields = ("author", "question")
    show_full_result_count = False
    ordering = ("question",)
