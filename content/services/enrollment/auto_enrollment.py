import logging
from collections import defaultdict

from django.db import transaction
from django.utils import timezone

from content.api.v1.utils import due_date
from content.models import Preference, Settings, Enrollment, DueDateSettings, Resource
from content.utils.utils import get_model_from_string_safe
from users.models import User

logger = logging.getLogger("app")

#TODO: ПРОВЕРИТЬ ЕСЛИ КУРС НАХОДИТСЯ ВНУТРИ ТРАЕКТОРИЙ, ТО ИГНОР

class AutoEnrollmentService:
    """
    Сервис автоматического назначения пользователей на ресурсы на основе настроек предпочтений.

    Основной процесс:
    - Находит настройки автоназначений (preferences)
    - Определяет пользователей по заданным условиям
    - Создает назначения и настройки сроков доступа (DueDateSettings)
    """

    def __init__(self):
        """Инициализация сервиса.

        Создает словарь для хранения новых назначений: ключ — ID ресурса, значение — множество ID пользователей.
        """
        self.new_enrollments = defaultdict(set)

    def run(self):
        """Основной метод запуска автоназначения.

        Находит все активные настройки, собирает новых пользователей и создает назначения.
        """
        assignment_preferences = self._get_assignment_preferences()
        if not assignment_preferences:
            logger.warning('[АВТО_НАЗНАЧЕНИЕ] Не найдены настройки для автоназначения, задача остановлена.')
            return

        resource_settings = Settings.objects.filter(preference__in=assignment_preferences)

        if not resource_settings.exists():
            logger.warning('[АВТО_НАЗНАЧЕНИЕ] Не найдены настройки ресурсов, задача остановлена.')
            return

        self._collect_new_enrollments(resource_settings)
        self._create_enrollments()

    @staticmethod
    def _get_assignment_preferences():
        """Возвращает список шаблонов автоназначения.

        Returns:
            QuerySet: Список объектов Preference, настроенных для автоназначения.
        """
        return Preference.objects.filter(
            section=Preference.ACCESS_CONTROL,
            code__icontains='AUTO_ASSIGNMENT'
        )

    def _collect_new_enrollments(self, resource_settings):
        """Собирает новых пользователей для назначения на основе настроек ресурсов.

        Args:
            resource_settings (QuerySet): Настройки ресурсов для автоназначения.
        """
        for resource_setting in resource_settings:
            resource_id = resource_setting.resource_id
            assignments = resource_setting.payload.get('auto_assignment', [])
            for assignment in assignments:
                user_ids = self._get_user_ids_for_assignment(assignment)
                if not user_ids:
                    continue

                enrolled_user_ids = set(
                    Enrollment.objects.filter(
                        user_id__in=user_ids,
                        resource_id=resource_id
                    ).values_list('user_id', flat=True)
                )

                new_user_ids = set(user_ids) - enrolled_user_ids
                if new_user_ids:
                    self.new_enrollments[resource_id].update(new_user_ids)
                    logger.info(
                        '[АВТО_НАЗНАЧЕНИЕ] Найдено %d новых пользователей для ресурса (ID: %s).',
                        len(new_user_ids), resource_id
                    )

    def _get_user_ids_for_assignment(self, assignment):
        """Определяет список пользователей на основе условия назначения.

        Args:
            assignment (dict): Словарь с описанием условия (тип, модель, значение).

        Returns:
            list: Список ID пользователей, подходящих под условие.
        """
        condition = assignment.get('condition')
        condition_value = assignment.get('condition_value', {})

        model_name = condition_value.get('model')
        model_value = condition_value.get('value')

        model = get_model_from_string_safe(model_name)
        if not model:
            logger.warning('[АВТО_НАЗНАЧЕНИЕ] Модель "%s" не найдена.', model_name)
            return []

        record = model.objects.filter(pk=model_value).first()
        if not record:
            logger.warning('[АВТО_НАЗНАЧЕНИЕ] Объект с id=%s не найден в модели "%s".', model_value, model_name)
            return []

        match condition:
            case 'department':
                user_ids = list(User.objects.filter(department_id=record.id).values_list('id', flat=True))
            case 'department_wth_children':
                department_ids = record.get_descendant_ids_cte()
                user_ids = list(User.objects.filter(department_id__in=department_ids).values_list('id', flat=True))
            case 'group':
                user_ids = list(User.objects.filter(groups__id=record.id).values_list('id', flat=True))
            case _:
                logger.warning('[АВТО_НАЗНАЧЕНИЕ] Неизвестное условие "%s".', condition)
                return []

        logger.info('[АВТО_НАЗНАЧЕНИЕ] Условие "%s" выбрало %d пользователей.', condition, len(user_ids))
        return user_ids

    def _create_enrollments(self):
        """Создает новые назначения и настройки сроков доступа для найденных пользователей."""
        if not self.new_enrollments:
            logger.info('[АВТО_НАЗНАЧЕНИЕ] Нет новых назначений для создания.')
            return

        enrollment_objects = []
        now = timezone.now()

        for resource_id, user_ids in self.new_enrollments.items():
            enrollment_objects.extend(
                Enrollment(user_id=user_id, resource_id=resource_id, access_date=now)
                for user_id in user_ids
            )

        with transaction.atomic():
            created_enrollments = Enrollment.objects.bulk_create(
                enrollment_objects,
                batch_size=500,
            )

            due_date_objects = [
                DueDateSettings(
                    enrollment=enrollment,
                    type=DueDateSettings.TYPE_DEFAULT,
                    date=due_date(
                        type=DueDateSettings.TYPE_DEFAULT,
                        enrollment=enrollment
                    ))
                for enrollment in created_enrollments
            ]

            DueDateSettings.objects.bulk_create(due_date_objects, batch_size=500)

        # Логирование для каждого ресурса отдельно
        for resource_id, user_ids in self.new_enrollments.items():
            try:
                resource = Resource.objects.get(pk=resource_id)
                resource_name = resource.name
            except Resource.DoesNotExist:
                resource_name = "Неизвестный ресурс"

            logger.info(
                '[АВТО_НАЗНАЧЕНИЕ] Назначено %d пользователей для ресурса "%s" (ID: %s).',
                len(user_ids), resource_name, resource_id
            )

        logger.info('[АВТО_НАЗНАЧЕНИЕ] Всего создано %d новых назначений.', len(enrollment_objects))
