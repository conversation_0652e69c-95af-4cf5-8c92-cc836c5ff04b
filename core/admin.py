from django.contrib import admin

from core.models import Media


@admin.register(Media)
class MediaAdmin(admin.ModelAdmin):
    list_display = ('id', 'path', 'content_type', 'size')
    list_display_links = ('id',)
    show_full_result_count = False

    def save_model(self, request, obj, form, change):
        if obj.path:
            obj.name = obj.path.name
            obj.size = obj.path.size
            try:
                obj.content_type = obj.path.file.content_type
            except AttributeError:
                obj.content_type = None
        super().save_model(request, obj, form, change)