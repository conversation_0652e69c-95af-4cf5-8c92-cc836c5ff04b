import logging
import re
from datetime import datetime

from django.core.validators import validate_email
from django.db import transaction, IntegrityError
from django.db.models import Model
from django.forms.models import model_to_dict
from django.template import Template, Context
from django.contrib.contenttypes.models import ContentType

from notifications.models import Notification, NotificationReceiver, NotificationTemplate, UINotification, ChannelType, \
    NotificationStatus
from users.models import User

logger = logging.getLogger('app')


class NotificationService:

    def _validate_recipient(self, channel: str, recipient: str):
        """Валидация получателя в зависимости от канала."""
        if channel == ChannelType.EMAIL:
            emails = [email.strip() for email in recipient.split(",") if email.strip()]
            for email in emails:
                validate_email(email)

    def render_placeholders(self, content: str, context: dict) -> str:
        def replace_match(match):
            """Заменяет плейсхолдеры вида %PLACEHOLDER% на значения из контекста."""
            key = match.group(1)
            return str(context.get(key.lower(), context.get(key.upper(), match.group(0))))

        return re.sub(r'%([A-Z_]+)%', replace_match, content)

    def render_template(self, content: str, context: dict) -> str:
        """ Сначала обрабатывает %PLACEHOLDER%, затем прогоняет через Django Template ({{ }})."""
        pre_rendered = self.render_placeholders(content, context)
        try:
            template = Template(pre_rendered)
            return template.render(Context(context))
        except Exception as e:
            logger.warning(f"[NotificationService] Template rendering failed: {str(e)}")
            return pre_rendered

    def create_notification(self, channel: str, recipient: str, subject: str, message: str,
                            payload: dict = None, scheduled_time: datetime = None, content_object: Model = None):
        """ Создает одно уведомление.  """
        self._validate_recipient(channel, recipient)
        content_type = None
        object_id = None

        if content_object:
            content_type = ContentType.objects.get_for_model(content_object)
            object_id = content_object.pk

        return Notification.objects.create(
            channel=channel,
            recipient=recipient,
            subject=subject,
            message=message,
            payload=payload or {},
            scheduled_time=scheduled_time,
            status=NotificationStatus.PENDING,
            content_type=content_type,
            object_id=object_id,
        )

    def create_notifications_batch(self, channel: str, users: list[User], subject: str,
                                   content: str, context: dict = None,
                                   scheduled_time: datetime = None, content_object: Model = None):
        """ Создает пачку уведомлений для списка пользователей. """
        notifications = []
        context = context or {}

        content_type = None
        object_id = None

        if content_object:
            content_type = ContentType.objects.get_for_model(content_object)
            object_id = content_object.pk

        for user in users:
            try:
                user_payload = model_to_dict(user, fields=['id', 'email'])

                full_context = {
                    **context,
                    'user': user_payload
                }
                rendered_subject = self.render_template(subject or "", full_context)
                rendered_message = self.render_template(content or "", full_context)

                self._validate_recipient(channel, user.email)

                notification = Notification(
                    channel=channel,
                    recipient=user.email,
                    subject=rendered_subject,
                    message=rendered_message,
                    payload=full_context,
                    scheduled_time=scheduled_time,
                    status=NotificationStatus.PENDING,
                    content_type=content_type,
                    object_id=object_id,
                )
                notifications.append(notification)
            except Exception as e:
                logger.error(f"[NotificationService] Failed to render or validate for user {user.id}: {str(e)}")

        try:
            with transaction.atomic():
                Notification.objects.bulk_create(notifications)
            logger.info(f"[NotificationService] Successfully created {len(notifications)} notifications.")
        except IntegrityError as e:
            logger.error(f"[NotificationService] DB error: {str(e)}")
        except Exception as e:
            logger.error(f"[NotificationService] Unexpected error: {str(e)}")

        return notifications

    def create_ui_notification(self, user: User, subject: str, content: str, context: dict = None):
        """
        Создает веб-уведомление (для колокольчика) для одного пользователя.
        """
        context = context or {}
        user_payload = model_to_dict(user, fields=['id', 'email'])

        full_context = {
            **context,
            'user': user_payload
        }

        try:
            rendered_message = self.render_template(content or "", full_context)

            notification = UINotification.objects.create(
                user=user,
                subject=subject,
                message=rendered_message,
                payload=full_context,
            )
            return notification

        except Exception as e:
            logger.error(f"UI Notification creation failed for user {user.id}: {str(e)}")
            return None

    def create_ui_notifications_batch(self, users: list[User], subject: str,
                                      content: str, context: dict = None):
        """Создает пачку UI-уведомлений (колокольчик) для группы пользователей."""
        notifications = []
        context = context or {}

        for user in users:
            try:
                user_payload = model_to_dict(user, fields=['id', 'email'])

                full_context = {
                    **context,
                    'user': user_payload
                }
                rendered_message = self.render_template(content or "", full_context)
                notifications.append(UINotification(
                    user=user,
                    subject=subject,
                    message=rendered_message,
                    payload=full_context,
                ))
            except Exception as e:
                logger.error(f"[NotificationService] Failed to render UI notification for user {user.id}: {str(e)}")

        try:
            with transaction.atomic():
                UINotification.objects.bulk_create(notifications)
            logger.info(f"[NotificationService] Successfully created {len(notifications)} UI notifications.")
        except Exception as e:
            logger.error(f"Failed to save UI notifications: {str(e)}")

        return notifications


class NotificationTemplateService:
    def __init__(self, template_code, context=None, receivers=None):
        self.template = NotificationTemplate.objects.get(code=template_code, is_active=True)
        self.context = context or {}
        self.receivers = receivers

    def generate_notifications(self):
        if self.receivers is not None:
            receivers = self.receivers
        else:
            receivers = NotificationReceiver.objects.filter(
                channel=self.template.channel,
                is_active=True,
            )

        notifications = []
        for receiver in receivers:
            try:
                template = Template(self.template.content or "")
                ctx = Context({**self.context, 'user': receiver.user})
                rendered_message = template.render(ctx)

                notification = Notification(
                    channel=self.template.channel,
                    recipient=receiver.recipient,
                    subject=self.template.title,
                    message=rendered_message,
                    payload=self.context,
                    scheduled_time=None,
                    status=NotificationStatus.PENDING,
                )
                notifications.append(notification)
            except Exception as e:
                logger.error(f"[NotificationService] Failed to render template for receiver {receiver.id}: {str(e)}")

        try:
            with transaction.atomic():
                Notification.objects.bulk_create(notifications)
            logger.info(f"[NotificationService] Successfully created {len(notifications)} notifications.")
        except IntegrityError as e:
            logger.error(f"[NotificationService] Database error while saving notifications: {str(e)}")
        except Exception as e:
            logger.error(f"[NotificationService] Unexpected error while saving notifications: {str(e)}")

        return notifications
