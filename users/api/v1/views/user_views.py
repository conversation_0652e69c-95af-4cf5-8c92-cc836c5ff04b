from django.db.models import <PERSON><PERSON><PERSON><PERSON>, Prefetch, Value
from django.db.models.functions import Concat
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from drf_yasg.utils import swagger_auto_schema
from rest_framework import filters, generics, status
from rest_framework.response import Response

from lms.utils import CustomPagination, get_paginated_serializer
from users.api.v1.serializers.shared_serializers import UserManagementSerializer
from users.api.v1.serializers.user_serializers import (
    UserAutocompleteSerializer,
    UserCreateSerializer,
    UserRequestSerializer,
    UserSerializer,
)
from users.models import User, UserCompany, UserStatus
from users.permissions.codes import (
    CanChangePersonalUserInformation,
    CanCreateImportUsers,
    CanDeleteUsers,
    CanViewUsers,
)


class UserListView(generics.ListAPIView):
    queryset = User.objects.all()
    serializer_class = UserManagementSerializer
    filter_backends = [filters.SearchFilter]
    permission_classes = [CanViewUsers]
    search_fields = ["first_name", "last_name", "email"]
    pagination_class = CustomPagination

    def get_queryset(self):
        # только активные статусы
        statuses_qs = UserStatus.objects.select_related("status").filter(start_at__gte=timezone.now()).order_by(
            "start_at"
        )

        # подтягиваем UserCompany вместе с company/department/position
        companies_qs = UserCompany.objects.select_related(
            "company", "department__parent", "department", "position"
        )

        return (
            self.queryset.prefetch_related(
                Prefetch("statuses", queryset=statuses_qs),
                Prefetch("companies", queryset=companies_qs),
                "roles", "groups"
            )
            .select_related("country")
            .order_by("id")
        )

    @swagger_auto_schema(responses={200: get_paginated_serializer(serializer_class)})
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class UserDestroyView(generics.DestroyAPIView):
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [CanDeleteUsers]


class UserUpdateView(generics.GenericAPIView):
    queryset = User.objects.all()
    serializer_class = UserRequestSerializer
    permission_classes = [CanChangePersonalUserInformation]

    @swagger_auto_schema(
        operation_summary=_("User update"),
        request_body=UserRequestSerializer,
    )
    def put(self, request, *args, **kwargs):
        user = self.get_object()
        serializer = self.get_serializer(instance=user, data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data, status=status.HTTP_200_OK)


class UserRetrieveView(generics.RetrieveAPIView):
    queryset = User.objects.all()
    permission_classes = [CanViewUsers]
    serializer_class = UserSerializer


class UserCreateView(generics.CreateAPIView):
    queryset = User.objects.all()
    permission_classes = [CanCreateImportUsers]
    serializer_class = UserCreateSerializer


class UserAutocompleteView(generics.ListAPIView):
    """
    GET params:
      - search: поиск по ФИО (first_name + last_name) или по email
    """

    serializer_class = UserAutocompleteSerializer
    permission_classes = [CanViewUsers]
    filter_backends = [filters.SearchFilter]
    search_fields = ["name", "email"]

    def get_queryset(self):
        statuses_qs = UserStatus.objects.filter(start_at__gte=timezone.now()).order_by(
            "start_at"
        )

        companies_qs = UserCompany.objects.select_related(
            "company",
            "department__parent",
            "department",
            "position",
        )

        return (
            User.objects.annotate(
                name=Concat(
                    "first_name", Value(" "), "last_name", output_field=CharField()
                )
            )
            .select_related("country")
            .prefetch_related(
                Prefetch("statuses", queryset=statuses_qs),
                Prefetch("companies", queryset=companies_qs),
            )
            .order_by("name")
        )
