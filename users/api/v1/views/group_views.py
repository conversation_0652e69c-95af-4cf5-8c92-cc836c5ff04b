import logging

from django.db.models import Cha<PERSON><PERSON><PERSON>, Count, Prefetch, Q, Value
from django.db.models.functions import Concat
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import generics, status, viewsets
from rest_framework.response import Response
from rest_framework.views import APIView

from lms.utils import CustomPagination
from users.api.v1.serializers.group_serializers import (
    GroupListSerializer,
    GroupSerializer,
    TerminateSerializer,
    UserGroupsRequestSerializer,
)
from users.api.v1.serializers.shared_serializers import UserManagementSerializer
from users.models import Group, User, UserCompany, UserSchedule, UserStatus
from users.permissions.codes import CanDeleteEvent, CanEditGroups, CanViewGroups

logger = logging.getLogger("app")


class ListUserGroupsView(generics.GenericAPIView):
    queryset = User.objects.all()
    serializer_class = GroupListSerializer
    permission_classes = [CanViewGroups]
    pagination_class = None

    def get(self, request, *args, **kwargs):
        groups = self.get_object().groups.all()
        serializer = self.serializer_class(groups, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class UpdateUserGroupsView(generics.GenericAPIView):
    serializer_class = UserGroupsRequestSerializer
    permission_classes = [CanEditGroups]
    queryset = User.objects.all()

    @swagger_auto_schema(request_body=UserGroupsRequestSerializer)
    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(
            data=request.data, instance=self.get_object()
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data, status=status.HTTP_200_OK)


class UserTerminateView(APIView):
    permission_classes = [CanDeleteEvent]

    @swagger_auto_schema(
        request_body=TerminateSerializer,
    )
    def post(self, request, *args, **kwargs):
        serializer = TerminateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        date = serializer.validated_data.get("finish_at", None)
        users = serializer.validated_data.get("users", [])
        event = serializer.validated_data.get("event", None)

        if not users:
            logger.warning("Попытка удаления без пользователей")
            return Response(
                {"detail": _("There are no users to delete")},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if date:
            for user in users:
                UserSchedule.objects.update_or_create(
                    user=user,
                    event=event,
                    defaults={
                        "finish_at": date,
                        "updated_at": timezone.now(),
                    },
                )
            logger.info("#user_event_terminate_add_schedule, users: %s", users)
        else:
            for user in users:
                if event == UserSchedule.DEACTIVATE:
                    user.deactivate()
                elif event == UserSchedule.REMOVE:
                    user.delete()
                elif event == UserSchedule.DISMISS:
                    user.dismiss()
                elif event == UserSchedule.ACTIVATE:
                    user.activate()
            logger.info("#user_event_terminate_call_immediately, users: %s", users)
        return Response(serializer.data, status=status.HTTP_200_OK)


class GroupUsersView(APIView, CustomPagination):
    serializer_class = UserManagementSerializer
    permission_classes = [CanViewGroups]

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter("search", openapi.IN_QUERY, type=openapi.TYPE_STRING)
        ]
    )
    def get(self, request, *args, **kwargs):
        group = get_object_or_404(Group, pk=kwargs["pk"])

        statuses_qs = UserStatus.objects.filter(start_at__gte=timezone.now()).order_by(
            "start_at"
        )
        companies_qs = UserCompany.objects.select_related(
            "company", "department__parent", "department", "position"
        )

        qs = (
            User.objects.filter(groups__id=group.pk)
            .prefetch_related(
                Prefetch("statuses", queryset=statuses_qs),
                "roles",
                Prefetch("companies", queryset=companies_qs),
            )
            .select_related("country")
            .distinct()
            .order_by("id")
        )

        search = request.GET.get("search")
        if search:
            qs = qs.annotate(
                search_full_name=Concat(
                    "first_name", Value(" "), "last_name", output_field=CharField()
                )
            ).filter(Q(search_full_name__icontains=search) | Q(email__icontains=search))

        page = self.paginate_queryset(qs, request, view=self)
        serializer = self.serializer_class(page, many=True)
        return self.get_paginated_response(serializer.data)


class GroupViewSet(viewsets.ModelViewSet):
    queryset = Group.objects.all()
    serializer_class = GroupSerializer

    def get_queryset(self):
        return self.queryset.annotate(
            users_count=Count("user"),
        ).order_by("id")
