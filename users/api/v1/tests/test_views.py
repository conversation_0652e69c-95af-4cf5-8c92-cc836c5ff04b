from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase

User = get_user_model()


def create_and_get_test_user():
    return User.objects.create_user(
        username="testuser",
        email="<EMAIL>",
        password="Test.123",
        first_name="Test",
        last_name="Test",
        iin="123456789012",
        is_superuser=False,
    )


class MeViewTest(APITestCase):
    def setUp(self):
        self.user = create_and_get_test_user()
        self.url = reverse("me")

    def test_authenticated_user_can_view_me_view(self):
        self.client.force_login(user=self.user)
        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        self.assertEqual(data["id"], self.user.id)
        self.assertEqual(data["username"], self.user.username)
        self.assertEqual(data["email"], self.user.email)
        self.assertEqual(data["first_name"], self.user.first_name)
        self.assertEqual(data["last_name"], self.user.last_name)
        self.assertIn("roles", data)
        self.assertIsInstance(data["roles"], list)

    def test_unauthenticated_user_gets_401(self):
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)


class MeDetailedViewTest(APITestCase):
    def setUp(self):
        self.user = create_and_get_test_user()
        self.url = reverse("me-detailed")

    def test_authenticated_user_can_view_me_detailed_view(self):
        self.client.force_login(user=self.user)
        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertIn("all_permissions", data)
        self.assertIn("permission_codes", data)
        self.assertIn("role_codes", data)

    def test_unauthenticated_user_gets_401(self):
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
