from rest_framework import serializers

from content.api.v1.serializers.shared_serializers import (
    StatusDataSerializer, TagSerializer, DueDateSettingsSerializer
)
from content.models import Enrollment, Resource, Detail, EnrollmentProgress, Certification
from users.api.v1.serializers import UserMinimalSerializer


class CourseDetailSerializer(serializers.ModelSerializer):
    instructor = UserMinimalSerializer(read_only=True)
    tags = TagSerializer(many=True, read_only=True)

    class Meta:
        model = Detail
        fields = (
            "id",
            "recommended_time",
            "thumbnail",
            "cover",
            "instructor",
            "tags",
        )


class CourseResourceSerializer(serializers.ModelSerializer):
    detail = CourseDetailSerializer(read_only=True)

    class Meta:
        model = Resource
        fields = (
            "id",
            "name",
            "description",
            "type",
            "detail",
            "parent_id",
            "created_at",
            "updated_at",
        )


class CourseSerializer(serializers.ModelSerializer):
    resource = CourseResourceSerializer(read_only=True)
    status_data = serializers.SerializerMethodField()
    due_date_settings = DueDateSettingsSerializer(read_only=True)

    class Meta:
        model = Enrollment
        fields = (
            "id",
            "user_id",
            "resource",
            "access_date",
            "due_date_settings",
            "completed_at",
            "change_reason",
            "status_data",
        )

    def get_status_data(self, obj):
        status_data = {
            "status": obj.status,
            "progress": obj.progress,
            "spentTimeMilli": 0,
        }
        return StatusDataSerializer(status_data).data


class CountSerializer(serializers.Serializer):
    total = serializers.IntegerField(
        help_text="Общее количество курсов",
    )
    completed = serializers.IntegerField(
        help_text="Количество завершённых курсов",
    )
    active = serializers.IntegerField(
        help_text="Количество незавершенных курсов",
    )


class CoursesEnvelopeSerializer(serializers.Serializer):
    count = CountSerializer(required=True, help_text="Статистика по курсам")
    finished = CourseSerializer(
        many=True, read_only=True, help_text="Список завершенных курсов"
    )
    active = CourseSerializer(
        many=True, read_only=True, help_text="Список не завершенных курсов"
    )


class MyCourseInfoSerializer(serializers.ModelSerializer):
    detail = CourseDetailSerializer(read_only=True)
    status_data = serializers.SerializerMethodField()
    child_count = serializers.IntegerField(read_only=True)

    class Meta:
        model = Resource
        fields = [
            "id",
            "name",
            "description",
            "type",
            "detail",
            "status_data",
            "child_count",
            "payload",
        ]

    def get_status_data(self, obj):
        user = self.context["request"].user
        enrollment = Enrollment.objects.filter(
            user_id=user.id, resource_id=obj.id
        ).first()
        status_data = {
            "status": enrollment.status if enrollment else None,
            "progress": enrollment.progress if enrollment else None,
            "spentTimeMilli": 0,
        }
        return StatusDataSerializer(status_data).data


class MyCourseStructureSerializer(serializers.ModelSerializer):
    children = serializers.SerializerMethodField()
    status_data = serializers.SerializerMethodField()

    class Meta:
        model = Resource
        fields = [
            "id",
            "name",
            "description",
            "type",
            "order",
            "children",
            "status_data",
        ]

    def get_children(self, obj):
        """
        Возвращает отсортированные дочерние элементы
        """
        sorted_children = obj.children.order_by("order")
        return MyCourseStructureSerializer(
            sorted_children, many=True, context=self.context
        ).data

    def get_status_data(self, obj):
        user = self.context["request"].user
        main_enrollment = self.context.get("main_enrollment")

        if obj.parent_id and obj.parent.type != Resource.FOLDER:
            status_data = self._get_child_status_data(obj, user, main_enrollment)
        else:
            status_data = self._get_parent_status_data(obj, user, main_enrollment)

        return StatusDataSerializer(status_data).data

    def _get_parent_status_data(self, obj, user, enrollment):
        """Получить данные для родительского ресурса"""
        if not enrollment:
            enrollment = Enrollment.objects.filter(
                user=user, resource_id=obj.id
            ).first()

        if enrollment:
            return {
                "status": enrollment.status,
                "progress": enrollment.progress,
                "spentTimeMilli": 0,
            }
        else:
            return {"status": None, "progress": None, "spentTimeMilli": 0}

    def _get_child_status_data(self, obj, user, main_enrollment):
        """Получить данные для дочернего ресурса"""
        if not main_enrollment:
            main_enrollment = Enrollment.objects.filter(
                user=user, resource_id=obj.parent_id
            ).first()

        if main_enrollment:
            # Ищем прогресс дочернего ресурса
            try:
                child_progress = main_enrollment.progress_details.get(
                    resource_id=obj.id
                )
                return {
                    "status": child_progress.status,
                    "progress": child_progress.progress,
                    "spentTimeMilli": 0,
                }
            except EnrollmentProgress.DoesNotExist:
                # Дочерний ресурс еще не начат
                return {
                    "status": Enrollment.NOT_STARTED,
                    "progress": 0,
                    "spentTimeMilli": 0,
                }
        else:
            return {"status": None, "progress": None, "spentTimeMilli": 0}

class MyCourseStructureSwaggerSerializer(MyCourseStructureSerializer):
    status_data = StatusDataSerializer()

class UserCertificateSerializer(serializers.ModelSerializer):
    enrollment_id = serializers.UUIDField(source="enrollment.id", read_only=True)
    resource_name = serializers.CharField(
        source="enrollment.resource.name", read_only=True
    )

    class Meta:
        model = Certification
        fields = [
            "id",
            "path",
            "enrollment_id",
            "resource_name",
            "issue_date",
            "expired_date",
            "status",
        ]