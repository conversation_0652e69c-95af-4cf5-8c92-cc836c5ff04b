from django.contrib.auth import get_user_model
from django.db import models
from django.utils.translation import gettext_lazy as _

from content.models import Resource
from core.models import BaseUUIDModel

User = get_user_model()


class Question(BaseUUIDModel):
    author = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="questions",
        verbose_name=_("author"),
    )
    resource = models.ForeignKey(
        Resource,
        on_delete=models.CASCADE,
        related_name="questions",
        verbose_name=_("resource"),
    )
    text = models.TextField()
    is_answered = models.BooleanField(default=False)

    def __str__(self):
        return f"Question by {self.author} on {self.resource}"


class Answer(BaseUUIDModel):
    question = models.ForeignKey(
        Question,
        on_delete=models.CASCADE,
        related_name="answers",
        verbose_name=_("question"),
    )
    author = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name="answers",
        verbose_name=_("author"),
    )
    text = models.TextField()

    def __str__(self) -> str:
        return f"Answer to Question {self.question.id}"
