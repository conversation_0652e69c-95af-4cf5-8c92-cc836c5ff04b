import os
import uuid
import logging
from datetime import datetime

from django.core.validators import FileExtensionValidator
from django.db import models
from django.db.models.signals import post_delete, pre_save
from django.dispatch import receiver
from django.utils.translation import gettext_lazy as _
from lms.s3_storage import PublicMediaStorage

logger = logging.getLogger('app')

def upload_to(instance, filename):
    ext = filename.split('.')[-1]
    new_filename = f"{uuid.uuid4()}.{ext}"
    now = datetime.now().strftime('%Y/%d/%m/')
    return os.path.join("files/{}".format(now), new_filename)


class BaseUUIDModel(models.Model):
    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        abstract = True


class TimestampModel(models.Model):
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        abstract = True


class Media(BaseUUIDModel):
    path = models.FileField(
        storage=PublicMediaStorage(),
        upload_to=upload_to,
        validators=[FileExtensionValidator(allowed_extensions=[
            'jpg', 'jpeg', 'png', 'gif', 'pdf', 'xlsx', 'xls', 'csv',
            'pptx', 'doc', 'docx', 'mp4', 'mp3', 'avi', 'mov', 'zip',
            'tar', 'rar',
        ])]
    )
    size = models.PositiveIntegerField(
        help_text=_('file size in bytes'),
        editable=False,
        blank=True,
        null=True,
    )
    content_type = models.CharField(
        max_length=255,
        help_text=_('content type'),
        editable=False,
        blank=True,
        null=True,
    )

    def __str__(self):
        return self.path.name

    class Meta:
        verbose_name = _('media')
        verbose_name_plural = _('medias')
        ordering = ['id', ]
        db_table = 'core_medias'


@receiver(post_delete, sender=Media)
def delete_media_path(sender, instance, **kwargs):
    try:
        if instance.path:
            instance.path.delete(save=False)
            logger.info("media with id {} file deleted".format(instance.pk))
    except Exception as e:
        logger.error("error in delete media , the media id - {} with message - {}".format(instance.pk, str(e)))
        return False


@receiver(pre_save, sender=Media)
def pre_save_media(sender, instance, *args, **kwargs):
    """ instance old image file will delete from os """
    if not instance.pk:
        return False

    try:
        old_file = Media.objects.get(pk=instance.pk).path
    except Media.DoesNotExist:
        return False

    new_file = instance.path
    logger.info("media with id {} old file {}".format(instance.pk, old_file))
    logger.info("media with id {} new file {}".format(instance.pk, new_file))

    try:
        if bool(old_file) and not old_file.name == new_file.name:
            media_storage = PublicMediaStorage()
            if media_storage.exists(old_file.name):
                media_storage.delete(old_file.name)
                logger.info("media with id {} old file deleted".format(instance.pk))
    except Exception as e:
        logger.error("media with id {} file delete error {}".format(instance.pk, str(e)))
        return False
