from django.contrib.auth import get_user_model
from django.db import models
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase

from content.models import Resource
from reviews.models import Comment, Review

User = get_user_model()


def create_and_get_user() -> models.Model:
    return User.objects.create_user(
        username="testuser",
        email="<EMAIL>",
        password="Test.123",
        first_name="Test",
        last_name="Test",
        iin="123456789012",
        is_superuser=False,
    )


def create_and_get_resource(owner: models.Model) -> Resource:
    return Resource.objects.create(
        name="test", description="test", owner=owner, type=Resource.COURSE
    )


def create_and_get_hacker_user() -> models.Model:
    return User.objects.create_user(
        username="hacker",
        email="<EMAIL>",
        password="VeryStrongPassword",
        first_name="Hacker",
        last_name="Hackerovich",
        iin="777777777777",
        is_superuser=False,
    )


def create_and_get_review(resource: Resource, user: models, rating: int) -> Review:
    return Review.objects.create(
        resource=resource,
        user=user,
        rating=rating,
        title="Test",
    )


def create_and_get_child_comment(review: Review, user: models.Model) -> Comment:
    return Comment.objects.create(
        review=review, user=user, content="I am child `Comment`"
    )


def create_and_get_parent_comment(review: Review, user: models.Model) -> Comment:
    return Comment.objects.create(
        review=review, user=user, content="I am parent `Comment`"
    )


class ReviewViewTest(APITestCase):
    def setUp(self):
        self.rating = 5
        self.user: models.Model = create_and_get_user()
        self.resource: Resource = create_and_get_resource(owner=self.user)
        self.review: Review = create_and_get_review(
            resource=self.resource, user=self.user, rating=self.rating
        )
        self.parent_comment: Comment = create_and_get_parent_comment(
            review=self.review, user=self.user
        )
        self.child_comment: Comment = create_and_get_child_comment(
            review=self.review, user=self.user
        )
        self.body_data_for_review: dict = {
            "resource_id": self.resource.id,
            "rating": self.rating,
        }
        self.body_data_for_comment: dict = {"content": "Test"}
        self.query_params_filters_data_for_review: dict = {
            "resource_id": self.resource.id,
            "user": self.user.id,
            "rating": self.rating,
        }

    def test_create_review(self):
        self.url = reverse("review-list-create")
        self.client.force_login(user=self.user)
        self.review.delete()  # <- иначе будет ошибка: вы уже оставляли отзыв
        response = self.client.post(self.url, self.body_data_for_review, format="json")

        data = response.json()
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(data["resource"], self.body_data_for_review["resource_id"])
        self.assertEqual(data["rating"], self.body_data_for_review["rating"])
        self.assertEqual(data["user"], self.user.username)

    def test_not_auth_user_create_review(self):
        self.url = reverse("review-list-create")
        response = self.client.post(self.url, self.body_data_for_review, format="json")

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_list_review(self):
        self.url = reverse("review-list-create")
        self.client.force_login(user=self.user)
        response = self.client.get(self.url)

        data = response.json()
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(data["results"][0]["id"], self.review.id)

    def test_not_auth_user_list_review(self):
        """
        Not auth'ed users can view reviews in ReadOnly mode
        """
        self.url = reverse("review-list-create")
        response = self.client.get(self.url)

        data = response.json()
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(data["results"][0]["id"], self.review.id)

    def test_list_review_with_filters(self):
        self.url = reverse("review-list-create")
        self.client.force_login(user=self.user)
        response = self.client.get(
            self.url, query_params=self.query_params_filters_data_for_review
        )

        data = response.json()
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            data["results"][0]["resource"],
            self.query_params_filters_data_for_review["resource_id"],
        )
        self.assertEqual(data["results"][0]["user"], self.user.username)
        self.assertEqual(
            data["results"][0]["rating"],
            self.query_params_filters_data_for_review["rating"],
        )

    def test_get_review_detail_view(self):
        self.url = reverse("review-detail-view", kwargs={"pk": self.review.pk})
        self.client.force_login(user=self.user)
        response = self.client.get(self.url)

        data = response.json()
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(data["id"], self.review.id)
        self.assertEqual(data["resource"], self.resource.id)
        self.assertEqual(data["rating"], self.rating)

    def test_put_review_detail_view(self):
        self.url = reverse("review-detail-view", kwargs={"pk": self.review.pk})
        self.client.force_login(user=self.user)
        data_for_update = {
            "resource_id": self.resource.id,
            "rating": 1,
            "content": "SUCCESS",
        }
        response = self.client.put(self.url, data=data_for_update, format="json")

        data = response.json()
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(data["resource"], data_for_update["resource_id"])
        self.assertEqual(data["rating"], data_for_update["rating"])
        self.assertEqual(data["content"], data_for_update["content"])

    def test_patch_review_detail_view(self):
        self.url = reverse("review-detail-view", kwargs={"pk": self.review.pk})
        self.client.force_login(user=self.user)
        data_for_update = {
            "resource_id": self.resource.id,
            "rating": 1,
            "content": "SUCCESS",
        }
        response = self.client.patch(self.url, data=data_for_update, format="json")

        data = response.json()
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(data["resource"], data_for_update["resource_id"])
        self.assertEqual(data["rating"], data_for_update["rating"])
        self.assertEqual(data["content"], data_for_update["content"])

    def test_not_auth_user_update_review(self):
        self.url = reverse("review-detail-view", kwargs={"pk": self.review.pk})
        data_for_update = {
            "resource_id": self.resource.id,
            "rating": 1,
            "content": "SUCCESS",
        }
        response_put = self.client.put(self.url, data=data_for_update, format="json")
        response_patch = self.client.patch(
            self.url, data=data_for_update, format="json"
        )

        self.assertEqual(response_put.status_code, status.HTTP_403_FORBIDDEN)
        self.assertEqual(response_patch.status_code, status.HTTP_403_FORBIDDEN)

    def test_not_author_update_review(self):
        self.url = reverse("review-detail-view", kwargs={"pk": self.review.pk})
        hacker = create_and_get_hacker_user()
        self.client.force_login(user=hacker)
        data_for_update = {
            "resource_id": self.resource.id,
            "rating": 1,
            "content": "SUCCESS",
        }
        response_put = self.client.put(self.url, data=data_for_update, format="json")
        response_patch = self.client.patch(
            self.url, data=data_for_update, format="json"
        )

        self.assertEqual(response_put.status_code, status.HTTP_403_FORBIDDEN)
        self.assertEqual(response_patch.status_code, status.HTTP_403_FORBIDDEN)


class CommentViewTest(APITestCase):
    def setUp(self):
        self.rating = 5
        self.user: models.Model = create_and_get_user()
        self.resource: Resource = create_and_get_resource(owner=self.user)
        self.review: Review = create_and_get_review(
            resource=self.resource, user=self.user, rating=self.rating
        )
        self.parent_comment: Comment = create_and_get_parent_comment(
            review=self.review, user=self.user
        )
        self.child_comment: Comment = create_and_get_child_comment(
            review=self.review, user=self.user
        )
        self.body_data_for_review: dict = {
            "resource_id": self.resource.id,
            "rating": self.rating,
        }
        self.body_data_for_comment: dict = {"content": "Comment has been created"}
        self.query_params_filters_data_for_review: dict = {
            "resource_id": self.resource.id,
            "user": self.user.id,
            "rating": self.rating,
        }

    def test_create_comment(self):
        self.url = reverse("comment-list-create", kwargs={"review_id": self.review.id})
        self.client.force_login(user=self.user)
        response = self.client.post(self.url, self.body_data_for_comment, format="json")

        data = response.json()
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(data["content"], self.body_data_for_comment["content"])

    def test_not_auth_user_create_comment(self):
        self.url = reverse("comment-list-create", kwargs={"review_id": self.review.id})
        response = self.client.post(self.url, self.body_data_for_comment, format="json")

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_list_comment(self):
        self.url = reverse("comment-list-create", kwargs={"review_id": self.review.id})
        self.client.force_login(user=self.user)
        response = self.client.get(self.url)

        data = response.json()
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(data["results"][0]["id"], self.parent_comment.id)
        self.assertEqual(data["results"][0]["content"], self.parent_comment.content)
        self.assertEqual(data["results"][1]["id"], self.child_comment.id)
        self.assertEqual(data["results"][1]["content"], self.child_comment.content)

    def test_get_comment_detail_view(self):
        self.url = reverse("comment-detail-view", kwargs={"pk": self.parent_comment.pk})
        self.client.force_login(user=self.user)
        response = self.client.get(self.url)

        data = response.json()
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(data["id"], self.parent_comment.id)
        self.assertEqual(data["content"], self.parent_comment.content)

    def test_put_comment_detail_view(self):
        self.url = reverse("comment-detail-view", kwargs={"pk": self.parent_comment.pk})
        self.client.force_login(user=self.user)
        data_for_update = {"content": "SUCCESS"}
        response = self.client.put(self.url, data=data_for_update, format="json")

        data = response.json()
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(data["content"], data_for_update["content"])

    def test_patch_comment_detail_view(self):
        self.url = reverse("comment-detail-view", kwargs={"pk": self.parent_comment.pk})
        self.client.force_login(user=self.user)
        data_for_update = {"content": "SUCCESS"}
        response = self.client.patch(self.url, data=data_for_update, format="json")

        data = response.json()
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(data["content"], data_for_update["content"])

    def test_not_auth_user_update_comment(self):
        self.url = reverse("comment-detail-view", kwargs={"pk": self.parent_comment.pk})
        data_for_update = {"content": "SUCCESS"}
        response_put = self.client.put(self.url, data=data_for_update, format="json")
        response_patch = self.client.patch(
            self.url, data=data_for_update, format="json"
        )

        self.assertEqual(response_put.status_code, status.HTTP_403_FORBIDDEN)
        self.assertEqual(response_patch.status_code, status.HTTP_403_FORBIDDEN)

    def test_not_author_update_comment(self):
        self.url = reverse("comment-detail-view", kwargs={"pk": self.parent_comment.pk})
        hacker = create_and_get_hacker_user()
        self.client.force_login(user=hacker)
        data_for_update = {"content": "SUCESS"}

        response_put = self.client.put(self.url, data=data_for_update, format="json")
        response_patch = self.client.patch(
            self.url, data=data_for_update, format="json"
        )

        self.assertEqual(response_put.status_code, status.HTTP_403_FORBIDDEN)
        self.assertEqual(response_patch.status_code, status.HTTP_403_FORBIDDEN)
