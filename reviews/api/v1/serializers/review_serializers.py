from rest_framework import serializers
from rest_framework.exceptions import ValidationError

from content.models import Resource
from reviews.models import Review
from users.api.v1.serializers.user_serializers import UserSerializer


class ReviewSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    resource_id = serializers.PrimaryKeyRelatedField(
        queryset=Resource.objects.all(), write_only=True, source="resource"
    )

    class Meta:
        model = Review
        fields = [
            "id",
            "resource_id",
            "resource",
            "user",
            "rating",
            "title",
            "content",
            "created_at",
        ]
        read_only_fields = ["id", "user", "created_at", "resource"]

    def validate(self, attrs: dict):
        user = self.context["request"].user
        resource = attrs.get("resource")
        if (
            self.instance is None
            and Review.objects.filter(user=user, resource=resource).exists()
        ):
            raise ValidationError("Вы уже оставили отзыв на этот ресурс.")
        return attrs

    def create(self, validated_data):
        validated_data["user"] = self.context["request"].user
        return super().create(validated_data)
