from django.db.models import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON>,
    F,
    <PERSON>,
    OuterRef,
    Prefetch,
    QuerySet,
    Subquery,
    Value,
    When,
)
from django.http import StreamingHttpResponse
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import generics
from rest_framework.permissions import (
    AllowAny,
)
from rest_framework.response import Response
from rest_framework.views import APIView

from content.api.v1.filters import CourseFilter
from content.api.v1.serializers import (
    CoursesEnvelopeSerializer,
    MyCourseInfoSerializer,
    MyCourseStructureSerializer,
    MyCourseStructureSwaggerSerializer,
    UserCertificateSerializer,
)
from content.api.v1.utils import create_recursive_serializer
from content.models import Certification, Enrollment, Resource
from content.permissions.codes import CanViewEducationalMaterials
from content.permissions.course_permission import HasAccessToMyCourse
from core.services import S3Service
from lms.utils import CustomPagination, get_paginated_serializer
from users.permissions.codes import HasAccessToUsersPortal


class MyCoursesAPIView(generics.GenericAPIView):
    filter_backends = [DjangoFilterBackend]
    filterset_class = CourseFilter
    serializer_class = CoursesEnvelopeSerializer
    permission_classes = [CanViewEducationalMaterials, HasAccessToUsersPortal]
    pagination_class = None

    def get_queryset(self):
        if getattr(self, 'swagger_fake_view', False):
            return Enrollment.objects.none()

        # строим subquery чтобы вытщаить послдений Enrollment
        latest_access_date = (
            Enrollment.objects
            .filter(user=OuterRef("user"))
            .values("user")
            .annotate(max_date=Max("access_date"))
            .values("max_date")[:1]
        )
        return (
            Enrollment.objects
            .filter(user=self.request.user)
            .select_related(
                "resource",
                "resource__owner",
                "resource__detail",
                "resource__detail__instructor",
                "due_date_settings",
                "user",
            )
            .prefetch_related("resource__detail__tags")
            .annotate(
                # Аннотация: является ли самым недавним Enrollment
                is_latest=Case(
                    When(access_date=Subquery(latest_access_date), then=Value(True)),
                    default=Value(False),
                    output_field=BooleanField(),
                ),
                # Аннотация: есть ли у Enrollment дедлайн
                has_deadline=Case(
                    When(due_date_settings__date__isnull=False, then=Value(True)),
                    default=Value(False),
                    output_field=BooleanField(),
                ),
                deadline_value=F("due_date_settings__date"),
            )
            .order_by(
                # Сначала самый недавний Enrollment
                F("is_latest").desc(nulls_last=True),
                # Затем те Enrollment, где дедлайн не пустой
                F("has_deadline").desc(nulls_last=True),
                # Затем те Enrollment, где дедлайн далеко от сегодня
                F("deadline_value").asc(nulls_last=True),
                # Затем недавние
                F("access_date").desc(nulls_last=True),
            )
        )

    @swagger_auto_schema(responses={200: CoursesEnvelopeSerializer})
    def get(self, request, *args, **kwargs):
        """
        Вычисление по активным и законченным курсам идет здесь,
        шоб не делать лишние db query.
        """
        qs: QuerySet[Enrollment] = self.filter_queryset(self.get_queryset())
        completed: QuerySet[Enrollment] = [
            e for e in qs if e.status == Enrollment.FINISHED
        ]
        active: QuerySet[Enrollment] = [
            e for e in qs if e.status != Enrollment.FINISHED
        ]
        completed_count: int = len(completed)
        active_count: int = len(active)
        total_count: int = completed_count + active_count

        envelope: dict = {
            "count": {
                "total": total_count,
                "completed": completed_count,
                "active": active_count,
            },
            "finished": completed,
            "active": active,
        }
        serializer: CoursesEnvelopeSerializer = self.get_serializer(envelope)
        return Response(serializer.data)


class MyCourseInfoAPIView(generics.RetrieveAPIView):
    serializer_class = MyCourseInfoSerializer
    queryset = Resource.objects.all()
    permission_classes = [HasAccessToUsersPortal, HasAccessToMyCourse]
    lookup_url_kwarg = 'course_id'

    def get_queryset(self):
        return Resource.objects.select_related('detail__instructor').annotate(
            child_count=Count('children')
        )


class MyCourseStructureAPIView(generics.RetrieveAPIView):
    serializer_class = MyCourseStructureSerializer
    queryset = Resource.objects.all()
    # permission_classes = [HasAccessToUsersPortal, HasAccessToMyCourse]
    permission_classes = [HasAccessToUsersPortal]
    lookup_url_kwarg = 'course_id'

    def get_queryset(self):
        return Resource.objects.prefetch_related(
            Prefetch(
                'children',
                queryset=Resource.objects.order_by('order')
            )
        ).order_by('order')

    def get_serializer_context(self):
        """Передаем дополнительный контекст в сериализатор"""
        context = super().get_serializer_context()
        context['user'] = self.request.user

        course_id = self.kwargs.get('course_id')
        if course_id:
            try:
                enrollment = Enrollment.objects.select_related('user', 'resource').prefetch_related(
                    'progress_details__resource'
                ).get(
                    user=self.request.user,
                    resource_id=course_id
                )
                context['main_enrollment'] = enrollment
            except Enrollment.DoesNotExist:
                context['main_enrollment'] = None

        return context

    @swagger_auto_schema(
        responses={200: create_recursive_serializer(MyCourseStructureSwaggerSerializer)}
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

class DownloadTemplateAPIView(APIView):
    filter_backends = None
    authentication_classes = []
    pagination_class = None
    permission_classes = [AllowAny]

    @swagger_auto_schema(
        operation_summary="Скачать Шаблон",
        responses={
            200: openapi.Response(
                description="Шаблон сертификата",
                schema=openapi.Schema(
                    type=openapi.TYPE_FILE,
                    format=".pdf"
                )
            )
        }
    )
    def get(self, request, *args, **kwargs):
        s3 = S3Service()
        obj = s3.retrieve_file_from_s3('files/templates/certificates/certificate_template.pdf')
        file_stream = obj['Body']

        response = StreamingHttpResponse(file_stream, content_type=obj['ContentType'])
        response['Content-Length'] = str(obj['ContentLength'])
        response['Content-Disposition'] = 'attachment; filename="preview.pdf"'

        return response


class UserCertificatesAPIView(generics.ListAPIView):
    serializer_class = UserCertificateSerializer
    pagination_class = CustomPagination

    def get_queryset(self):
        if getattr(self, 'swagger_fake_view', False):
            return Certification.objects.none()
        return (
            Certification.objects
            .filter(user=self.request.user).select_related('enrollment', 'user', 'enrollment__resource')
        )

    @swagger_auto_schema(responses={200: get_paginated_serializer(serializer_class)})
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class UserCertificateListView(generics.ListAPIView):
    queryset = Certification.objects.all()
    pagination_class = CustomPagination
    serializer_class = UserCertificateSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ["user"]

    @swagger_auto_schema(responses={200: get_paginated_serializer(serializer_class)})
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    def get_queryset(self):
        return Certification.objects.select_related(
            "user", "enrollment", "enrollment__resource"
        )
