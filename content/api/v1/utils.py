from datetime import timedelta
from uuid import UUID

from dateutil.relativedelta import relativedelta
from django.forms import model_to_dict
from rest_framework import serializers
from django.db import connection

from content.models import Settings, DueDateSettings, Preference
from users.models import Department, User


def coerce_types(data):
    """
    Функция для проверки типов данных при валидации json схемы
    :param data: dict
    :return: dict: clean
    """
    if isinstance(data, dict):
        return {k: coerce_types(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [coerce_types(v) for v in data]
    elif isinstance(data, str) and data.isdigit():
        return int(data)
    return data


def get_settings_for_resource(resource_id: int, preference_code: str) -> dict:
        """
        Получение настроек для определенного ресурса
        :param resource_id: id ресурса
        :param resource_type: LONGREAD, CHAPTER, STAGE и т.д.
        :return:
        """
        settings_qs = Settings.objects.filter(
            resource_id=resource_id,
            preference__code=preference_code,
        ).first()

        if not settings_qs:
            return {}

        return model_to_dict(settings_qs)


def due_date(type, period_unit, period, enrollment, date):
    """
    Рассчитывает финальную дату дедлайна в зависимости от типа.
    """

    if type == DueDateSettings.TYPE_DUE_DATE:
        return date

    elif type == DueDateSettings.TYPE_DUE_PERIOD:
        if period is None or not period_unit:
            return None

        if period_unit == DueDateSettings.PERIOD_DAY:
            return enrollment.access_date + timedelta(days=period)
        elif period_unit == DueDateSettings.PERIOD_WEEK:
            return enrollment.access_date + timedelta(weeks=period)
        elif period_unit == DueDateSettings.PERIOD_MONTH:
            return enrollment.access_date + relativedelta(months=period)
        elif period_unit == DueDateSettings.PERIOD_YEAR:
            return enrollment.access_date + relativedelta(years=period)

    elif type == DueDateSettings.TYPE_DEFAULT:
        resource_type = enrollment.resource.type
        pref_code = f"{resource_type}_ACCESS_MANAGEMENT_DEFAULT_ASSIGNMENT_PARAMETERS"
        preference = Preference.objects.filter(
            section=Preference.ACCESS_CONTROL,
            code=pref_code
        ).first()
        if not preference:
            return None

        settings = Settings.objects.filter(
            resource_id=enrollment.resource_id,
            preference_id=preference.id
        ).first()

        if not settings:
            return None

        payload = settings.payload or {}
        term = payload.get("term")
        if term == DueDateSettings.TYPE_DUE_PERIOD:
            units = payload.get("units")
            value = payload.get("value")

            if not units or value is None:
                return None

            if units == DueDateSettings.PERIOD_DAY:
                return enrollment.access_date + timedelta(days=value)
            elif units == DueDateSettings.PERIOD_WEEK:
                return enrollment.access_date + timedelta(weeks=value)
            elif units == DueDateSettings.PERIOD_MONTH:
                return enrollment.access_date + relativedelta(months=value)
            elif units == DueDateSettings   .PERIOD_YEAR:
                return enrollment.access_date + relativedelta(years=value)

        elif term == DueDateSettings.TYPE_UNLIMITED:
            return None

    return None  # для UNLIMITED


def create_recursive_serializer(base_serializer_class):
    class_name = f"{base_serializer_class.__name__}Recursive"
    """
    Данная функция нужна только для корректного отображения документации для рекурсивных сериализаторов

    Под рекурсивными сериализаторами мы понимаем объект Resource.children = Resource
    """
    class DynamicRecursiveSerializer(base_serializer_class):
        children = serializers.ListSerializer(child=base_serializer_class())

        class Meta(base_serializer_class.Meta):
            ref_name = class_name

    return DynamicRecursiveSerializer


def get_recursive_supervised_user_ids(supervisor_id: int) -> list[int]:
    """
    Получить всех прямых и вложенных подчинённых для заданного руководителя.
    """
    query = """
            WITH RECURSIVE supervised_users AS (
                SELECT user_id
                FROM users_user_supervisors
                WHERE supervisor_id = %s

                UNION ALL

                SELECT s.user_id
                FROM users_user_supervisors s
                INNER JOIN supervised_users su ON su.user_id = s.supervisor_id
            )
            SELECT user_id FROM supervised_users
        """
    with connection.cursor() as cursor:
        cursor.execute(query, [supervisor_id])
        return [row[0] for row in cursor.fetchall()]


def user_is_supervisor_in_parent_department(user: User, department_id: UUID) -> bool:
    try:
        department = Department.objects.get(id=department_id)
    except Department.DoesNotExist:
        return False

    supervised_departments: set[Department] = set(
        Department.objects.filter(
            supervisors_in_department__supervisor=user,
        ).values_list("id", flat=True)
    )

    current = department
    while current is not None:
        if current.id in supervised_departments:
            return True # <- Пользователь является supervisor
        current = current.parent

    return False
