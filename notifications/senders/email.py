from lms.celery import send_mail_celery
from .base import BaseSender
import logging

logger = logging.getLogger('app')


class EmailSender(BaseSender):
    def send(self):
        send_mail_celery.delay(
            subject=self.notification.subject,
            message=self.notification.message,
            to=[email.strip() for email in (self.notification.recipient or "").split(",") if email.strip()],
        )
        logger.info(f"Sending Email ID {self.notification.pk} sent to - {self.notification.recipient}.")
