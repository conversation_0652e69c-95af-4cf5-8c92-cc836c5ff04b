from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.translation import gettext_lazy as _

from users.models import (
    Role, Country, Department, DepartmentSupervisor,
    Status, UserStatus, ContentType,
    Permission, User, UserSupervisor, UserSchedule, Position, Company, DepartmentPosition, UserCompany
)


class UserSupervisorInline(admin.TabularInline):
    model = UserSupervisor
    fk_name = 'user'
    extra = 1
    verbose_name = "Supervisor"
    verbose_name_plural = "Supervisors"

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('supervisor')


class UserCompanyInline(admin.TabularInline):
    model = UserCompany
    extra = 0
    autocomplete_fields = ('company', 'department', 'position')
    verbose_name = _('Company affiliation')
    verbose_name_plural = _('Company affiliations')

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'company', 'department', 'position'
        )


# admin.site.register(User, UserAdmin)
@admin.register(User)
class UserAdmin(BaseUserAdmin):
    inlines = (UserSupervisorInline, UserCompanyInline)
    readonly_fields = BaseUserAdmin.readonly_fields + ('id',)

    fieldsets = BaseUserAdmin.fieldsets + (
        (_('Additional info'), {
            'fields': (
                'iin',
                'middle_name',
                'phone',
                'country',
                'birth_date',
                'about',
                'photo',
                'roles',
            ),
        }),
    )

    add_fieldsets = BaseUserAdmin.add_fieldsets + (
        (_('Additional info'), {
            'fields': (
                'id',
                'iin',
                'middle_name',
                'phone',
                'country',
                'birth_date',
                'about',
                'photo',
                'roles',
            ),
        }),
    )

    list_display = (
        'username',
        'email',
        'first_name',
        'last_name',
        'middle_name',
        'iin',
        'country',
        'phone',
        'is_staff',
    )
    list_filter = (
        'is_staff',
        'is_superuser',
        'is_active',
        'country',
        'roles',
    )
    search_fields = (
        'username',
        'first_name',
        'middle_name',
        'last_name',
        'email',
        'phone',
        'iin',
    )
    autocomplete_fields = ('country',)
    filter_horizontal = (
        'groups',
        'user_permissions',
        'roles',
    )

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return (
            qs
            .select_related('country')
            .prefetch_related(
                'companies__company',
                'companies__department',
                'companies__position',
                'roles',
                'groups',
                'user_permissions',
                'supervisors',
            )
        )

@admin.register(Country)
class CountryAdmin(admin.ModelAdmin):
    list_display = ('id', 'name')
    search_fields = ('id', 'name')
    list_display_links = ('name',)
    show_full_result_count = False


@admin.register(UserStatus)
class UserStatusAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'status',)
    list_display_links = ('user',)
    autocomplete_fields = ('user',)
    show_full_result_count = False


@admin.register(Company)
class CompanyAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'name',
        'code',
        'is_active',
        'created_at',
        'updated_at',
    )
    list_display_links = ('name',)
    search_fields = ('name', 'code')
    list_filter = ('is_active', 'created_at', 'updated_at')
    readonly_fields = ('id', 'created_at', 'updated_at')
    fieldsets = (
        (None, {
            'fields': ('name', 'code', 'is_active'),
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',),
        }),
    )


class DepartmentSupervisorTabularInline(admin.TabularInline):
    model = DepartmentSupervisor
    autocomplete_fields = ('supervisor',)


class DepartmentPositionTabularInline(admin.TabularInline):
    model = DepartmentPosition
    autocomplete_fields = ('position',)
    extra = 0
    verbose_name = 'Position assignment'
    verbose_name_plural = 'Position assignments'


@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'ID1C',
        'IDParent1C',
        'company',
        'name',
        'code',
        'parent',
        'created_at',
        'updated_at',
    )
    list_display_links = ('name',)
    prepopulated_fields = {'code': ('name',)}
    search_fields = (
        'id',
        'ID1C',
        'IDParent1C',
        'company__name',
        'name',
        'code',
    )
    list_filter = (
        'company',
        'parent',
        'created_at',
        'updated_at',
    )
    readonly_fields = (
        'id',
        'ID1C',
        'IDParent1C',
        'created_at',
        'updated_at',
    )
    autocomplete_fields = ('parent',)
    inlines = [
        DepartmentSupervisorTabularInline,
        DepartmentPositionTabularInline,
    ]
    show_full_result_count = False
    fieldsets = (
        (None, {
            'fields': (
                'company',
                'name',
                'code',
                'parent',
            ),
        }),
        ('Идентификатор 1C и временные метки', {
            'fields': (
                'ID1C',
                'IDParent1C',
                'created_at',
                'updated_at',
            ),
            'classes': ('collapse',),
        }),
    )

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.select_related('parent', 'company')
        return queryset


@admin.register(Position)
class PositionAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'ID1C',
        'company',
        'name',
        'is_boss',
        'created_at',
        'updated_at',
    )
    list_display_links = ('name',)
    list_editable = ('is_boss',)
    search_fields = (
        'name',
        'ID1C',
        'company__name',
    )
    list_filter = (
        'company',
        'is_boss',
        'created_at',
        'updated_at',
    )
    readonly_fields = (
        'id',
        'ID1C',
        'created_at',
        'updated_at',
    )
    autocomplete_fields = ('company',)
    fieldsets = (
        (None, {
            'fields': (
                'company',
                'name',
                'is_boss',
            ),
        }),
        ('Идентификатор 1C и временные метки', {
            'fields': (
                'ID1C',
                'created_at',
                'updated_at',
            ),
            'classes': ('collapse',),
        }),
    )


@admin.register(Status)
class StatusAdmin(admin.ModelAdmin):
    list_display = ('id', 'name')
    list_display_links = ('name',)
    search_fields = ('name',)
    show_full_result_count = False


@admin.register(Permission)
class PermissionAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'code', 'parent')
    list_display_links = ('name',)
    search_fields = ['name']
    prepopulated_fields = {'code': ('name',)}
    autocomplete_fields = ('parent', 'dependencies')
    show_full_result_count = False

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.select_related('parent')
        return queryset


@admin.register(ContentType)
class ContentTypeAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'code')
    list_display_links = ('name',)
    search_fields = ('name',)
    prepopulated_fields = {'code': ('name',)}
    show_full_result_count = False
    autocomplete_fields = ('permissions',)


@admin.register(Role)
class RoleAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'code', 'is_deletable', 'has_department', 'users_count')
    search_fields = ('id', 'name',)
    list_display_links = ('name',)
    prepopulated_fields = {'code': ('name',)}
    autocomplete_fields = ('permissions',)
    show_full_result_count = False


@admin.register(UserSupervisor)
class UserSupervisorAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'supervisor', 'type',)
    list_display_links = ('user',)
    autocomplete_fields = ('user', 'supervisor')
    show_full_result_count = False


@admin.register(UserSchedule)
class UserScheduleAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'finish_at', 'event')
    autocomplete_fields = ('user',)
    show_full_result_count = False
    search_fields = ('user__username',)

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.select_related('user')
        return queryset
