from django.db.models import Count
from django_filters.rest_framework import Django<PERSON>ilt<PERSON><PERSON><PERSON>end
from drf_yasg.utils import swagger_auto_schema
from rest_framework import generics
from rest_framework.permissions import IsAuthenticated

from lms.utils import CustomPagination, get_paginated_serializer
from qa.api.v1.serializers.question_serializers import (
    QuestionCreateSerializer,
    QuestionDetailSerializer,
    QuestionListSerializer,
)
from qa.models import Question
from qa.permissions.codes import IsQuestionAuthor


class QuestionListView(generics.ListAPIView):
    serializer_class = QuestionListSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ["resource", "author", "is_answered"]
    pagination_class = CustomPagination

    def get_queryset(self):
        return Question.objects.select_related(
            "author", "resource", "resource__owner"
        ).annotate(answers_count=Count("answers", distinct=True)).order_by('-created_at')

    @swagger_auto_schema(responses={200: get_paginated_serializer(serializer_class)})
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class QuestionCreateView(generics.CreateAPIView):
    queryset = Question.objects.all()
    serializer_class = QuestionCreateSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        serializer.save(author=self.request.user)


class QuestionDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Question.objects.all()
    serializer_class = QuestionDetailSerializer
    permission_classes = [IsAuthenticated, IsQuestionAuthor]

    def get_queryset(self):
        return Question.objects.select_related("author", "resource", "resource__owner")
