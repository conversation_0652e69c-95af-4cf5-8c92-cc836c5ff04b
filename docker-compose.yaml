services:

  postgres:
    image: repka.kaspi.kz:8443/postgres:17.0
    container_name: postgres
    restart: always
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_ROOT_PASSWORD: ${POSTGRES_ROOT_PASSWORD}
      FRONTEND_BASE_URL: ${FRONTEND_BASE_URL}
    healthcheck:
      test: [ "CMD-SHELL", "sh -c 'pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}'" ]
      interval: 10s
      timeout: 3s
      retries: 3
    ports:
      - "5432:5432"

  redis:
    image: repka.kaspi.kz:8443/redis:7.4.2
    container_name: redis
    hostname: redis
    restart: always
    command: redis-server --requirepass "pass"
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  elasticsearch:
    image: repka.kaspi.kz:8443/elasticsearch:7.17.22
    container_name: elasticsearch
    restart: always
    ports:
      - "9200:9200"
      - "9300:9300"
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local
  elasticsearch_data:
    driver: local