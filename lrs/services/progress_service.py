import logging

from django.db import transaction
from django.utils import timezone

from content.models import Resource, Enrollment, EnrollmentProgress

logger = logging.getLogger('app')

class ProgressService:
    @staticmethod
    def update_progress(user_id, resource_id, progress, status=None, lrs_statement_id=None):
        """
        Универсальный метод для обновления прогресса
        Работает как для standalone ресурсов, так и для дочерних элементов
        """
        try:
            with transaction.atomic():
                resource = Resource.objects.get(id=resource_id)

                # Если это дочерний ресурс, ищем назначение по родительскому ресурсу
                if resource.parent_id and resource.parent.type != Resource.FOLDER:
                    enrollment = Enrollment.objects.filter(
                        user_id=user_id,
                        resource_id=resource.parent_id
                    ).first()

                    if enrollment:
                        # Обновляем прогресс дочернего ресурса
                        ProgressService._update_child_progress(
                            enrollment, resource_id, progress, status, lrs_statement_id
                        )
                        # Пересчитываем прогресс родительского ресурса
                        ProgressService._update_parent_progress(enrollment)
                    else:
                        logger.warning(
                            'Enrollment not found for parent resource: user_id: %s; parent_resource_id: %s;',
                            user_id, resource.parent_id
                        )
                else:
                    # Это standalone ресурс, обновляем напрямую
                    enrollment = Enrollment.objects.filter(
                        user_id=user_id,
                        resource_id=resource_id
                    ).first()

                    if enrollment:
                        ProgressService._update_enrollment_directly(
                            enrollment, progress, status
                        )
                    else:
                        logger.warning(
                            'Enrollment not found: user_id: %s; resource_id: %s;',
                            user_id, resource_id
                        )

        except Resource.DoesNotExist:
            logger.error(f'Resource not found: {resource_id}')
        except Exception as e:
            logger.error(f'Error updating progress: {e}')

    @staticmethod
    def _update_child_progress(enrollment, resource_id, progress, status, lrs_statement_id):
        """Обновить прогресс дочернего ресурса"""
        progress_obj, created = EnrollmentProgress.objects.get_or_create(
            enrollment=enrollment,
            resource_id=resource_id,
            defaults={
                'progress': progress,
                'status': status or Enrollment.NOT_STARTED,
                'lrs_statement_id': lrs_statement_id
            }
        )

        if not created:
            progress_obj.progress = progress
            if status:
                progress_obj.status = status
            if lrs_statement_id:
                progress_obj.lrs_statement_id = lrs_statement_id

            # Обновляем времена
            if progress > 0 and not progress_obj.started_at:
                progress_obj.started_at = timezone.now()
            if progress == 100 and not progress_obj.completed_at:
                progress_obj.completed_at = timezone.now()

            progress_obj.save()

        logger.info(
            f"Child progress updated: enrollment_id={enrollment.id}, resource_id={resource_id}, progress={progress}")

    @staticmethod
    def _update_enrollment_directly(enrollment, progress, status):
        """Обновить прогресс назначения напрямую (для standalone ресурсов)"""
        enrollment.progress = progress
        if status:
            enrollment.status = status

        if progress == 100 and not enrollment.completed_at:
            enrollment.completed_at = timezone.now()

        enrollment.save()
        logger.info(f"Direct enrollment progress updated: enrollment_id={enrollment.id}, progress={progress}")

    @staticmethod
    def _update_parent_progress(enrollment):
        """Пересчитать и обновить прогресс родительского ресурса"""
        children_progress = enrollment.progress_details.all()

        if not children_progress.exists():
            return

        # Рассчитываем общий прогресс
        total_progress = sum(p.progress for p in children_progress)
        average_progress = total_progress // children_progress.count()

        # Определяем общий статус
        statuses = [p.status for p in children_progress]

        if all(s == Enrollment.FINISHED for s in statuses):
            new_status = Enrollment.FINISHED
        elif any(s == Enrollment.IN_PROGRESS for s in statuses):
            new_status = Enrollment.IN_PROGRESS
        elif all(s == Enrollment.NOT_STARTED for s in statuses):
            new_status = Enrollment.NOT_STARTED
        else:
            new_status = Enrollment.IN_PROGRESS

        # Обновляем только если значения изменились
        if enrollment.progress != average_progress or enrollment.status != new_status:
            enrollment.progress = average_progress
            enrollment.status = new_status

            if average_progress == 100 and not enrollment.completed_at:
                enrollment.completed_at = timezone.now()

            enrollment.save(update_fields=['progress', 'status', 'completed_at'])
            logger.info(
                f"Parent progress updated: enrollment_id={enrollment.id}, progress={average_progress}, status={new_status}")



