from django.db.models import Count, F
from django.shortcuts import get_object_or_404
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from jsonpatch import <PERSON><PERSON><PERSON><PERSON>, JsonPatchException
from rest_framework import generics, status
from rest_framework.exceptions import ParseError, PermissionDenied
from rest_framework.filters import Ordering<PERSON><PERSON>er, SearchFilter
from rest_framework.generics import (
    ListAPIView,
    RetrieveAPIView,
)
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from content.api.v1.filters import ResourceFilter
from content.api.v1.serializers.shared_serializers import CatalogResourceSerializer
from content.models import Catalog, Detail, Preference, Resource, Settings
from content.permissions.codes import (
    CanCreateAndManageProjectInTrainingMaterialsSection,
)
from content.utils.utils import build_prefetch_chain
from lms.utils import CustomPagination, get_paginated_serializer

from ..serializers import ResourceSerializer
from ..serializers.resource_serializers import (
    CatalogSerializer,
    DetailUpdateSerializer,
    PreferenceSerializer,
    ResourceCreateSerializer,
    ResourceRetrieveSerializer,
    ResourceTreeSerializer,
    ResourceUpdateSerializer,
    SettingsDetailSerializer,
    SettingsMapSerializer,
)
from ..utils import create_recursive_serializer

MAX_DEPTH = 5


class ResourceListView(generics.ListAPIView):
    queryset = Resource.objects.all()
    serializer_class = ResourceSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter]
    search_fields = ["name",]
    filterset_class = ResourceFilter
    pagination_class = CustomPagination

    def get_queryset(self):
        queryset = self.queryset.select_related('owner', 'parent')
        return queryset

    @swagger_auto_schema(responses={200: get_paginated_serializer(serializer_class)})
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class ResourceCreateView(generics.CreateAPIView):
    queryset = Resource.objects.all()
    serializer_class = ResourceCreateSerializer
    permission_classes = [CanCreateAndManageProjectInTrainingMaterialsSection]


class ResourceTreeListView(ListAPIView):
    """
    Возвращает иерархию ресурсов (максимум 5 уровней).
    """
    serializer_class = ResourceTreeSerializer
    # permission_classes = [CanCreateAndManageProjectInTrainingMaterialsSection] #TODO: разделить логику
    pagination_class = None

    @swagger_auto_schema(responses={200: create_recursive_serializer(serializer_class)})
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    def get_queryset(self):
        return Resource.objects.prefetch_related(
            build_prefetch_chain('children', Resource, MAX_DEPTH)
        ).filter(parent__isnull=True).select_related('owner').order_by('order')


class ResourceTypeListView(APIView):
    permission_classes = [CanCreateAndManageProjectInTrainingMaterialsSection]

    @swagger_auto_schema(
        operation_description="Get list of resource type choices",
        responses={
            200: openapi.Response(
                description="List of resource choices",
                schema=openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        properties={
                            "key": openapi.Schema(type=openapi.TYPE_STRING),
                            "value": openapi.Schema(type=openapi.TYPE_STRING),
                        },
                        required=["key", "value"],
                    ),
                ),
            )
        },
    )
    def get(self, request, *args, **kwargs):
        choices = [{'key': key, 'value': value} for key, value in Resource.RESOURCE_CHOICES]
        return Response(choices, status=status.HTTP_200_OK)


class ResourceRetrieveDestroyView(generics.RetrieveDestroyAPIView):
    queryset = Resource.objects.select_related('owner', 'detail')
    # permission_classes = [CanCreateAndManageProjectInTrainingMaterialsSection] #TODO: разделить логику
    serializer_class = ResourceRetrieveSerializer

    def perform_destroy(self, instance: Resource):
        if instance.owner != self.request.user:
            raise PermissionDenied("You do not have permission to delete this book.")
        instance.soft_delete()


class ResourceTreeRetrieveView(RetrieveAPIView):
    """
    Возвращает иерархию ресурса по ID (максимум 5 уровней).
    """
    serializer_class = ResourceTreeSerializer
    # permission_classes = [CanCreateAndManageProjectInTrainingMaterialsSection] #TODO: разделить логику
    queryset = Resource.objects.all()
    pagination_class = None

    @swagger_auto_schema(responses={200: create_recursive_serializer(serializer_class)})
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    def get_object(self):
        return Resource.objects.prefetch_related(
            build_prefetch_chain('children', Resource, MAX_DEPTH)
        ).select_related('owner').get(pk=self.kwargs['pk'])


class ResourceUpdateView(generics.UpdateAPIView):
    queryset = Resource.objects.all()
    serializer_class = ResourceUpdateSerializer
    permission_classes = [CanCreateAndManageProjectInTrainingMaterialsSection]

    def perform_update(self, serializer):
        instance = self.get_object()
        if instance.owner != self.request.user and not self.request.user.is_superuser:
            raise PermissionDenied("You do not have permission to update this resource.")
        serializer.save()


class SettingsListView(generics.ListAPIView):
    queryset = Settings.objects.all()
    serializer_class = SettingsDetailSerializer
    permission_classes = [CanCreateAndManageProjectInTrainingMaterialsSection]
    pagination_class = None

    def get_queryset(self):
        if getattr(self, 'swagger_fake_view', False):
            return Settings.objects.none()

        return self.queryset.filter(
            resource_id=self.kwargs['pk'],
        ).order_by('order')


class ResourcePayloadUpdateView(generics.GenericAPIView):
    queryset = Resource.objects.all()
    permission_classes = [CanCreateAndManageProjectInTrainingMaterialsSection]
    lookup_field = 'pk'

    @swagger_auto_schema(
        request_body=openapi.Schema(
            type=openapi.TYPE_ARRAY,
            items=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                required=["op", "path"],
                properties={
                    "op": openapi.Schema(type=openapi.TYPE_STRING, description="Операция: add / replace / remove"),
                    "path": openapi.Schema(type=openapi.TYPE_STRING, description="Путь к полю JSON (RFC 6901)"),
                    "value": openapi.Schema(type=openapi.TYPE_STRING, description="Новое значение")
                }
            )
        ),
        operation_description="Применяет JSON Patch операции к полю payload"
    )
    def patch(self, request, *args, **kwargs):
        instance = self.get_object()

        if not isinstance(request.data, list):
            raise ParseError("Ожидается массив JSON Patch операций")

        try:
            patch = JsonPatch(request.data)
            updated_payload = patch.apply(instance.payload)
        except (JsonPatchException, ValueError) as e:
            raise ParseError(f"Invalid JSON Patch: {str(e)}")

        instance.payload = updated_payload
        instance.save(update_fields=["payload"])

        return Response({"success": True}, status=200)


class PreferenceListView(generics.ListAPIView):
    queryset = Preference.objects.all()
    serializer_class = PreferenceSerializer
    permission_classes = [CanCreateAndManageProjectInTrainingMaterialsSection]
    pagination_class = None
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['section', 'code', ]

    def get_queryset(self):
        pref_type = self.kwargs.get('type')
        return self.queryset.filter(type=pref_type)


class SettingsListUpdateView(APIView):
    permission_classes = [CanCreateAndManageProjectInTrainingMaterialsSection]

    @swagger_auto_schema(
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            additional_properties=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={},
            )
        ),
    )
    def post(self, request, *args, **kwargs):
        serializer = SettingsMapSerializer(data=request.data, context={'resource_id': kwargs['pk']})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(status=status.HTTP_200_OK)


class DetailUpdateView(APIView):
    permission_classes = [CanCreateAndManageProjectInTrainingMaterialsSection]

    @swagger_auto_schema(request_body=DetailUpdateSerializer)
    def put(self, request, *args, **kwargs):
        instance = get_object_or_404(Detail, pk=kwargs['detail_id'])

        serializer = DetailUpdateSerializer(data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.update(instance, serializer.validated_data)

        return Response(status=status.HTTP_200_OK)


class CatalogListView(generics.ListAPIView):
    queryset = Catalog.objects.all()
    serializer_class = CatalogSerializer
    pagination_class = CustomPagination
    filter_backends = [DjangoFilterBackend, OrderingFilter, SearchFilter]
    search_fields = ["name"]
    ordering = [
        "order",
    ]
    filterset_fields = [
        "name",
    ]
    ordering_fields = [
        "name",
        "order",
    ]

    def get_queryset(self):
        return Catalog.objects.annotate(
            resources_count=Count("catalog_resources", distinct=True),
        )

    @swagger_auto_schema(responses={200: get_paginated_serializer(serializer_class)})
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class ResourceRestoreView(generics.UpdateAPIView):
    queryset = Resource.objects.all()
    serializer_class = ResourceRetrieveSerializer

    def update(self, request, *args, **kwargs):
        instance: Resource = self.get_object()
        instance.restore()
        return super().update(request, partial=True)


class CatalogDetailView(generics.RetrieveAPIView):
    queryset = Catalog.objects.all()
    serializer_class = CatalogSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Catalog.objects.annotate(
            resources_count=Count("catalog_resources", distinct=True),
        )


class CatalogResourceView(generics.ListAPIView):
    queryset = Resource.objects.all()
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticated]
    serializer_class = CatalogResourceSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter]
    search_fields = ["name"]

    def get_queryset(self):
        catalog_id: int = self.kwargs.get("pk")
        return (
            Resource.objects.filter(resource_catalogs__catalog_id=catalog_id)
            .select_related("owner", "parent")
            .annotate(status=F("enrollment__status"))
        )

    @swagger_auto_schema(responses={200: get_paginated_serializer(serializer_class)})
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)