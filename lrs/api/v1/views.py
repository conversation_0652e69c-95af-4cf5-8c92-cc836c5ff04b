from datetime import datetime

import orj<PERSON> as json
import logging
from django.conf import settings
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from tincan import StateDocument, RemoteLRS, Activity, Agent, ActivityDefinition, AgentAccount

from content.api.v1.utils import get_settings_for_resource
from content.models import Enrollment, Resource
from content.tasks.enrollment import generate_certificate
from lrs.api.v1.serializers import SetStateRequestSerializer, GetStateRequestSerializer, StatementRequestSerializer
from lrs.services.progress_service import ProgressService

logger = logging.getLogger('app')


class SetStateView(APIView):

    serializer_class = SetStateRequestSerializer

    @staticmethod
    def get_lrs() -> RemoteLRS:
        return RemoteLRS(
            version='1.0.3',
            endpoint=settings.LRS_URL,
            username=settings.LRS_USERNAME,
            password=settings.LRS_PASSWORD,
        )

    @staticmethod
    def get_serializer_data(serializer_class, source_data):
        serializer = serializer_class(data=source_data)
        serializer.is_valid(raise_exception=True)
        return serializer

    def save_state(self, request) -> Response:
        serializer = self.get_serializer_data(SetStateRequestSerializer, request.data)

        agent = serializer.get_agent()
        activity = serializer.get_activity()
        stateId = serializer.validated_data['stateId']
        state = serializer.validated_data['state']

        doc = StateDocument(
            activity=activity,
            agent=agent,
            id=stateId,
            content=json.dumps(state).decode('utf-8'),
        )
        response = self.get_lrs().save_state(doc)

        if response.success and response.response.code != 404:
            user_id = doc.agent.account.name
            activity_id = activity.id.split('.')[1]

            logger.info(f"LRS State saved - USER_ID: {user_id}, Resource_ID: {activity_id}")

            ProgressService.update_progress(
                user_id=user_id,
                resource_id=activity_id,
                progress=state.get('totalScore', 0),
                status=state.get('status'),
                lrs_statement_id=doc.id
            )

            self._check_and_generate_certificate(user_id, activity_id, state)

        return Response(status=status.HTTP_201_CREATED)

    def _check_and_generate_certificate(self, user_id, resource_id, state):
        """Проверить и сгенерировать сертификат если нужно"""
        try:
            resource = Resource.objects.get(id=resource_id)

            if resource.parent_id and resource.parent.type != Resource.FOLDER:
                # Это дочерний ресурс, проверяем родительский enrollment
                enrollment = Enrollment.objects.filter(
                    user_id=user_id,
                    resource_id=resource.parent_id
                ).first()
                resource_for_settings = resource.parent
            else:
                # Это standalone ресурс
                enrollment = Enrollment.objects.filter(
                    user_id=user_id,
                    resource_id=resource_id
                ).first()
                resource_for_settings = resource

            if enrollment and enrollment.progress == 100:
                settings = get_settings_for_resource(
                    resource_for_settings.pk,
                    f'{resource_for_settings.type}_END_OF_COURSE'
                )
                if settings.get('payload', {}).get('certification', False):
                    generate_certificate(enrollment.id, settings)
                    logger.info(f"Certificate generation initiated for enrollment {enrollment.id}")

        except Resource.DoesNotExist:
            logger.error(f'Resource not found for certificate check: {resource_id}')
        except Exception as e:
            logger.error(f'Error checking certificate generation: {e}')

    @swagger_auto_schema(
        tags=['lrs'],
        manual_parameters=[
            openapi.Parameter(
                'agent',
                openapi.IN_QUERY,
                type=openapi.TYPE_OBJECT,
                description='Информация о пользователе "{"account": {"homePage": "location","name": "user_id"}}"'
            ),
            openapi.Parameter(
                'stateId',
                openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description='ID состояния'
            ),
            openapi.Parameter(
                'activityId',
                openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description='ID активности'
            ),
        ]
    )
    def get(self, request, *args, **kwargs):
        try:
            agent_json = request.query_params.get('agent')
            agent = json.loads(agent_json) if agent_json else None
        except json.JSONDecodeError:
            return Response({'error': 'agent должен быть валидным JSON'}, status=400)

        data = {
            'agent': agent,
            'activityId': request.query_params.get('activityId'),
            'stateId': request.query_params.get('stateId'),
        }

        serializer = GetStateRequestSerializer(data=data)
        serializer.is_valid(raise_exception=True)

        agent_obj = serializer.get_agent()
        activity_obj = serializer.get_activity()

        result = self.get_lrs().retrieve_state(activity_obj, agent_obj, serializer.validated_data["stateId"])

        if result.data:
            data = json.loads(result.content.content.decode('utf-8'))
        else:
            data = {}

        return Response(data)


    @swagger_auto_schema(tags=["lrs"], request_body=SetStateRequestSerializer)
    def post(self, request, *args, **kwargs):
        return self.save_state(request)

    @swagger_auto_schema(tags=["lrs"], request_body=SetStateRequestSerializer)
    def put(self, request, *args, **kwargs):
        return self.save_state(request)


class SendStatementsView(APIView):

    serializer_class = StatementRequestSerializer

    @staticmethod
    def get_lrs() -> RemoteLRS:
        return RemoteLRS(
            version='1.0.3',
            endpoint=settings.LRS_URL,
            username=settings.LRS_USERNAME,
            password=settings.LRS_PASSWORD,
        )

    @staticmethod
    def get_serializer_data(serializer_class, source_data):
        serializer = serializer_class(data=source_data)
        serializer.is_valid(raise_exception=True)
        return serializer

    @swagger_auto_schema(tags=["lrs"], request_body=StatementRequestSerializer)
    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer_data(StatementRequestSerializer, request.data)
        self.get_lrs().save_statement(serializer.get_statement())
        return Response(status=status.HTTP_202_ACCEPTED)