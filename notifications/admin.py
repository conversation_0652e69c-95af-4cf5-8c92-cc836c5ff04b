from django.contrib import admin

from notifications.models import Notification, NotificationTemplate, NotificationReceiver, UINotification
from django import forms
from django.urls import path, reverse
from django.shortcuts import redirect, render

from notifications.services import NotificationTemplateService


# Register your models here.
@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    list_display = ('channel', 'status', 'subject', 'sent_time')
    list_filter = ('status', 'channel',)
    search_fields = ('id', 'subject',)
    ordering = ('-id',)


@admin.register(UINotification)
class UINotificationAdmin(admin.ModelAdmin):
    list_display = ('user', 'subject', 'is_read', 'read_at')
    list_filter = ('is_read',)
    search_fields = ('user__first_name', 'user__last_name', 'user__username', 'subject',)
    ordering = ('-created_at',)


@admin.register(NotificationReceiver)
class NotificationReceiverAdmin(admin.ModelAdmin):
    list_display = ('id', 'recipient', 'channel', 'code', 'is_active', 'created_at', 'updated_at')
    search_fields = ('id', 'recipient', 'code',)
    autocomplete_fields = ('user',)
    list_filter = ('channel',)
    list_display_links = ('recipient',)
    list_editable = ('is_active',)
    ordering = ('-id',)


class NotificationContextForm(forms.Form):
    context_data = forms.JSONField(
        label='Аргументы для Context (JSON)',
        required=True,
    )
    receivers = forms.ModelMultipleChoiceField(
        queryset=NotificationReceiver.objects.filter(is_active=True),
        label='Получатели',
        required=False,
        widget=admin.widgets.FilteredSelectMultiple('Получатели', is_stacked=False),
    )


@admin.register(NotificationTemplate)
class NotificationTemplateAdmin(admin.ModelAdmin):
    list_display = ('id', 'label', 'channel', 'title', 'is_active', 'created_at', 'updated_at')
    search_fields = ('id', 'label', 'title',)
    list_filter = ('channel',)
    list_display_links = ('label',)
    list_editable = ('is_active',)
    ordering = ('-id',)

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('create_notifications/', self.admin_site.admin_view(self.create_notifications_view),
                 name='create-notifications'),
        ]
        return custom_urls + urls

    @admin.action(description="Создать уведомления с контекстом")
    def create_notifications_action(self, request, queryset):
        selected = queryset.values_list('id', flat=True)
        return redirect(
            '{}?{}'.format(
                reverse('admin:create-notifications'),
                '&'.join(f'_selected_action={id}' for id in selected)
            )
        )

    actions = [create_notifications_action]

    def create_notifications_view(self, request):
        selected_ids = request.POST.getlist('_selected_action') or request.GET.getlist('_selected_action')
        queryset = NotificationTemplate.objects.filter(id__in=selected_ids)

        if request.method == 'POST':
            form = NotificationContextForm(request.POST)
            if form.is_valid():
                context_data = form.cleaned_data['context_data']
                receivers = form.cleaned_data['receivers']
                for template in queryset:
                    service = NotificationTemplateService(
                        template_code=template.code,
                        context=context_data,
                        receivers=receivers if receivers.exists() else None,
                    )
                    service.generate_notifications()
                self.message_user(request, "Уведомления успешно созданы!")
                changelist_url = reverse('admin:notifications_notificationtemplate_changelist')
                return redirect(changelist_url)
        else:
            form = NotificationContextForm()

        return render(request, 'admin/notifications/notificationtemplate/create_notifications_form.html',
                      context={'form': form, 'queryset': queryset})
