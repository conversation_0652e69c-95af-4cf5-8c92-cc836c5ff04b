from django.urls import path

from content.api.v1.views import (
    GroupedSupervisedDepartmentListView,
    StatisticsCourseExpiredView,
    StatisticsCourseFinishedView,
    StatisticsCourseProgressView,
    SupervisorDashboardAPIView,
    SupervisorDashboardEnrollmentsAPIView,
    SupervisorDashboardMemberAPIView,
    UserSupervisorSettingsViewSet,
)

urlpatterns = [
    path('', SupervisorDashboardAPIView.as_view()),
    path('grouped', GroupedSupervisedDepartmentListView.as_view()),
    path('settings', UserSupervisorSettingsViewSet.as_view()),
    path('member', SupervisorDashboardMemberAPIView.as_view()),
    path('enrollments', SupervisorDashboardEnrollmentsAPIView.as_view()),
    path('statistics/course_progress', StatisticsCourseProgressView.as_view()),
    path('statistics/course_finished', StatisticsCourseFinishedView.as_view()),
    path('statistics/course_expired', StatisticsCourseExpiredView.as_view()),
]