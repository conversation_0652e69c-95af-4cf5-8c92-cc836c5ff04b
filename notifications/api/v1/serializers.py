from rest_framework import serializers

from notifications.models import UINotification
from users.api.v1.serializers import UserSerializer


class UINotificationSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    read_at = serializers.DateTimeField(read_only=True, format='%Y-%m-%d %H:%M:%S')

    class Meta:
        model = UINotification
        fields = ('id', 'user', 'subject', 'message', 'payload', 'is_read', 'read_at')
