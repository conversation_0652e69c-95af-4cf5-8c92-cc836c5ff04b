from django.shortcuts import get_object_or_404
from drf_yasg.utils import swagger_auto_schema
from rest_framework import generics
from rest_framework.permissions import (
    IsAuthenticatedOrReadOnly,
)

from lms.utils import CustomPagination, get_paginated_serializer
from reviews.api.v1.permissions.comment_permissions import IsCommentAuthor
from reviews.api.v1.serializers.comment_serializers import CommentSerializer
from reviews.models import Comment, Review


class CommentListCreateAPIView(generics.ListCreateAPIView):
    serializer_class = CommentSerializer
    permission_classes = [IsAuthenticatedOrReadOnly]
    pagination_class = CustomPagination

    def get_queryset(self):
        if getattr(self, "swagger_fake_view", False):
            return Comment.objects.none()
        return (
            Comment.objects.filter(
                review_id=self.kwargs["review_id"],
                parent__isnull=True,  # Только корневые
            )
            .select_related("review", "user")
            .prefetch_related("replies")
            .order_by("created_at")
        )

    def perform_create(self, serializer):
        review = get_object_or_404(Review, pk=self.kwargs["review_id"])
        serializer.save(user=self.request.user, review=review)

    @swagger_auto_schema(responses={200: get_paginated_serializer(serializer_class)})
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class CommentRetrieveUpdateDestroyAPIView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Comment.objects.all()
    serializer_class = CommentSerializer
    permission_classes = [IsCommentAuthor]
