```shell
python manage.py loaddata \
        users/fixtures/groups.json \
        users/fixtures/statuses.json  \
        users/fixtures/countries.json  \
        users/fixtures/departments.json  \
        users/fixtures/permissions.json \
        users/fixtures/content-types.json \
        users/fixtures/roles.json \
        users/fixtures/users.json  \
        users/fixtures/user-statuses.json \
        content/fixtures/resources.json \
        content/fixtures/participants.json \
        content/fixtures/tags.json \
        content/fixtures/preferences.json \
        content/fixtures/catalogs.json \
        content/fixtures/details.json
        
```

```shell
celery -A lms worker --loglevel=info
```

```shell
celery -A lms beat --loglevel=info
```

```shell
sudo apt install gettext
django-admin makemessages -l ru
django-admin compilemessages
```

```json
{
  "1": {
    "notify": true,
    "data": {
      "notification": {
        "subject": "",
        "body": ""
      }
    }
  },
  "2": {
    "notify": true,
    "data": {
      "notification": {
        "subject": "",
        "body": ""
      },
      "intervals": [
        {
          "units": "m",
          "value": 1
        },
        {
          "units": "h",
          "value": 2
        },
        {
          "units": "d",
          "value": 3
        }
      ]
    }
  },
  "3": {
    "notify": true,
    "data": {
      "notification": {
        "subject": "",
        "body": ""
      },
      "intervals": [
        {
          "units": "d",
          "value": 1
        },
        {
          "units": "w",
          "value": 2
        }
      ]
    }
  },
  "4": {
    "notify": true,
    "data": {
      "notification": {
        "subject": "",
        "body": ""
      },
      "users": [
        {
          "name": "",
          "email": ""
        }
      ]
    }
  }
}


```