from django.db.models import Char<PERSON><PERSON>, Prefetch, Q, Value
from django.db.models.functions import Concat
from django.shortcuts import get_object_or_404
from django.utils import timezone
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import generics, status, viewsets
from rest_framework.response import Response
from rest_framework.views import APIView

from content.api.v1.utils import create_recursive_serializer
from content.utils.utils import build_prefetch_chain
from lms.utils import CustomPagination
from users.api.v1.serializers.department_serializers import (
    CreateUpdateDepartmentSerializer,
    DepartmentChildrenSerializer,
    DepartmentRetrieveSerializer,
    DepartmentTreeSerializer,
    UsersMoveToSerializer,
)
from users.api.v1.serializers.shared_serializers import UserManagementSerializer
from users.api.v1.utils import prefetch_department_children
from users.models import Department, DepartmentSupervisor, User, UserCompany, UserStatus
from users.permissions.codes import (
    CanCreateAndEditDepartments,
    CanDeleteDepartments,
    CanViewDepartments,
)

MAX_DEPTH = 5


class DepartmentTreeView(generics.ListAPIView):
    """
    Дерево департаментов без количества сотрудников.
    Максимальная вложенность до 5(включительно)
    """

    serializer_class = DepartmentTreeSerializer
    permission_classes = [CanViewDepartments]
    pagination_class = None

    @swagger_auto_schema(responses={200: create_recursive_serializer(serializer_class)})
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    def get_queryset(self):
        return (
            Department.objects.filter(parent_id__isnull=True)
            .prefetch_related(
                build_prefetch_chain("children", Department, MAX_DEPTH),
                "supervisors_in_department",
            )
            .order_by("id")
        )

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = MAX_DEPTH
        return context


class DepartmentUsersView(APIView, CustomPagination):
    permission_classes = [CanViewDepartments]

    @swagger_auto_schema(
        responses={
            200: openapi.Response(
                description="List of users for <Department ID>",
                schema=UserManagementSerializer,
            )
        },
        manual_parameters=[
            openapi.Parameter("search", openapi.IN_QUERY, type=openapi.TYPE_STRING)
        ],
    )
    def get(self, request, *args, **kwargs):
        department = get_object_or_404(Department, pk=kwargs["pk"])
        dept_ids = department.get_descendant_ids_cte()

        # Предвыбираем только актуальные статусы пользователя
        statuses_qs = UserStatus.objects.filter(start_at__gte=timezone.now()).order_by(
            "start_at"
        )

        # И заранее подтягиваем связи для UserCompany
        companies_qs = UserCompany.objects.select_related(
            "company", "department__parent", "department", "position"
        )

        # Базовый queryset: фильтруем по companies__department, prefetch+select, distinct
        qs = (
            User.objects.filter(companies__department__id__in=dept_ids)
            .prefetch_related(
                Prefetch("statuses", queryset=statuses_qs),
                "roles", "groups",
                Prefetch("companies", queryset=companies_qs),
            )
            .select_related("country")
            .distinct()
            .order_by("id")
        )

        # Поиск по ФИО или email
        search = request.GET.get("search")
        if search:
            qs = qs.annotate(
                search_full_name=Concat(
                    "first_name", Value(" "), "last_name", output_field=CharField()
                )
            ).filter(Q(search_full_name__icontains=search) | Q(email__icontains=search))

        page = self.paginate_queryset(qs, request, view=self)
        serializer = UserManagementSerializer(page, many=True)
        return self.get_paginated_response(serializer.data)


class UsersMoveToDepartment(APIView):
    serializer_class = UsersMoveToSerializer
    permission_classes = [CanCreateAndEditDepartments]

    @swagger_auto_schema(
        request_body=UsersMoveToSerializer,
    )
    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data, status=status.HTTP_200_OK)


class DepartmentViewSet(viewsets.ModelViewSet):
    queryset = Department.objects.all()
    pagination_class = None
    serializer_class = DepartmentRetrieveSerializer

    def get_permissions(self):
        if self.action in ["list", "retrieve"]:
            permission_classes = [CanViewDepartments]
        elif self.action in ["create", "update"]:
            permission_classes = [CanCreateAndEditDepartments]
        elif self.action in ["delete"]:
            permission_classes = [CanDeleteDepartments]
        return [permission() for permission in permission_classes]

    def retrieve(self, request, *args, **kwargs):
        department = get_object_or_404(
            Department.objects.prefetch_related(
                "supervisors_in_department",
            ),
            pk=kwargs["pk"],
        )
        serializer = self.serializer_class(department)

        return Response(serializer.data)

    @swagger_auto_schema(
        responses={
            200: create_recursive_serializer(DepartmentChildrenSerializer),
        }
    )
    def list(self, request, *args, **kwargs):
        """
        Максимальная вложенность до 5(включительно)
        """
        queryset = self.filter_queryset(self.get_queryset())
        serializer = DepartmentChildrenSerializer(queryset, many=True)

        return Response(serializer.data)

    def get_queryset(self):
        queryset = (
            Department.objects
            .filter(parent_id__isnull=True)
            .prefetch_related(
                Prefetch(
                    "supervisors_in_department",
                    queryset=DepartmentSupervisor.objects.select_related(
                        "supervisor"
                    ),
                ),
                prefetch_department_children(depth=5),
            )
        )
        return queryset


    def create(self, request, *args, **kwargs):
        serializer = CreateUpdateDepartmentSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)

        return Response(
            serializer.data, status=status.HTTP_201_CREATED, headers=headers
        )

    def update(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = CreateUpdateDepartmentSerializer(instance, data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        return Response(serializer.data)
