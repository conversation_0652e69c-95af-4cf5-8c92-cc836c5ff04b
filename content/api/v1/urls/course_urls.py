from django.urls import path

from content.api.v1.views import (
    MyCoursesAPIView,
    MyCourseInfoAPIView,
    MyCourseStructureAPIView,
    DownloadTemplateAPIView,
    UserCertificatesAPIView,
    UserCertificateListView,
)

urlpatterns = [
    path('my-courses', MyCoursesAPIView.as_view()),
    path('my-courses/<uuid:course_id>/course-info', MyCourseInfoAPIView.as_view()),
    path('my-courses/<uuid:course_id>/structure', MyCourseStructureAPIView.as_view()),
    path('show_template', DownloadTemplateAPIView.as_view()),
    path('my-certificates', UserCertificatesAPIView.as_view()),
    path('certificates', UserCertificateListView.as_view()),
]