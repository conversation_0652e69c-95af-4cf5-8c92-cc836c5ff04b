from datetime import date
from celery import shared_task
from users.models import UserSchedule
from .base import logger


@shared_task
def users_schedule():
    try:
        schedules = UserSchedule.objects.filter(
            finish_at=date.today(),
        )
        for schedule in schedules:
            if schedule.event == UserSchedule.DEACTIVATE:
                schedule.user.deactivate()
            elif schedule.event == UserSchedule.REMOVE:
                schedule.user.delete()
            elif schedule.event == UserSchedule.DISMISS:
                schedule.user.dismiss()
            elif schedule.event == UserSchedule.ACTIVATE:
                schedule.user.activate()
            schedule.delete()
            logger.info('#users_schedule event: %s, %s', schedule.event, schedule.user.id)
    except Exception as e:
        logger.error('#users_schedule error: %s', e)