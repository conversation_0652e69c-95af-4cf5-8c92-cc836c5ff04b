from django.db import transaction
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers

from users.api.v1.serializers.user_serializers import UserCreateSerializer
from users.models import Department, Permission, Role, UserRoleDepartment


class RoleSerializer(serializers.ModelSerializer):
    """Сериализатор для создания и обновления ролей."""

    class Meta:
        model = Role
        fields = ("id", "name", "code", "description", "is_deletable")


class UserRolesRequestSerializer(serializers.Serializer):
    roles = serializers.PrimaryKeyRelatedField(queryset=Role.objects.all(), many=True)
    departments = serializers.PrimaryKeyRelatedField(
        queryset=Department.objects.all(), many=True, required=False
    )

    def validate(self, data):
        roles = data.get("roles", [])
        departments = data.get("departments", [])

        if roles:
            for role in roles:
                if role.has_department and not departments:
                    raise serializers.ValidationError(
                        {"departments": _("Required field")}
                    )

        return data

    def update(self, instance, validated_data):
        roles = validated_data.get("roles", [])
        departments = validated_data.get("departments", [])
        with transaction.atomic():
            instance.roles.clear()
            instance.role_departments.all().delete()
            instance.roles.add(UserCreateSerializer.DEFAULT_ROLE_ID)
            for role in roles:
                instance.roles.add(role)
                if role.has_department:
                    for department in departments:
                        UserRoleDepartment.objects.get_or_create(
                            user=instance, role=role, department=department
                        )
        return validated_data


class RoleManagementSerializer(serializers.ModelSerializer):
    class Meta:
        model = Role
        fields = ("id", "name", "code")


class RoleQuerySerializer(serializers.Serializer):
    roles = serializers.ListField(child=serializers.IntegerField())


class RoleCreateSerializer(serializers.ModelSerializer):
    permissions = serializers.PrimaryKeyRelatedField(
        queryset=Permission.objects.all(), many=True
    )

    class Meta:
        model = Role
        fields = ("id", "name", "code", "description", "permissions")


class RoleUpdateSerializer(serializers.ModelSerializer):
    permissions = serializers.ListField(child=serializers.IntegerField())

    class Meta:
        model = Role
        fields = ("id", "name", "code", "description", "permissions")


class RoleRetrieveSerializer(serializers.ModelSerializer):
    """Сериализатор для детального просмотра ролей."""

    is_deletable = serializers.BooleanField(read_only=True)
    permissions = serializers.SlugRelatedField(
        many=True, slug_field="code", read_only=True
    )

    class Meta:
        model = Role
        fields = ("id", "name", "code", "description", "is_deletable", "permissions")


class RoleListSerializer(serializers.ModelSerializer):
    """Сериализатор для списка ролей."""

    is_deletable = serializers.BooleanField(read_only=True)
    users_count = serializers.IntegerField(
        read_only=True
    )  # Добавил явно, чтобы было понятно

    class Meta:
        model = Role
        fields = ("id", "name", "code", "description", "is_deletable", "users_count")
