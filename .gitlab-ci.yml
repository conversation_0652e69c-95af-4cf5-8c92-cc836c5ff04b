stages:
  - build
  - deploy
  - cleanup

variables:
  GIT_SSL_NO_VERIFY: '1'
  DOCKER_IMAGE: 'repka.kaspi.kz:8443/docker:28.0.4'
  DOCKER_TLS_CERTDIR: '/certs'
  CACHE_COMPRESSION_LEVEL: "fastest"
  PIP_CACHE_DIR: "$CI_PROJECT_DIR/.pip-cache"
  DOCKER_BUILDKIT: 1

image: $DOCKER_IMAGE

before_script:
  - echo $CI_REGISTRY
  - mkdir -p /etc/docker/certs.d/registry.gl.hq.bc
  - echo "${SUB_CERT}" >> /etc/docker/certs.d/registry.gl.hq.bc/ca.crt
  - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN $CI_REGISTRY

Build:
  stage: build
  script:
    - docker build -t "$CI_REGISTRY_IMAGE/$CI_COMMIT_REF_SLUG:$CI_COMMIT_SHORT_SHA" --cache-from=$CI_REGISTRY_IMAGE --build-arg BUILDKIT_INLINE_CACHE=1 .
    - docker push "$CI_REGISTRY_IMAGE/$CI_COMMIT_REF_SLUG:$CI_COMMIT_SHORT_SHA"
  cache:
    key:
      files:
      - requirements.txt
      - package.json
    paths:
      - node_modules/
      - $CI_PROJECT_DIR/.pip-cache/
      - .venv/
    policy: pull-push
  tags:
    - shared
  rules:
    - if: '$CI_COMMIT_REF_NAME == "stage" || $CI_COMMIT_REF_NAME == "master"'

Deploy:stage:
  stage: deploy
  script:
    - docker compose -f docker-compose.stage.yaml pull
    - docker stack deploy -c docker-compose.stage.yaml --with-registry-auth edu-stage-api
  tags:
    - stage
  only:
    - stage

Deploy:production:
  stage: deploy
  script:
    - docker compose -f docker-compose.prod.yaml pull
    - docker stack deploy -c docker-compose.prod.yaml --with-registry-auth edu-api
  tags:
    - kaspi_university
  only:
    - master

# Cleanup на shared раннере (для сборки)
Cleanup:shared:
  stage: cleanup
  when: always
  script:
    - docker rm $(docker ps -a -f status=exited -q) || true
    - docker system prune -af
    - |
      docker images \
        --filter=reference="${CI_REGISTRY_IMAGE}/${CI_COMMIT_REF_SLUG}:*" \
        --format '{{.ID}}' \
      | xargs -r docker rmi -f
  only:
    - master
    - stage
  tags:
    - shared

# Cleanup на stage раннере (для деплоя)
Cleanup:stage:
  stage: cleanup
  when: always
  script:
    - docker rm $(docker ps -a -q --filter status=exited)
    - docker system prune -af
  only:
    - stage
  tags:
    - stage

# Cleanup на production раннере
Cleanup:production:
  stage: cleanup
  when: always
  script:
    - docker rm $(docker ps -a -q --filter status=exited)
    - docker system prune -af
  only:
    - master
  tags:
    - kaspi_university