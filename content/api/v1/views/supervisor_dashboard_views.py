from datetime import datetime

from django.db import connection
from django.db.models import (
    <PERSON>,
    ExpressionWrapper,
    F,
    FloatField,
    Prefetch,
    Q,
    QuerySet,
    Subquery,
    OuterRef,
)
from django.db.models.functions import Greatest
from django.shortcuts import get_object_or_404
from django.utils import timezone
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import generics
from rest_framework.filters import SearchFilter
from rest_framework.generics import (
    ListAPIView,
    RetrieveAPIView,
    RetrieveUpdateAPIView,
)
from rest_framework.permissions import (
    IsAuthenticated,
)
from rest_framework.response import Response

from content.api.v1.serializers.course_serializers import CoursesEnvelopeSerializer
from content.api.v1.serializers.supervisor_dashboard_serializers import (
    GroupedSupervisedDepartmentListSerializer,
    StatisticsCourseExpiredSerializer,
    StatisticsCourseFinishedSerializer,
    StatisticsCourseProgressSerializer,
)
from content.api.v1.utils import (
    create_recursive_serializer,
    get_recursive_supervised_user_ids,
    user_is_supervisor_in_parent_department,
)
from content.models import Enrollment
from lms.utils import CustomPagination, get_paginated_serializer
from users.models import (
    Department,
    DepartmentSupervisor,
    User,
    UserCompany,
    UserSupervisorSettings,
)

from ..serializers import (
    DashboardMemberSerializer,
    SupervisedUserSerializer,
    UserSupervisorSettingsSerializer,
)
from . import MyCoursesAPIView


class SupervisorDashboardAPIView(ListAPIView):
    serializer_class = SupervisedUserSerializer
    pagination_class = CustomPagination
    filter_backends = [SearchFilter]
    permission_classes = [IsAuthenticated]
    search_fields = ["full_name", "email", "username"]

    def get_queryset(self) -> QuerySet[User]:
        user: User = self.request.user
        settings, _ = UserSupervisorSettings.objects.get_or_create(user=user)
        supervised_users_ids: list[int] = get_recursive_supervised_user_ids(user.id)

        enroll_qs: QuerySet[Enrollment] = Enrollment.objects.select_related(
            "resource"
        ).filter(~Q(status=Enrollment.FINISHED))

        companies_qs: QuerySet[UserCompany] = UserCompany.objects.select_related(
            'company',
            'department__parent',
            'department',
            'position'
        )

        qs: QuerySet[User] = (
            User.objects
            .filter(id__in=supervised_users_ids)
            .annotate(
                total_enrollments=Count('enrollment', distinct=True),
                finished_enrollments=Count(
                    'enrollment',
                    filter=Q(enrollment__status=Enrollment.FINISHED),
                    distinct=True
                ),
                not_finished_manually_count=Count(
                    'enrollment',
                    filter=Q(enrollment__status=Enrollment.NOT_FINISHED_MANUALLY),
                    distinct=True
                ),
                expired_enrollments_count=Count(
                    'enrollment',
                    filter=Q(enrollment__due_date_settings__date__lte=datetime.now()),
                    distinct=True
                ),
            )
            .annotate(
                progress_percent=ExpressionWrapper(
                    100.0 * F('finished_enrollments') / Greatest(F('total_enrollments'), 1),
                    output_field=FloatField()
                )
            )
            .prefetch_related(
                Prefetch('enrollment_set', queryset=enroll_qs, to_attr='not_finished_enrollments'),
                Prefetch('companies', queryset=companies_qs, to_attr='user_companies'),
            )
            .distinct()
        )

        filter_type = self.request.query_params.get('training_status')

        if filter_type == 'low':
            qs = qs.order_by('progress_percent')
        elif filter_type == 'high':
            qs = qs.order_by('-progress_percent')

        department_id: int = self.request.query_params.get("department_id")
        if department_id:
            qs = qs.filter(companies__department__id=department_id)

        finished: str = self.request.query_params.get("finished")
        if finished == "true":
            qs = qs.filter(enrollment__status=Enrollment.FINISHED)

        not_finished: str = self.request.query_params.get("not_finished")
        if not_finished == "true":
            qs = qs.filter(
                Q(enrollment__status=Enrollment.NOT_STARTED)
                | Q(enrollment__status=Enrollment.IN_PROGRESS)
            )

        not_logged_in: str = self.request.query_params.get("not_logged_in")
        if not_logged_in == "true":
            qs = qs.filter(last_login__isnull=True)

        expired_courses: str = self.request.query_params.get("expired_courses")
        if expired_courses == "true":
            qs = qs.filter(enrollment__due_date_settings__date__lt=timezone.now())

        expired_certificates: str = self.request.query_params.get("expired_certificates")
        if expired_certificates == "true":
            qs = qs.filter(enrollment__certification__expired_date__lt=timezone.now())

        return qs

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "training_status",
                openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Статус обученности (низкая/высокая)",
            ),
            openapi.Parameter(
                "department_id",
                openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="UUID департамента",
            ),
            openapi.Parameter(
                "finished",
                openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                description="Завершенные курсы",
            ),
            openapi.Parameter(
                "not_finished",
                openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                description="Незавершенные курсы",
            ),
            openapi.Parameter(
                "not_logged_in",
                openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                description="Не заходил на портал",
            ),
            openapi.Parameter(
                "expired_courses",
                openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                description="Просроченные курсы",
            ),
            openapi.Parameter(
                "expired_certificates",
                openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                description="Просроченные сертификаты",
            ),
        ],
        responses={200: get_paginated_serializer(serializer_class)},
    )
    def get(self, request, *args, **kwargs):
        return self.list(request, *args, **kwargs)


class GroupedSupervisedDepartmentListView(generics.ListAPIView):
    queryset = Department.objects.all()
    serializer_class = GroupedSupervisedDepartmentListSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = CustomPagination
    filter_backends = [SearchFilter]
    search_fields = [
        "name",
    ]

    def _get_all_descendant_mappings(self, department_ids: list) -> dict[int, list[int]]:
        """
        Get all descendant mappings for multiple departments in a single query.
        Returns dict: {parent_id: [descendant_id1, descendant_id2, ...]}
        """
        if not department_ids:
            return {}

        placeholders = ','.join(['%s'] * len(department_ids))

        with connection.cursor() as cursor:
            cursor.execute(f"""
                WITH RECURSIVE department_tree AS (
                    -- Base case: all departments we're interested in
                    SELECT id as root_id, id as descendant_id
                    FROM users_department
                    WHERE id IN ({placeholders})

                    UNION ALL

                    -- Recursive case: find children
                    SELECT dt.root_id, d.id as descendant_id
                    FROM users_department d
                    INNER JOIN department_tree dt ON d.parent_id = dt.descendant_id
                )
                SELECT root_id, descendant_id FROM department_tree
                ORDER BY root_id, descendant_id;
            """, department_ids)

            results = cursor.fetchall()

        # Group results by root department
        mappings = {}
        for root_id, descendant_id in results:
            if root_id not in mappings:
                mappings[root_id] = []
            mappings[root_id].append(descendant_id)

        return mappings

    def get_queryset(self):
        user: User = self.request.user
        department_id = self.request.query_params.get("department_id")

        if department_id:
            if user_is_supervisor_in_parent_department(user.id, department_id):
                base_departments = Department.objects.filter(parent_id=department_id)
            else:
                return Department.objects.none()
        else:
            base_departments = Department.objects.filter(
                supervisors_in_department__supervisor=user,
            )

        # Get all department IDs we need to process
        base_dept_list = list(base_departments.select_related("company", "parent"))
        if not base_dept_list:
            return Department.objects.none()

        base_dept_ids = [dept.id for dept in base_dept_list]

        # Get all descendant mappings in a single query
        descendant_mappings = self._get_all_descendant_mappings(base_dept_ids)
        # Calculate statistics for all departments at once
        all_descendant_ids = []
        for descendant_list in descendant_mappings.values():
            all_descendant_ids.extend(descendant_list)

        # Remove duplicates while preserving order
        unique_descendant_ids = list(dict.fromkeys(all_descendant_ids))

        # Get all user companies for descendant departments in one query
        user_companies_stats = (
            UserCompany.objects
            .filter(department_id__in=unique_descendant_ids)
            .select_related('user')
            .prefetch_related('user__enrollments')
            .values('department_id')
            .annotate(
                user_count=Count('user', distinct=True),
                total_enrollments=Count('user__enrollments', distinct=True),
                finished_enrollments=Count(
                    'user__enrollments',
                    filter=Q(user__enrollments__status=Enrollment.FINISHED),
                    distinct=True,
                ),
                not_finished_manually_count=Count(
                    'user__enrollments',
                    filter=Q(user__enrollments__status=Enrollment.NOT_FINISHED_MANUALLY),
                    distinct=True,
                ),
                not_finished_enrollments_count=Count(
                    'user__enrollments',
                    filter=~Q(user__enrollments__status=Enrollment.FINISHED),
                    distinct=True,
                ),
                expired_enrollments_count=Count(
                    'user__enrollments',
                    filter=Q(user__enrollments__due_date_settings__date__lte=timezone.now()),
                    distinct=True,
                ),
            )
        )

        # Create a mapping of department_id -> stats
        dept_stats_map = {item['department_id']: item for item in user_companies_stats}

        # Aggregate stats for each base department
        departments_with_stats = []
        for dept in base_dept_list:
            descendant_ids = descendant_mappings.get(dept.id, [])

            # Aggregate stats from all descendant departments
            aggregated_stats = {
                'user_count': 0,
                'total_enrollments': 0,
                'finished_enrollments': 0,
                'not_finished_manually_count': 0,
                'not_finished_enrollments_count': 0,
                'expired_enrollments_count': 0,
            }

            for desc_id in descendant_ids:
                if desc_id in dept_stats_map:
                    stats = dept_stats_map[desc_id]
                    for key in aggregated_stats:
                        aggregated_stats[key] += stats.get(key, 0)

            # Calculate progress percentage
            total_enrollments = aggregated_stats['total_enrollments']
            finished_enrollments = aggregated_stats['finished_enrollments']
            progress_percent = (
                100.0 * finished_enrollments / max(total_enrollments, 1)
                if total_enrollments > 0 else 0.0
            )

            # Add calculated stats to department object
            for key, value in aggregated_stats.items():
                setattr(dept, key, value)
            setattr(dept, 'progress_percent', progress_percent)

            departments_with_stats.append(dept)

        # Create final queryset
        dept_ids = [dept.id for dept in departments_with_stats]

        qs = (
            Department.objects
            .filter(id__in=dept_ids)
            .select_related("company", "parent")
            .prefetch_related(
                Prefetch(
                    "supervisors_in_department",
                    queryset=DepartmentSupervisor.objects.select_related(
                        "department", "supervisor"
                    ),
                ),
                Prefetch(
                    "children",
                    queryset=Department.objects.select_related(
                        "parent", "company"
                    ).prefetch_related("children", "supervisors"),
                ),
                "supervisors",
            )
        )

        # Apply pre-calculated statistics
        stats_map = {dept.id: dept for dept in departments_with_stats}

        original_iterator = qs._iterator
        def stats_iterator(use_chunked_fetch=False, chunk_size=2000):
            for dept in original_iterator(use_chunked_fetch, chunk_size):
                if dept.id in stats_map:
                    stats_dept = stats_map[dept.id]
                    dept.user_count = getattr(stats_dept, 'user_count', 0)
                    dept.total_enrollments = getattr(stats_dept, 'total_enrollments', 0)
                    dept.finished_enrollments = getattr(stats_dept, 'finished_enrollments', 0)
                    dept.not_finished_manually_count = getattr(stats_dept, 'not_finished_manually_count', 0)
                    dept.not_finished_enrollments_count = getattr(stats_dept, 'not_finished_enrollments_count', 0)
                    dept.expired_enrollments_count = getattr(stats_dept, 'expired_enrollments_count', 0)
                    dept.progress_percent = getattr(stats_dept, 'progress_percent', 0.0)
                yield dept

        qs._iterator = stats_iterator
        return qs

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "department_id",
                openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Department UUID",
            ),
            openapi.Parameter(
                "search",
                openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Search Department by name"
            ),
        ],
        responses={200: get_paginated_serializer(create_recursive_serializer(serializer_class))},
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

class UserSupervisorSettingsViewSet(RetrieveUpdateAPIView):
    serializer_class = UserSupervisorSettingsSerializer
    permission_classes = [IsAuthenticated]

    def get_object(self):
        obj, created = UserSupervisorSettings.objects.get_or_create(user=self.request.user)
        return obj

    def put(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

    def patch(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)


class SupervisorDashboardMemberAPIView(RetrieveAPIView):
    """
    Получить детальную карточку подчинённого (с аффилиацией, позицией, супервизорами)
    """
    permission_classes = [IsAuthenticated]
    serializer_class = DashboardMemberSerializer

    def get_queryset(self):
        companies_qs = UserCompany.objects.select_related(
            'company',
            'department__parent',
            'department',
            'position'
        )

        return User.objects.prefetch_related(
            Prefetch(
                'companies',
                queryset=companies_qs,
                to_attr='user_companies'
            ),
            'supervisors',
        ).select_related('country')

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                'user_id',
                openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description='User UUID'
            )
        ]
    )
    def get(self, request, *args, **kwargs):
        user = get_object_or_404(self.get_queryset(), pk=request.query_params.get('user_id'))
        serializer = self.serializer_class(user)

        return Response(serializer.data)


class SupervisorDashboardEnrollmentsAPIView(MyCoursesAPIView):
    def get_queryset(self):
        if getattr(self, "swagger_fake_view", False):
            return Enrollment.objects.none()
        return (
            Enrollment.objects.filter(user=self.request.query_params.get("user_id"))
            .select_related(
                "resource",
                "resource__detail",
                "due_date_settings",
                "resource__detail__instructor",
                "resource__owner",
            )
            .prefetch_related("resource__detail__tags")
        )

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                'user_id',
                openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description='User UUID'
            )
        ],
        responses={200: CoursesEnvelopeSerializer})
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class StatisticsCourseProgressView(generics.GenericAPIView):
    serializer_class = StatisticsCourseProgressSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = None

    def get_queryset(self):
        user: User = self.request.user
        qs: QuerySet[Enrollment] = Enrollment.objects.filter(
            Q(user__supervisors_in_user__supervisor=user) |
            Q(
                user__companies__department__supervisors_in_department__supervisor_id=user
            )
        )

        department_id = self.request.query_params.get("department_id")
        if department_id:
            qs = qs.filter(user__companies__department_id=department_id)

        return (
            qs
            .distinct()
            .aggregate(
                total_count=Count(
                    "id",
                    distinct=True,
                ),
                finished_count=Count(
                    "status",
                    filter=Q(status=Enrollment.FINISHED),
                ),
                in_progress_count=Count(
                    "status",
                    filter=Q(status=Enrollment.IN_PROGRESS),
                ),
                finished_manually_count=Count(
                    "status",
                    filter=Q(status=Enrollment.FINISHED_MANUALLY),
                ),
                not_started_count=Count(
                    "status",
                    filter=Q(status=Enrollment.NOT_STARTED),
                ),
            )
        )

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                name="department_id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Department UUID",
            )
        ],
        responses={200: serializer_class},
    )
    def get(self, request, *args, **kwargs):
        qs: dict = self.filter_queryset(self.get_queryset())

        total_count = qs.get("total_count")
        qs["finished_percent"] = self.calculate_percent(qs.get("finished_count"), total_count)
        qs["in_progress_percent"] = self.calculate_percent(qs.get("in_progress_count"), total_count)
        qs["finished_manually_percent"] = self.calculate_percent(qs.get("finished_manually_count"), total_count)
        qs["not_started_percent"] = self.calculate_percent(qs.get("not_started_count"), total_count)

        serializer: StatisticsCourseProgressSerializer = self.get_serializer(qs)
        return Response(serializer.data)

    @staticmethod
    def calculate_percent(value: int, total_count: int) -> float:
        # Чтобы не делить на 0
        if total_count == 0:
            return 0
        return round((value / total_count) * 100, 2)


class StatisticsCourseFinishedView(generics.GenericAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = StatisticsCourseFinishedSerializer
    pagination_class = None

    def get_queryset(self):
        user: User = self.request.user
        qs: QuerySet[Enrollment] = Enrollment.objects.filter(
            Q(user__supervisors_in_user__supervisor=user) |
            Q(
                user__companies__department__supervisors_in_department__supervisor_id=user
            )
        )

        department_id = self.request.query_params.get("department_id")
        if department_id:
            qs = qs.filter(user__companies__department_id=department_id)

        return qs.aggregate(
            total_count=Count(
                "id",
                distinct=True,
            ),
            finished_count=Count(
                "status",
                filter=Q(status=Enrollment.FINISHED)
                | Q(status=Enrollment.FINISHED_MANUALLY),
            ),
            not_finished_count=Count(
                "status",
                filter=Q(status=Enrollment.IN_PROGRESS)
                | Q(status=Enrollment.NOT_STARTED),
            ),
        )

    def get(self, request, *args, **kwargs):
        qs: dict = self.filter_queryset(self.get_queryset())
        serializer: StatisticsCourseFinishedSerializer = self.get_serializer(qs)
        return Response(serializer.data)


class StatisticsCourseExpiredView(generics.GenericAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = StatisticsCourseExpiredSerializer
    pagination_class = None

    def get_queryset(self):
        user: User = self.request.user
        qs: QuerySet[Enrollment] = Enrollment.objects.filter(
            Q(user__supervisors_in_user__supervisor=user)
            | Q(
                user__companies__department__supervisors_in_department__supervisor_id=user
            )
        )

        department_id = self.request.query_params.get("department_id")
        if department_id:
            qs = qs.filter(user__companies__department_id=department_id)

        return qs.aggregate(
            not_logged_in=Count("user", filter=Q(user__last_login__isnull=True)),
            expired_courses=Count(
                "due_date_settings__date",
                filter=Q(due_date_settings__date__lt=timezone.now()),
            ),
            expired_certificates=Count(
                "certification",
                filter=Q(certification__expired_date__lt=timezone.now()),
            ),
        )

    def get(self, request, *args, **kwargs):
        qs: dict = self.filter_queryset(self.get_queryset())
        serializer: StatisticsCourseExpiredSerializer = self.get_serializer(qs)
        return Response(serializer.data)
