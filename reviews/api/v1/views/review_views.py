from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg.utils import swagger_auto_schema
from rest_framework import filters, generics
from rest_framework.generics import ListCreateAPIView
from rest_framework.permissions import (
    IsAuthenticatedOrReadOnly,
)

from lms.utils import CustomPagination, get_paginated_serializer
from reviews.api.v1.filters import ReviewFilter
from reviews.api.v1.permissions.review_permissions import IsReviewAuthor
from reviews.api.v1.serializers.review_serializers import ReviewSerializer
from reviews.models import Review


class ReviewListCreateAPIView(ListCreateAPIView):
    queryset = Review.objects.select_related("resource", "user").order_by("-created_at")
    serializer_class = ReviewSerializer
    permission_classes = [IsAuthenticatedOrReadOnly]
    pagination_class = CustomPagination
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_class = ReviewFilter
    ordering = ["-created_at"]
    ordering_fields = ["resource", "user", "rating", "title", "created_at"]

    @swagger_auto_schema(responses={200: get_paginated_serializer(serializer_class)})
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class ReviewRetrieveUpdateDestroyAPIView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Review.objects.all()
    serializer_class = ReviewSerializer
    permission_classes = [IsReviewAuthor]
