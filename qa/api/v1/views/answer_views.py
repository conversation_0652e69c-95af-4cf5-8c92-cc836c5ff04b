from django_filters.rest_framework import Django<PERSON>ilterB<PERSON><PERSON>
from rest_framework import generics
from rest_framework.permissions import IsAuthenticated
from lms.utils import CustomPagination, get_paginated_serializer
from drf_yasg.utils import swagger_auto_schema
from qa.permissions.codes import IsAnswerAuthor

from qa.api.v1.serializers import (
    AnswerCreateSerializer,
    AnswerDetailSerializer,
    AnswerListSerializer,
)
from qa.models import Answer


class AnswerListView(generics.ListAPIView):
    serializer_class = AnswerListSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ["author", "question"]
    pagination_class = CustomPagination

    @swagger_auto_schema(responses={200: get_paginated_serializer(serializer_class)})
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    def get_queryset(self):
        return Answer.objects.select_related(
            "question",
            "question__resource",
            "question__author",
            "question__resource__owner",
            "author",
        ).all()


class AnswerCreateView(generics.CreateAPIView):
    queryset = Answer.objects.all()
    serializer_class = AnswerCreateSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        question = serializer.validated_data.get("question")
        question.is_answered = True
        question.save()
        serializer.save(author=self.request.user)


class AnswerDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Answer.objects.all()
    serializer_class = AnswerDetailSerializer
    permission_classes = [IsAuthenticated, IsAnswerAuthor]

    def get_queryset(self):
        return Answer.objects.select_related(
            "question",
            "question__resource",
            "question__author",
            "question__resource__owner",
            "author",
        )
