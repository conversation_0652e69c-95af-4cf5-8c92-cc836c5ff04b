from rest_framework import permissions
from core.enums import UserRoleEnum


class BaseRolePermission(permissions.BasePermission):
    allowed_roles = []

    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False

        if request.user.is_superuser:
            return True

        if hasattr(request.user, 'roles'):
            return request.user.roles.filter(code__in=self.allowed_roles).exists()

        return False

    def has_object_permission(self, request, view, obj):
        return self.has_permission(request, view)


class IsRoleAdministrator(BaseRolePermission):
    allowed_roles = [UserRoleEnum.ADMINISTRATOR.value]


class IsRoleLearner(BaseRolePermission):
    allowed_roles = [UserRoleEnum.LEARNER.value]


class IsRoleDepartmentAdministrator(BaseRolePermission):
    allowed_roles = [UserRoleEnum.DEPARTMENT_ADMINISTRATOR.value]


class IsRoleAccountOwner(BaseRolePermission):
    allowed_roles = [UserRoleEnum.ACCOUNT_OWNER.value]


class IsRoleCourseAuthor(BaseRolePermission):
    allowed_roles = [UserRoleEnum.COURSE_AUTHOR.value]


class IsRoleSupervisor(BaseRolePermission):
    allowed_roles = [UserRoleEnum.SUPERVISOR.value]


class HasPermissionCode(permissions.BasePermission):
    """
    Проверяет, есть ли у пользователя конкретное разрешение (по коду).
    """
    permission_code = None

    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False

        if request.user.is_superuser:
            return True

        if not self.permission_code:
            return False

        return request.user.roles.filter(
            permissions__code=self.permission_code
        ).exists()
