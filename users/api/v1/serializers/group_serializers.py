from datetime import datetime

from django.db import transaction
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers

from users.models import Group, User, UserSchedule


class GroupSerializer(serializers.ModelSerializer):
    users_count = serializers.IntegerField(read_only=True, default=0)

    class Meta:
        model = Group
        fields = ("id", "name", "users_count")


class GroupListSerializer(serializers.ModelSerializer):
    class Meta:
        model = Group
        fields = ("id", "name")


class UserGroupsRequestSerializer(serializers.Serializer):
    groups = serializers.PrimaryKeyRelatedField(queryset=Group.objects.all(), many=True)

    def update(self, instance, validated_data):
        with transaction.atomic():
            instance.groups.clear()
            instance.groups.add(*validated_data.get("groups", []))
        return validated_data


class TerminateSerializer(serializers.Serializer):
    users = serializers.PrimaryKeyRelatedField(queryset=User.objects.all(), many=True)
    finish_at = serializers.DateField(required=False, allow_null=True)
    event = serializers.ChoiceField(choices=UserSchedule.EVENT_CHOICES)

    @staticmethod
    def validate_finish_at(value):
        if value and value < datetime.now().date():
            raise serializers.ValidationError(
                _("The finish date must not be less than today")
            )
        return value
