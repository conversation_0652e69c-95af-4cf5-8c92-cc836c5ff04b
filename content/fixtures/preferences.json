[{"model": "content.preference", "pk": "f847d40d-7d11-408f-9173-67b1ae5c6c6b", "fields": {"created_at": "2025-01-23T12:34:07.773Z", "updated_at": "2025-01-23T12:34:07.773Z", "type": "LONGREAD", "section": "NOTIFICATION", "code": "LONGREAD_NOTIFICATION_OF_APPOINTMENT", "name": "Уведомление о назначении", "payload": {"context": {"form": [{"name": "notify", "type": "CHECKBOX", "label": "Уведомлять пользователей о назначении курса", "value": false}, {"name": "notification.subject", "type": "INPUT_TEXT", "label": "Тема", "value": "У вас появились новые курсы", "enabled": "1.notify=true"}, {"name": "notification.message", "type": "TEXTAREA", "label": "Текст уведомления", "value": "Добрый день!\n\nВам назначен материал «%TITLE%».\n\nСрок выполнения для материала %DUE_DATE%.\n\n\nЧтобы пройти материал, перейдите по ссылке:\n%LINK%", "enabled": "1.notify=true"}]}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["notify"], "properties": {"notification": {"type": "object", "required": ["subject", "message"], "properties": {"message": {"type": "string", "maxLength": 1000}, "subject": {"type": "string", "maxLength": 255}}}, "notify": {"type": "boolean"}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "a44b9a1f-3112-49b9-8907-46c929f6216b", "fields": {"created_at": "2025-01-23T12:34:07.773Z", "updated_at": "2025-01-23T12:34:07.773Z", "type": "LONGREAD", "section": "NOTIFICATION", "code": "LONGREAD_REMINDER_BEFORE_THE_COMPLETION_DATE", "name": "Напоминание перед сроком завершения", "payload": {"context": {"form": [{"name": "notify", "type": "CHECKBOX", "label": "Напомнить пользователям о приближении срока завершения", "value": false}, {"name": "data.intervals", "type": "ARRAY", "enabled": "2.notify=true", "children": [{"name": "value", "type": "INPUT_NUMBER", "value": 1}, {"help": "до срока завершения", "name": "units", "type": "SELECT", "value": "d", "options": [{"label": "минут", "value": "m"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "h"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "d"}]}]}, {"name": "data.notification.subject", "type": "INPUT_TEXT", "label": "Тема", "value": "Необходимо завершить материал «%CONTENT_TITLE%»", "enabled": "2.notify=true"}, {"name": "data.notification.message", "type": "TEXTAREA", "label": "Текст напоминания", "value": "Напоминаем, что приближается время завершения материала.\n\n  Материал: %CONTENT_TITLE%.\n  Завершить материал до %DUE_DATE%\n\nПерейдите по ссылке для доступа к материалу:\n%COURSE_INFO_URL%\n", "enabled": "2.notify=true"}], "disable": false}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["notify", "data"], "properties": {"data": {"type": "object", "required": ["notification", "intervals"], "properties": {"intervals": {"type": "array", "items": {"type": "object", "required": ["units", "value"], "properties": {"units": {"enum": ["m", "h", "d"], "type": "string"}, "value": {"type": "integer", "minimum": 1}}, "additionalProperties": false}}, "notification": {"type": "object", "required": ["subject", "message"], "properties": {"message": {"type": "string", "maxLength": 1000}, "subject": {"type": "string", "maxLength": 255}}}}, "additionalProperties": false}, "notify": {"type": "boolean"}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "c0332c1e-8729-40c9-bce8-726eedf6336a", "fields": {"created_at": "2025-01-23T12:34:07.773Z", "updated_at": "2025-01-23T12:34:07.773Z", "type": "LONGREAD", "section": "NOTIFICATION", "code": "LONGREAD_REMINDER_AFTER_THE_COMPLETION_DATE", "name": "Напоминание после срока завершения", "payload": {"context": {"form": [{"name": "notify", "type": "CHECKBOX", "label": "Напоминать пользователям о просроченном обучении через", "order": 1, "value": false}, {"name": "data.intervals", "type": "ARRAY", "enabled": "3.notify=true", "children": [{"name": "value", "type": "INPUT_NUMBER", "order": 1, "value": "1"}, {"help": "после срока", "name": "units", "type": "SELECT", "order": 2, "value": "w", "options": [{"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "d"}, {"label": "недель", "value": "w"}]}]}, {"name": "data.notification.subject", "type": "INPUT_TEXT", "label": "Тема", "value": "Завершите курс «%CONTENT_TITLE%»", "enabled": "3.notify=true"}, {"name": "data.notification.message", "type": "TEXTAREA", "label": "Текст напоминания", "value": "Здравствуйте, %USERNAME%!\n\nСрок прохождения материала «%CONTENT_TITLE%» истек. Материал все еще можно изучить, не откладывайте: %COURSE_INFO_URL%", "enabled": "3.notify=true"}], "disable": false}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["notify", "data"], "properties": {"data": {"type": "object", "required": ["notification", "intervals"], "properties": {"intervals": {"type": "array", "items": {"type": "object", "required": ["units", "value"], "properties": {"units": {"enum": ["d", "w"], "type": "string"}, "value": {"type": "integer", "minimum": 1}}, "additionalProperties": false}}, "notification": {"type": "object", "required": ["subject", "message"], "properties": {"message": {"type": "string", "maxLength": 1000}, "subject": {"type": "string", "maxLength": 255}}}}, "additionalProperties": false}, "notify": {"type": "boolean"}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "de5a2e98-03a0-4137-9bd5-18be982d456a", "fields": {"created_at": "2025-01-23T12:34:07.773Z", "updated_at": "2025-01-23T12:34:07.773Z", "type": "LONGREAD", "section": "NOTIFICATION", "code": "LONGREAD_COMPLETION_NOTIFICATION", "name": "Уведомлениe о завершении", "payload": {"context": {"form": [{"name": "notify", "type": "CHECKBOX", "label": "Уведомлять администраторов, когда пользователь прошел обучение", "order": 1, "value": false}, {"name": "notification.users", "type": "AUTOCOMPLETE", "order": 1, "route": "USERS", "value": [], "enabled": "4.notify=true", "placeholder": "Поиск"}, {"name": "notification.subject", "type": "INPUT_TEXT", "label": "Тема", "value": "Сотрудник %USERNAME% завершил материал «%CONTENT_TITLE%»", "enabled": "4.notify=true"}, {"name": "notification.message", "type": "TEXTAREA", "label": "Текст напоминания", "value": "Здравствуйте!\n\nСотрудник %USERNAME% успешно завершил материал «%CONTENT_TITLE%».\n\nВы можете ознакомиться с деталями прохождения и результатами по ссылке: %COURSE_INFO_URL%\n\nЕсли материал требует проверки или оценки — перейдите по ссылке, чтобы продолжить работу.", "enabled": "4.notify=true"}], "disable": false}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["notify", "notification"], "properties": {"notification": {"type": "object", "required": ["users", "subject", "message"], "properties": {"users": {"type": "array", "items": {"type": "object", "required": ["name", "email"], "properties": {"name": {"type": "string", "maxLength": 255}, "email": {"type": "string", "format": "email", "maxLength": 255}}, "additionalProperties": false}}, "subject": {"type": "string", "maxLength": 255}, "message": {"type": "string", "maxLength": 1000}}, "additionalProperties": false}, "notify": {"type": "boolean"}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "afb32f35-e24b-4652-a8ec-2745f87421b4", "fields": {"created_at": "2025-01-23T12:34:07.773Z", "updated_at": "2025-01-23T12:34:07.773Z", "type": "LEARNING_PATH", "section": "STRUCTURE", "code": "LEARNING_PATH_SETTINGS", "name": "Настройки прохождения курса", "payload": {}}}, {"model": "content.preference", "pk": "13aaca9f-1be4-4382-9bce-8b5606d2aebb", "fields": {"created_at": "2025-01-23T12:34:07.773Z", "updated_at": "2025-01-23T12:34:07.773Z", "type": "LEARNING_TRACK", "section": "STRUCTURE", "code": "LEARNING_TRACK_DATE_OF_ENROLLMENT_SETTINGS", "name": "Дата назначения", "payload": {}}}, {"model": "content.preference", "pk": "41b1b476-bda5-4271-b071-858665aea9fd", "fields": {"created_at": "2025-01-23T12:34:07.773Z", "updated_at": "2025-01-23T12:34:07.773Z", "type": "LEARNING_TRACK", "section": "STRUCTURE", "code": "LEARNING_TRACK_COMPLETION_PERIOD", "name": "Срок выполнения курса", "payload": {}}}, {"model": "content.preference", "pk": "a47a81da-5fed-41dd-99be-689454b9073a", "fields": {"created_at": "2025-04-08T10:15:10.698Z", "updated_at": "2025-04-08T10:15:10.698Z", "type": "LONGREAD", "section": "ACCESS_CONTROL", "code": "LONGREAD_ACCESS_MANAGEMENT_AUTO_ASSIGNMENT", "name": "Автоматическое назначение", "payload": {"context": {"form": [{"name": "auto_assignment", "type": "ARRAY", "order": 1, "children": [{"name": "condition", "type": "select", "label": "Когда учащийся добавлен", "value": "department", "options": [{"label": "в подразделение", "order": 1, "value": "department"}, {"label": "в подразделение (или дочернее)", "order": 2, "value": "department_wth_children"}, {"label": "в группу", "order": 3, "value": "group"}]}, {"name": "condition_value.value", "type": "FETCH_SELECT", "model": "Department", "value": null, "enabled": "8.auto_assignment.$.condition=department"}, {"name": "condition_value.value", "type": "FETCH_SELECT", "model": "Department", "value": null, "enabled": "8.auto_assignment.$.condition=department_wth_children"}, {"name": "condition_value.value", "type": "FETCH_SELECT", "model": "Group", "value": null, "enabled": "8.auto_assignment.$.condition=group"}, {"name": "condition_value.model", "type": "HIDDEN", "value": "Department", "enabled": "8.auto_assignment.$.condition=department"}, {"name": "condition_value.model", "type": "HIDDEN", "value": "Department", "enabled": "8.auto_assignment.$.condition=department_wth_children"}, {"name": "condition_value.model", "type": "HIDDEN", "value": "Group", "enabled": "8.auto_assignment.$.condition=group"}]}]}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["auto_assignment"], "properties": {"auto_assignment": {"type": "array", "items": {"type": "object", "allOf": [{"if": {"properties": {"condition": {"const": "department"}}}, "then": {"properties": {"condition_value": {"properties": {"model": {"const": "Department"}}}}}}, {"if": {"properties": {"condition": {"const": "department_wth_children"}}}, "then": {"properties": {"condition_value": {"properties": {"model": {"const": "Department"}}}}}}, {"if": {"properties": {"condition": {"const": "group"}}}, "then": {"properties": {"condition_value": {"properties": {"model": {"const": "Group"}}}}}}], "required": ["condition", "condition_value"], "properties": {"condition": {"enum": ["department", "department_wth_children", "group"], "type": "string"}, "condition_value": {"type": "object", "required": ["model", "value"], "properties": {"model": {"type": "string", "maxLength": 255}, "value": {"type": "integer", "minimum": 1}}}}}}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "40d7b4f5-5d17-40ef-b7ae-8371702d91f7", "fields": {"created_at": "2025-04-08T10:54:05.050Z", "updated_at": "2025-04-16T12:39:33.917Z", "type": "LONGREAD", "section": "ACCESS_CONTROL", "code": "LONGREAD_ACCESS_MANAGEMENT_ADDING_CATALOG", "name": "Добавление в каталог", "payload": {"context": {"form": [{"name": "adding_catalog", "type": "ARRAY", "order": 1, "children": [{"name": "condition_value.value", "type": "FETCH_SELECT", "label": "Категории", "model": "Catalog", "value": null}, {"name": "condition_value.model", "type": "HIDDEN", "value": "Catalog"}, {"name": "access_by_claim", "type": "CHECKBOX", "label": "Доступ по заявке", "value": false}]}]}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["adding_catalog"], "properties": {"adding_catalog": {"type": "array", "items": {"type": "object", "required": ["condition_value"], "properties": {"access_by_claim": {"type": "boolean"}, "condition_value": {"type": "object", "required": ["model", "value"], "properties": {"model": {"type": "string", "maxLength": 255}, "value": {"type": "integer", "minimum": 1}}}}}}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "762032e4-d060-417a-864f-47f22dfe034c", "fields": {"created_at": "2025-04-08T11:58:37.604Z", "updated_at": "2025-04-08T12:01:33.557Z", "type": "LONGREAD", "section": "ACCESS_CONTROL", "code": "LONGREAD_ACCESS_MANAGEMENT_DEFAULT_ASSIGNMENT_PARAMETERS", "name": "Параметры назначения по умолчанию", "payload": {"context": {"form": [{"name": "term", "type": "select", "label": "Срок выполнения", "value": "UNLIMITED", "options": [{"label": "Без срока", "value": "UNLIMITED"}, {"label": "На срок", "value": "DUE_PERIOD"}]}, {"name": "value", "type": "INPUT_NUMBER", "value": "1", "enabled": "10.term=DUE_PERIOD"}, {"name": "units", "type": "SELECT", "value": "WEEKS", "enabled": "10.term=DUE_PERIOD", "options": [{"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "DAYS"}, {"label": "недель", "value": "WEEKS"}, {"label": "меся<PERSON>ев", "value": "MONTHS"}, {"label": "лет", "value": "YEARS"}]}, {"name": "deny_access", "type": "select", "label": "Запретить доступ", "enabled": "10.term=DUE_PERIOD", "value": "NO_PROHIBIT", "options": [{"label": "Не запрещать", "value": "NO_PROHIBIT"}, {"label": "После окончания срока выполнения", "value": "AFTER_DEADLINE"}]}]}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["term"], "properties": {"term": {"enum": ["UNLIMITED", "DUE_PERIOD"], "type": "string"}, "units": {"enum": ["DAYS", "WEEKS", "MONTHS", "YEARS"], "type": "string"}, "value": {"type": "integer"}, "deny_access": {"enum": ["NO_PROHIBIT", "AFTER_DEADLINE"], "type": "string"}}, "additionalProperties": false, "allOf": [{"if": {"properties": {"term": {"const": "DUE_PERIOD"}}}, "then": {"required": ["value", "units", "deny_access"]}}]}}}}, {"model": "content.preference", "pk": "6f98296a-5d82-4e39-a9b1-d34ba68ef0c8", "fields": {"created_at": "2025-04-08T10:15:10.698Z", "updated_at": "2025-04-28T05:24:54.853Z", "type": "LINK", "section": "ACCESS_CONTROL", "code": "LINK_ACCESS_MANAGEMENT_AUTO_ASSIGNMENT", "name": "Автоматическое назначение", "payload": {"context": {"form": [{"name": "auto_assignment", "type": "ARRAY", "order": 1, "children": [{"name": "condition", "type": "select", "label": "Когда учащийся добавлен", "value": "department", "options": [{"label": "в подразделение", "order": 1, "value": "department"}, {"label": "в подразделение (или дочернее)", "order": 2, "value": "department_wth_children"}, {"label": "в группу", "order": 3, "value": "group"}]}, {"name": "condition_value.value", "type": "FETCH_SELECT", "model": "Department", "value": null, "enabled": "11.auto_assignment.$.condition=department"}, {"name": "condition_value.value", "type": "FETCH_SELECT", "model": "Department", "value": null, "enabled": "11.auto_assignment.$.condition=department_wth_children"}, {"name": "condition_value.value", "type": "FETCH_SELECT", "model": "Group", "value": null, "enabled": "11.auto_assignment.$.condition=group"}, {"name": "condition_value.model", "type": "HIDDEN", "value": "Department", "enabled": "11.auto_assignment.$.condition=department"}, {"name": "condition_value.model", "type": "HIDDEN", "value": "Department", "enabled": "11.auto_assignment.$.condition=department_wth_children"}, {"name": "condition_value.model", "type": "HIDDEN", "value": "Group", "enabled": "11.auto_assignment.$.condition=group"}]}]}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["auto_assignment"], "properties": {"auto_assignment": {"type": "array", "items": {"type": "object", "allOf": [{"if": {"properties": {"condition": {"const": "department"}}}, "then": {"properties": {"condition_value": {"properties": {"model": {"const": "Department"}}}}}}, {"if": {"properties": {"condition": {"const": "department_wth_children"}}}, "then": {"properties": {"condition_value": {"properties": {"model": {"const": "Department"}}}}}}, {"if": {"properties": {"condition": {"const": "group"}}}, "then": {"properties": {"condition_value": {"properties": {"model": {"const": "Group"}}}}}}], "required": ["condition", "condition_value"], "properties": {"condition": {"enum": ["department", "department_wth_children", "group"], "type": "string"}, "condition_value": {"type": "object", "required": ["model", "value"], "properties": {"model": {"type": "string", "maxLength": 255}, "value": {"type": "integer", "minimum": 1}}}}}}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "f736b5e9-5427-40c1-9f01-78c81e873dec", "fields": {"created_at": "2025-04-08T10:54:05.050Z", "updated_at": "2025-04-16T12:39:33.917Z", "type": "LINK", "section": "ACCESS_CONTROL", "code": "LINK_ACCESS_MANAGEMENT_ADDING_CATALOG", "name": "Добавление в каталог", "payload": {"context": {"form": [{"name": "adding_catalog", "type": "ARRAY", "order": 1, "children": [{"name": "condition_value.value", "type": "FETCH_SELECT", "label": "Категории", "model": "Catalog", "value": null}, {"name": "condition_value.model", "type": "HIDDEN", "value": "Catalog"}, {"name": "access_by_claim", "type": "CHECKBOX", "label": "Доступ по заявке", "value": false}]}]}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["adding_catalog"], "properties": {"adding_catalog": {"type": "array", "items": {"type": "object", "required": ["condition_value"], "properties": {"access_by_claim": {"type": "boolean"}, "condition_value": {"type": "object", "required": ["model", "value"], "properties": {"model": {"type": "string", "maxLength": 255}, "value": {"type": "integer", "minimum": 1}}}}}}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "611e0080-941e-48c5-8599-de7b6e8c0bb9", "fields": {"created_at": "2025-04-08T11:58:37.604Z", "updated_at": "2025-04-28T05:25:18.364Z", "type": "LINK", "section": "ACCESS_CONTROL", "code": "LINK_ACCESS_MANAGEMENT_DEFAULT_ASSIGNMENT_PARAMETERS", "name": "Параметры назначения по умолчанию", "payload": {"context": {"form": [{"name": "term", "type": "select", "label": "Срок выполнения", "value": "UNLIMITED", "options": [{"label": "Без срока", "value": "UNLIMITED"}, {"label": "На срок", "value": "DUE_PERIOD"}]}, {"name": "value", "type": "INPUT_NUMBER", "value": "1", "enabled": "13.term=DUE_PERIOD"}, {"name": "units", "type": "SELECT", "value": "WEEKS", "enabled": "13.term=DUE_PERIOD", "options": [{"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "DAYS"}, {"label": "недель", "value": "WEEKS"}, {"label": "меся<PERSON>ев", "value": "MONTHS"}, {"label": "лет", "value": "YEARS"}]}, {"name": "deny_access", "type": "select", "label": "Запретить доступ", "enabled": "13.term=DUE_PERIOD", "value": "NO_PROHIBIT", "options": [{"label": "Не запрещать", "value": "NO_PROHIBIT"}, {"label": "После окончания срока выполнения", "value": "AFTER_DEADLINE"}]}]}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["term"], "properties": {"term": {"enum": ["UNLIMITED", "DUE_PERIOD"], "type": "string"}, "units": {"enum": ["DAYS", "WEEKS", "MONTHS", "YEARS"], "type": "string"}, "value": {"type": "integer"}, "deny_access": {"enum": ["NO_PROHIBIT", "AFTER_DEADLINE"], "type": "string"}}, "additionalProperties": false, "allOf": [{"if": {"properties": {"term": {"const": "DUE_PERIOD"}}}, "then": {"required": ["value", "units", "deny_access"]}}]}}}}, {"model": "content.preference", "pk": "54f6e014-e85f-4c81-ba2b-89334f8b4793", "fields": {"created_at": "2025-04-08T10:15:10.698Z", "updated_at": "2025-04-28T05:31:48.420Z", "type": "ONLINE_QUIZ", "section": "ACCESS_CONTROL", "code": "ONLINE_QUIZ_ACCESS_MANAGEMENT_AUTO_ASSIGNMENT", "name": "Автоматическое назначение", "payload": {"context": {"form": [{"name": "auto_assignment", "type": "ARRAY", "order": 1, "children": [{"name": "condition", "type": "select", "label": "Когда учащийся добавлен", "value": "department", "options": [{"label": "в подразделение", "order": 1, "value": "department"}, {"label": "в подразделение (или дочернее)", "order": 2, "value": "department_wth_children"}, {"label": "в группу", "order": 3, "value": "group"}]}, {"name": "condition_value.value", "type": "FETCH_SELECT", "model": "Department", "value": null, "enabled": "14.auto_assignment.$.condition=department"}, {"name": "condition_value.value", "type": "FETCH_SELECT", "model": "Department", "value": null, "enabled": "14.auto_assignment.$.condition=department_wth_children"}, {"name": "condition_value.value", "type": "FETCH_SELECT", "model": "Group", "value": null, "enabled": "14.auto_assignment.$.condition=group"}, {"name": "condition_value.model", "type": "HIDDEN", "value": "Department", "enabled": "14.auto_assignment.$.condition=department"}, {"name": "condition_value.model", "type": "HIDDEN", "value": "Department", "enabled": "14.auto_assignment.$.condition=department_wth_children"}, {"name": "condition_value.model", "type": "HIDDEN", "value": "Group", "enabled": "14.auto_assignment.$.condition=group"}]}]}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["auto_assignment"], "properties": {"auto_assignment": {"type": "array", "items": {"type": "object", "allOf": [{"if": {"properties": {"condition": {"const": "department"}}}, "then": {"properties": {"condition_value": {"properties": {"model": {"const": "Department"}}}}}}, {"if": {"properties": {"condition": {"const": "department_wth_children"}}}, "then": {"properties": {"condition_value": {"properties": {"model": {"const": "Department"}}}}}}, {"if": {"properties": {"condition": {"const": "group"}}}, "then": {"properties": {"condition_value": {"properties": {"model": {"const": "Group"}}}}}}], "required": ["condition", "condition_value"], "properties": {"condition": {"enum": ["department", "department_wth_children", "group"], "type": "string"}, "condition_value": {"type": "object", "required": ["model", "value"], "properties": {"model": {"type": "string", "maxLength": 255}, "value": {"type": "integer", "minimum": 1}}}}}}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "8dfeadb0-9ff3-4f03-8d86-cacb5c5c3604", "fields": {"created_at": "2025-04-08T10:54:05.050Z", "updated_at": "2025-04-16T12:39:33.917Z", "type": "ONLINE_QUIZ", "section": "ACCESS_CONTROL", "code": "ONLINE_QUIZ_ACCESS_MANAGEMENT_ADDING_CATALOG", "name": "Добавление в каталог", "payload": {"context": {"form": [{"name": "adding_catalog", "type": "ARRAY", "order": 1, "children": [{"name": "condition_value.value", "type": "FETCH_SELECT", "label": "Категории", "model": "Catalog", "value": null}, {"name": "condition_value.model", "type": "HIDDEN", "value": "Catalog"}, {"name": "access_by_claim", "type": "CHECKBOX", "label": "Доступ по заявке", "value": false}]}]}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["adding_catalog"], "properties": {"adding_catalog": {"type": "array", "items": {"type": "object", "required": ["condition_value"], "properties": {"access_by_claim": {"type": "boolean"}, "condition_value": {"type": "object", "required": ["model", "value"], "properties": {"model": {"type": "string", "maxLength": 255}, "value": {"type": "integer", "minimum": 1}}}}}}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "adbb89ad-55ab-476c-81e0-b51113e20abc", "fields": {"created_at": "2025-04-08T11:58:37.604Z", "updated_at": "2025-04-28T05:32:03.841Z", "type": "ONLINE_QUIZ", "section": "ACCESS_CONTROL", "code": "ONLINE_QUIZ_ACCESS_MANAGEMENT_DEFAULT_ASSIGNMENT_PARAMETERS", "name": "Параметры назначения по умолчанию", "payload": {"context": {"form": [{"name": "term", "type": "select", "label": "Срок выполнения", "value": "UNLIMITED", "options": [{"label": "Без срока", "value": "UNLIMITED"}, {"label": "На срок", "value": "DUE_PERIOD"}]}, {"name": "value", "type": "INPUT_NUMBER", "value": "1", "enabled": "16.term=DUE_PERIOD"}, {"name": "units", "type": "SELECT", "value": "WEEKS", "enabled": "16.term=DUE_PERIOD", "options": [{"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "DAYS"}, {"label": "недель", "value": "WEEKS"}, {"label": "меся<PERSON>ев", "value": "MONTHS"}, {"label": "лет", "value": "YEARS"}]}, {"name": "deny_access", "type": "select", "label": "Запретить доступ", "value": "NO_PROHIBIT", "enabled": "16.term=DUE_PERIOD", "options": [{"label": "Не запрещать", "value": "NO_PROHIBIT"}, {"label": "После окончания срока выполнения", "value": "AFTER_DEADLINE"}]}]}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["term"], "properties": {"term": {"enum": ["UNLIMITED", "DUE_PERIOD"], "type": "string"}, "units": {"enum": ["DAYS", "WEEKS", "MONTHS", "YEARS"], "type": "string"}, "value": {"type": "integer"}, "deny_access": {"enum": ["NO_PROHIBIT", "AFTER_DEADLINE"], "type": "string"}}, "additionalProperties": false, "allOf": [{"if": {"properties": {"term": {"const": "DUE_PERIOD"}}}, "then": {"required": ["value", "units", "deny_access"]}}]}}}}, {"model": "content.preference", "pk": "c335772c-ad77-4d90-a97d-838f426b42b7", "fields": {"created_at": "2025-04-08T10:15:10.698Z", "updated_at": "2025-04-28T05:42:35.667Z", "type": "COURSE", "section": "ACCESS_CONTROL", "code": "COURSE_ACCESS_MANAGEMENT_AUTO_ASSIGNMENT", "name": "Автоматическое назначение", "payload": {"context": {"form": [{"name": "auto_assignment", "type": "ARRAY", "order": 1, "children": [{"name": "condition", "type": "select", "label": "Когда учащийся добавлен", "value": "department", "options": [{"label": "в подразделение", "order": 1, "value": "department"}, {"label": "в подразделение (или дочернее)", "order": 2, "value": "department_wth_children"}, {"label": "в группу", "order": 3, "value": "group"}]}, {"name": "condition_value.value", "type": "FETCH_SELECT", "model": "Department", "value": null, "enabled": "17.auto_assignment.$.condition=department"}, {"name": "condition_value.value", "type": "FETCH_SELECT", "model": "Department", "value": null, "enabled": "17.auto_assignment.$.condition=department_wth_children"}, {"name": "condition_value.value", "type": "FETCH_SELECT", "model": "Group", "value": null, "enabled": "17.auto_assignment.$.condition=group"}, {"name": "condition_value.model", "type": "HIDDEN", "value": "Department", "enabled": "17.auto_assignment.$.condition=department"}, {"name": "condition_value.model", "type": "HIDDEN", "value": "Department", "enabled": "17.auto_assignment.$.condition=department_wth_children"}, {"name": "condition_value.model", "type": "HIDDEN", "value": "Group", "enabled": "17.auto_assignment.$.condition=group"}]}]}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["auto_assignment"], "properties": {"auto_assignment": {"type": "array", "items": {"type": "object", "allOf": [{"if": {"properties": {"condition": {"const": "department"}}}, "then": {"properties": {"condition_value": {"properties": {"model": {"const": "Department"}}}}}}, {"if": {"properties": {"condition": {"const": "department_wth_children"}}}, "then": {"properties": {"condition_value": {"properties": {"model": {"const": "Department"}}}}}}, {"if": {"properties": {"condition": {"const": "group"}}}, "then": {"properties": {"condition_value": {"properties": {"model": {"const": "Group"}}}}}}], "required": ["condition", "condition_value"], "properties": {"condition": {"enum": ["department", "department_wth_children", "group"], "type": "string"}, "condition_value": {"type": "object", "required": ["model", "value"], "properties": {"model": {"type": "string", "maxLength": 255}, "value": {"type": "integer", "minimum": 1}}}}}}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "bf56f68f-394d-42ae-b2fa-1e6440d92173", "fields": {"created_at": "2025-04-08T10:54:05.050Z", "updated_at": "2025-04-16T12:39:33.917Z", "type": "COURSE", "section": "ACCESS_CONTROL", "code": "COURSE_ACCESS_MANAGEMENT_ADDING_CATALOG", "name": "Добавление в каталог", "payload": {"context": {"form": [{"name": "adding_catalog", "type": "ARRAY", "order": 1, "children": [{"name": "condition_value.value", "type": "FETCH_SELECT", "label": "Категории", "model": "Catalog", "value": null}, {"name": "condition_value.model", "type": "HIDDEN", "value": "Catalog"}, {"name": "access_by_claim", "type": "CHECKBOX", "label": "Доступ по заявке", "value": false}]}]}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["adding_catalog"], "properties": {"adding_catalog": {"type": "array", "items": {"type": "object", "required": ["condition_value"], "properties": {"access_by_claim": {"type": "boolean"}, "condition_value": {"type": "object", "required": ["model", "value"], "properties": {"model": {"type": "string", "maxLength": 255}, "value": {"type": "integer", "minimum": 1}}}}}}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "b558ab57-6a57-4dab-b48e-5e3f5342efc3", "fields": {"created_at": "2025-04-08T11:58:37.604Z", "updated_at": "2025-04-28T05:42:57.058Z", "type": "COURSE", "section": "ACCESS_CONTROL", "code": "COURSE_ACCESS_MANAGEMENT_DEFAULT_ASSIGNMENT_PARAMETERS", "name": "Параметры назначения по умолчанию", "payload": {"context": {"form": [{"name": "term", "type": "select", "label": "Срок выполнения", "value": "UNLIMITED", "options": [{"label": "Без срока", "value": "UNLIMITED"}, {"label": "На срок", "value": "DUE_PERIOD"}]}, {"name": "value", "type": "INPUT_NUMBER", "value": "1", "enabled": "19.term=DUE_PERIOD"}, {"name": "units", "type": "SELECT", "value": "WEEKS", "enabled": "19.term=DUE_PERIOD", "options": [{"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "DAYS"}, {"label": "недель", "value": "WEEKS"}, {"label": "меся<PERSON>ев", "value": "MONTHS"}, {"label": "лет", "value": "YEARS"}]}, {"name": "deny_access", "type": "select", "label": "Запретить доступ", "value": "NO_PROHIBIT", "enabled": "19.term=DUE_PERIOD", "options": [{"label": "Не запрещать", "value": "NO_PROHIBIT"}, {"label": "После окончания срока выполнения", "value": "AFTER_DEADLINE"}]}]}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["term"], "properties": {"term": {"enum": ["UNLIMITED", "DUE_PERIOD"], "type": "string"}, "units": {"enum": ["DAYS", "WEEKS", "MONTHS", "YEARS"], "type": "string"}, "value": {"type": "integer"}, "deny_access": {"enum": ["NO_PROHIBIT", "AFTER_DEADLINE"], "type": "string"}}, "additionalProperties": false, "allOf": [{"if": {"properties": {"term": {"const": "DUE_PERIOD"}}}, "then": {"required": ["value", "units", "deny_access"]}}]}}}}, {"model": "content.preference", "pk": "67684c09-8d54-4751-8d4a-3754157aba5a", "fields": {"created_at": "2025-04-08T10:15:10.698Z", "updated_at": "2025-04-28T05:51:57.830Z", "type": "LEARNING_PATH", "section": "ACCESS_CONTROL", "code": "LEARNING_PATH_ACCESS_MANAGEMENT_AUTO_ASSIGNMENT", "name": "Автоматическое назначение", "payload": {"context": {"form": [{"name": "auto_assignment", "type": "ARRAY", "order": 1, "children": [{"name": "condition", "type": "select", "label": "Когда учащийся добавлен", "value": "department", "options": [{"label": "в подразделение", "order": 1, "value": "department"}, {"label": "в подразделение (или дочернее)", "order": 2, "value": "department_wth_children"}, {"label": "в группу", "order": 3, "value": "group"}]}, {"name": "condition_value.value", "type": "FETCH_SELECT", "model": "Department", "value": null, "enabled": "20.auto_assignment.$.condition=department"}, {"name": "condition_value.value", "type": "FETCH_SELECT", "model": "Department", "value": null, "enabled": "20.auto_assignment.$.condition=department_wth_children"}, {"name": "condition_value.value", "type": "FETCH_SELECT", "model": "Group", "value": null, "enabled": "20.auto_assignment.$.condition=group"}, {"name": "condition_value.model", "type": "HIDDEN", "value": "Department", "enabled": "20.auto_assignment.$.condition=department"}, {"name": "condition_value.model", "type": "HIDDEN", "value": "Department", "enabled": "20.auto_assignment.$.condition=department_wth_children"}, {"name": "condition_value.model", "type": "HIDDEN", "value": "Group", "enabled": "20.auto_assignment.$.condition=group"}]}]}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["auto_assignment"], "properties": {"auto_assignment": {"type": "array", "items": {"type": "object", "allOf": [{"if": {"properties": {"condition": {"const": "department"}}}, "then": {"properties": {"condition_value": {"properties": {"model": {"const": "Department"}}}}}}, {"if": {"properties": {"condition": {"const": "department_wth_children"}}}, "then": {"properties": {"condition_value": {"properties": {"model": {"const": "Department"}}}}}}, {"if": {"properties": {"condition": {"const": "group"}}}, "then": {"properties": {"condition_value": {"properties": {"model": {"const": "Group"}}}}}}], "required": ["condition", "condition_value"], "properties": {"condition": {"enum": ["department", "department_wth_children", "group"], "type": "string"}, "condition_value": {"type": "object", "required": ["model", "value"], "properties": {"model": {"type": "string", "maxLength": 255}, "value": {"type": "integer", "minimum": 1}}}}}}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "a98a9b00-143b-4b23-9c8e-66acb7e78503", "fields": {"created_at": "2025-04-08T10:54:05.050Z", "updated_at": "2025-04-16T12:39:33.917Z", "type": "LEARNING_PATH", "section": "ACCESS_CONTROL", "code": "LEARNING_PATH_ACCESS_MANAGEMENT_ADDING_CATALOG", "name": "Добавление в каталог", "payload": {"context": {"form": [{"name": "adding_catalog", "type": "ARRAY", "order": 1, "children": [{"name": "condition_value.value", "type": "FETCH_SELECT", "label": "Категории", "model": "Catalog", "value": null}, {"name": "condition_value.model", "type": "HIDDEN", "value": "Catalog"}, {"name": "access_by_claim", "type": "CHECKBOX", "label": "Доступ по заявке", "value": false}]}]}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["adding_catalog"], "properties": {"adding_catalog": {"type": "array", "items": {"type": "object", "required": ["condition_value"], "properties": {"access_by_claim": {"type": "boolean"}, "condition_value": {"type": "object", "required": ["model", "value"], "properties": {"model": {"type": "string", "maxLength": 255}, "value": {"type": "integer", "minimum": 1}}}}}}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "79cee5b1-8162-439b-a221-37819ee60c36", "fields": {"created_at": "2025-04-08T11:58:37.604Z", "updated_at": "2025-04-28T05:52:08.191Z", "type": "LEARNING_PATH", "section": "ACCESS_CONTROL", "code": "LEARNING_PATH_ACCESS_MANAGEMENT_DEFAULT_ASSIGNMENT_PARAMETERS", "name": "Параметры назначения по умолчанию", "payload": {"context": {"form": [{"name": "term", "type": "select", "label": "Срок выполнения", "value": "UNLIMITED", "options": [{"label": "Без срока", "value": "UNLIMITED"}, {"label": "На срок", "value": "DUE_PERIOD"}]}, {"name": "value", "type": "INPUT_NUMBER", "value": "1", "enabled": "22.term=DUE_PERIOD"}, {"name": "units", "type": "SELECT", "value": "WEEKS", "enabled": "22.term=DUE_PERIOD", "options": [{"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "DAYS"}, {"label": "недель", "value": "WEEKS"}, {"label": "меся<PERSON>ев", "value": "MONTHS"}, {"label": "лет", "value": "YEARS"}]}, {"name": "deny_access", "type": "select", "label": "Запретить доступ", "value": "NO_PROHIBIT", "enabled": "22.term=DUE_PERIOD", "options": [{"label": "Не запрещать", "value": "NO_PROHIBIT"}, {"label": "После окончания срока выполнения", "value": "AFTER_DEADLINE"}]}]}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["term"], "properties": {"term": {"enum": ["UNLIMITED", "DUE_PERIOD"], "type": "string"}, "units": {"enum": ["DAYS", "WEEKS", "MONTHS", "YEARS"], "type": "string"}, "value": {"type": "integer"}, "deny_access": {"enum": ["NO_PROHIBIT", "AFTER_DEADLINE"], "type": "string"}}, "additionalProperties": false, "allOf": [{"if": {"properties": {"term": {"const": "DUE_PERIOD"}}}, "then": {"required": ["value", "units", "deny_access"]}}]}}}}, {"model": "content.preference", "pk": "d4e98b13-40d0-4ada-825a-1f1aaf098c1f", "fields": {"created_at": "2025-04-08T10:15:10.698Z", "updated_at": "2025-04-28T05:54:30.532Z", "type": "HOMEWORK", "section": "ACCESS_CONTROL", "code": "HOMEWORK_ACCESS_MANAGEMENT_AUTO_ASSIGNMENT", "name": "Автоматическое назначение", "payload": {"context": {"form": [{"name": "auto_assignment", "type": "ARRAY", "order": 1, "children": [{"name": "condition", "type": "select", "label": "Когда учащийся добавлен", "value": "department", "options": [{"label": "в подразделение", "order": 1, "value": "department"}, {"label": "в подразделение (или дочернее)", "order": 2, "value": "department_wth_children"}, {"label": "в группу", "order": 3, "value": "group"}]}, {"name": "condition_value.value", "type": "FETCH_SELECT", "model": "Department", "value": null, "enabled": "23.auto_assignment.$.condition=department"}, {"name": "condition_value.value", "type": "FETCH_SELECT", "model": "Department", "value": null, "enabled": "23.auto_assignment.$.condition=department_wth_children"}, {"name": "condition_value.value", "type": "FETCH_SELECT", "model": "Group", "value": null, "enabled": "23.auto_assignment.$.condition=group"}, {"name": "condition_value.model", "type": "HIDDEN", "value": "Department", "enabled": "23.auto_assignment.$.condition=department"}, {"name": "condition_value.model", "type": "HIDDEN", "value": "Department", "enabled": "23.auto_assignment.$.condition=department_wth_children"}, {"name": "condition_value.model", "type": "HIDDEN", "value": "Group", "enabled": "23.auto_assignment.$.condition=group"}]}]}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["auto_assignment"], "properties": {"auto_assignment": {"type": "array", "items": {"type": "object", "allOf": [{"if": {"properties": {"condition": {"const": "department"}}}, "then": {"properties": {"condition_value": {"properties": {"model": {"const": "Department"}}}}}}, {"if": {"properties": {"condition": {"const": "department_wth_children"}}}, "then": {"properties": {"condition_value": {"properties": {"model": {"const": "Department"}}}}}}, {"if": {"properties": {"condition": {"const": "group"}}}, "then": {"properties": {"condition_value": {"properties": {"model": {"const": "Group"}}}}}}], "required": ["condition", "condition_value"], "properties": {"condition": {"enum": ["department", "department_wth_children", "group"], "type": "string"}, "condition_value": {"type": "object", "required": ["model", "value"], "properties": {"model": {"type": "string", "maxLength": 255}, "value": {"type": "integer", "minimum": 1}}}}}}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "6e752d7d-db35-4d51-90d7-9863e99fc90a", "fields": {"created_at": "2025-04-08T10:54:05.050Z", "updated_at": "2025-04-16T12:39:33.917Z", "type": "HOMEWORK", "section": "ACCESS_CONTROL", "code": "HOMEWORK_ACCESS_MANAGEMENT_ADDING_CATALOG", "name": "Добавление в каталог", "payload": {"context": {"form": [{"name": "adding_catalog", "type": "ARRAY", "order": 1, "children": [{"name": "condition_value.value", "type": "FETCH_SELECT", "label": "Категории", "model": "Catalog", "value": null}, {"name": "condition_value.model", "type": "HIDDEN", "value": "Catalog"}, {"name": "access_by_claim", "type": "CHECKBOX", "label": "Доступ по заявке", "value": false}]}]}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["adding_catalog"], "properties": {"adding_catalog": {"type": "array", "items": {"type": "object", "required": ["condition_value"], "properties": {"access_by_claim": {"type": "boolean"}, "condition_value": {"type": "object", "required": ["model", "value"], "properties": {"model": {"type": "string", "maxLength": 255}, "value": {"type": "integer", "minimum": 1}}}}}}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "9ad940e5-236d-4954-a656-50b7385d12f7", "fields": {"created_at": "2025-04-08T11:58:37.604Z", "updated_at": "2025-04-28T05:54:52.193Z", "type": "HOMEWORK", "section": "ACCESS_CONTROL", "code": "HOMEWORK_ACCESS_MANAGEMENT_DEFAULT_ASSIGNMENT_PARAMETERS", "name": "Параметры назначения по умолчанию", "payload": {"context": {"form": [{"name": "term", "type": "select", "label": "Срок выполнения", "value": "UNLIMITED", "options": [{"label": "Без срока", "value": "UNLIMITED"}, {"label": "На срок", "value": "DUE_PERIOD"}]}, {"name": "value", "type": "INPUT_NUMBER", "value": "1", "enabled": "25.term=DUE_PERIOD"}, {"name": "units", "type": "SELECT", "value": "WEEKS", "enabled": "25.term=DUE_PERIOD", "options": [{"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "DAYS"}, {"label": "недель", "value": "WEEKS"}, {"label": "меся<PERSON>ев", "value": "MONTHS"}, {"label": "лет", "value": "YEARS"}]}, {"name": "deny_access", "type": "select", "label": "Запретить доступ", "value": "NO_PROHIBIT", "enabled": "25.term=DUE_PERIOD", "options": [{"label": "Не запрещать", "value": "NO_PROHIBIT"}, {"label": "После окончания срока выполнения", "value": "AFTER_DEADLINE"}]}]}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["term"], "properties": {"term": {"enum": ["UNLIMITED", "DUE_PERIOD"], "type": "string"}, "units": {"enum": ["DAYS", "WEEKS", "MONTHS", "YEARS"], "type": "string"}, "value": {"type": "integer"}, "deny_access": {"enum": ["NO_PROHIBIT", "AFTER_DEADLINE"], "type": "string"}}, "additionalProperties": false, "allOf": [{"if": {"properties": {"term": {"const": "DUE_PERIOD"}}}, "then": {"required": ["value", "units", "deny_access"]}}]}}}}, {"model": "content.preference", "pk": "ee5d4288-b374-4665-aaf5-ce9a2440d87f", "fields": {"created_at": "2025-05-27T12:34:07.773Z", "updated_at": "2025-05-27T12:34:07.773Z", "type": "COURSE", "section": "NOTIFICATION", "code": "COURSE_NOTIFICATION_OF_APPOINTMENT", "name": "Уведомление о назначении", "payload": {"context": {"form": [{"name": "notify", "type": "CHECKBOX", "label": "Уведомлять пользователей о назначении курса", "value": false}, {"name": "notification.subject", "type": "INPUT_TEXT", "label": "Тема", "value": "У вас появились новые курсы", "enabled": "26.notify=true"}, {"name": "notification.message", "type": "TEXTAREA", "label": "Текст уведомления", "value": "Добрый день!\n\nВам назначен материал «%TITLE%».\n\nСрок выполнения для материала %DUE_DATE%.\n\n\nЧтобы пройти материал, перейдите по ссылке:\n%LINK%", "enabled": "26.notify=true"}]}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["notify"], "properties": {"notification": {"type": "object", "required": ["subject", "message"], "properties": {"message": {"type": "string", "maxLength": 1000}, "subject": {"type": "string", "maxLength": 255}}}, "notify": {"type": "boolean"}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "43f46950-6367-4814-84d5-f4e815d68f10", "fields": {"created_at": "2025-01-23T12:34:07.773Z", "updated_at": "2025-01-23T12:34:07.773Z", "type": "COURSE", "section": "NOTIFICATION", "code": "COURSE_REMINDER_BEFORE_THE_COMPLETION_DATE", "name": "Напоминание перед сроком завершения", "payload": {"context": {"form": [{"name": "notify", "type": "CHECKBOX", "label": "Напомнить пользователям о приближении срока завершения", "value": false}, {"name": "data.intervals", "type": "ARRAY", "enabled": "27.notify=true", "children": [{"name": "value", "type": "INPUT_NUMBER", "value": 1}, {"help": "до срока завершения", "name": "units", "type": "SELECT", "value": "d", "options": [{"label": "минут", "value": "m"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "h"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "d"}]}]}, {"name": "data.notification.subject", "type": "INPUT_TEXT", "label": "Тема", "value": "Необходимо завершить курс «%CONTENT_TITLE%»", "enabled": "27.notify=true"}, {"name": "data.notification.message", "type": "TEXTAREA", "label": "Текст напоминания", "value": "Напоминаем, что приближается время завершения курса.\n\n Курс: %CONTENT_TITLE%.\n  Завершить курс до %DUE_DATE%\n\nПерейдите по ссылке для доступа к курсу:\n%COURSE_INFO_URL%\n", "enabled": "27.notify=true"}], "disable": false}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["notify", "data"], "properties": {"data": {"type": "object", "required": ["notification", "intervals"], "properties": {"intervals": {"type": "array", "items": {"type": "object", "required": ["units", "value"], "properties": {"units": {"enum": ["m", "h", "d"], "type": "string"}, "value": {"type": "integer", "minimum": 1}}, "additionalProperties": false}}, "notification": {"type": "object", "required": ["subject", "message"], "properties": {"message": {"type": "string", "maxLength": 1000}, "subject": {"type": "string", "maxLength": 255}}}}, "additionalProperties": false}, "notify": {"type": "boolean"}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "08cb8053-7777-407e-bcb5-088188c442ff", "fields": {"created_at": "2025-01-23T12:34:07.773Z", "updated_at": "2025-01-23T12:34:07.773Z", "type": "COURSE", "section": "NOTIFICATION", "code": "COURSE_REMINDER_AFTER_THE_COMPLETION_DATE", "name": "Напоминание после срока завершения", "payload": {"context": {"form": [{"name": "notify", "type": "CHECKBOX", "label": "Напоминать пользователям о просроченном обучении через", "order": 1, "value": false}, {"name": "data.intervals", "type": "ARRAY", "enabled": "28.notify=true", "children": [{"name": "value", "type": "INPUT_NUMBER", "order": 1, "value": "1"}, {"help": "после срока", "name": "units", "type": "SELECT", "order": 2, "value": "w", "options": [{"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "d"}, {"label": "недель", "value": "w"}]}]}, {"name": "data.notification.subject", "type": "INPUT_TEXT", "label": "Тема", "value": "Завершите курс «%CONTENT_TITLE%»", "enabled": "28.notify=true"}, {"name": "data.notification.message", "type": "TEXTAREA", "label": "Текст напоминания", "value": "Здравствуйте, %USERNAME%!\n\nСрок прохождения курса «%CONTENT_TITLE%» истек. Курс все еще можно изучить, не откладывайте: %COURSE_INFO_URL%", "enabled": "28.notify=true"}], "disable": false}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["notify", "data"], "properties": {"data": {"type": "object", "required": ["notification", "intervals"], "properties": {"intervals": {"type": "array", "items": {"type": "object", "required": ["units", "value"], "properties": {"units": {"enum": ["d", "w"], "type": "string"}, "value": {"type": "integer", "minimum": 1}}, "additionalProperties": false}}, "notification": {"type": "object", "required": ["subject", "message"], "properties": {"message": {"type": "string", "maxLength": 1000}, "subject": {"type": "string", "maxLength": 255}}}}, "additionalProperties": false}, "notify": {"type": "boolean"}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "17e7282a-83b8-4bb4-99a4-17e85bf693ee", "fields": {"created_at": "2025-01-23T12:34:07.773Z", "updated_at": "2025-01-23T12:34:07.773Z", "type": "COURSE", "section": "NOTIFICATION", "code": "COURSE_COMPLETION_NOTIFICATION", "name": "Уведомлениe о завершении", "payload": {"context": {"form": [{"name": "notify", "type": "CHECKBOX", "label": "Уведомлять администраторов, когда пользователь прошел обучение", "order": 1, "value": false}, {"name": "notification.users", "type": "AUTOCOMPLETE", "order": 1, "route": "USERS", "value": [], "enabled": "29.notify=true", "placeholder": "Поиск"}, {"name": "notification.subject", "type": "INPUT_TEXT", "label": "Тема", "value": "Сотрудник %USERNAME% завершил материал «%CONTENT_TITLE%»", "enabled": "29.notify=true"}, {"name": "notification.message", "type": "TEXTAREA", "label": "Текст напоминания", "value": "Здравствуйте!\n\nСотрудник %USERNAME% успешно завершил материал «%CONTENT_TITLE%».\n\nВы можете ознакомиться с деталями прохождения и результатами по ссылке: %COURSE_INFO_URL%\n\nЕсли материал требует проверки или оценки — перейдите по ссылке, чтобы продолжить работу.", "enabled": "29.notify=true"}], "disable": false}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["notify"], "properties": {"notification": {"type": "object", "required": ["users", "subject", "message"], "properties": {"users": {"type": "array", "items": {"type": "object", "required": ["name", "email"], "properties": {"name": {"type": "string", "maxLength": 255}, "email": {"type": "string", "format": "email", "maxLength": 255}}, "additionalProperties": false}}, "subject": {"type": "string", "maxLength": 255}, "message": {"type": "string", "maxLength": 1000}}, "additionalProperties": false}, "notify": {"type": "boolean"}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "cbd7296f-583e-43c6-907c-a1def7cf9bbb", "fields": {"created_at": "2025-05-27T12:34:07.773Z", "updated_at": "2025-05-27T12:34:07.773Z", "type": "LEARNING_TRACK", "section": "NOTIFICATION", "code": "LEARNING_TRACK_NOTIFICATION_OF_APPOINTMENT", "name": "Уведомление о назначении", "payload": {"context": {"form": [{"name": "notify", "type": "CHECKBOX", "label": "Уведомлять пользователей о назначении курса", "value": false}, {"name": "notification.subject", "type": "INPUT_TEXT", "label": "Тема", "value": "У вас появились новые курсы", "enabled": "30.notify=true"}, {"name": "notification.message", "type": "TEXTAREA", "label": "Текст уведомления", "value": "Добрый день!\n\nВам назначен материал «%TITLE%».\n\nСрок выполнения для материала %DUE_DATE%.\n\n\nЧтобы пройти материал, перейдите по ссылке:\n%LINK%", "enabled": "30.notify=true"}]}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["notify"], "properties": {"notification": {"type": "object", "required": ["subject", "message"], "properties": {"message": {"type": "string", "maxLength": 1000}, "subject": {"type": "string", "maxLength": 255}}}, "notify": {"type": "boolean"}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "0b1a4a8e-adde-4c7c-96a6-be955bc36b6f", "fields": {"created_at": "2025-01-23T12:34:07.773Z", "updated_at": "2025-01-23T12:34:07.773Z", "type": "LEARNING_TRACK", "section": "NOTIFICATION", "code": "LEARNING_TRACK_REMINDER_BEFORE_THE_COMPLETION_DATE", "name": "Напоминание перед сроком завершения", "payload": {"context": {"form": [{"name": "notify", "type": "CHECKBOX", "label": "Напомнить пользователям о приближении срока завершения", "value": false}, {"name": "data.intervals", "type": "ARRAY", "enabled": "31.notify=true", "children": [{"name": "value", "type": "INPUT_NUMBER", "value": 1}, {"help": "до срока завершения", "name": "units", "type": "SELECT", "value": "d", "options": [{"label": "минут", "value": "m"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "h"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "d"}]}]}, {"name": "data.notification.subject", "type": "INPUT_TEXT", "label": "Тема", "value": "Необходимо завершить траекторию обучения «%CONTENT_TITLE%»", "enabled": "31.notify=true"}, {"name": "data.notification.message", "type": "TEXTAREA", "label": "Текст напоминания", "value": "Напоминаем, что приближается время завершения траектории обучения.\n\n  Траектория обучения: %CONTENT_TITLE%.\n  Завершить траекторию обучения до %DUE_DATE%\n\nПерейдите по ссылке для доступа к траектории обучения:\n%COURSE_INFO_URL%\n", "enabled": "31.notify=true"}], "disable": false}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["notify", "data"], "properties": {"data": {"type": "object", "required": ["notification", "intervals"], "properties": {"intervals": {"type": "array", "items": {"type": "object", "required": ["units", "value"], "properties": {"units": {"enum": ["m", "h", "d"], "type": "string"}, "value": {"type": "integer", "minimum": 1}}, "additionalProperties": false}}, "notification": {"type": "object", "required": ["subject", "message"], "properties": {"message": {"type": "string", "maxLength": 1000}, "subject": {"type": "string", "maxLength": 255}}}}, "additionalProperties": false}, "notify": {"type": "boolean"}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "ca4acf1a-f71e-4886-b0ba-a14c8e530c86", "fields": {"created_at": "2025-01-23T12:34:07.773Z", "updated_at": "2025-01-23T12:34:07.773Z", "type": "LEARNING_TRACK", "section": "NOTIFICATION", "code": "LEARNING_TRACK_REMINDER_AFTER_THE_COMPLETION_DATE", "name": "Напоминание после срока завершения", "payload": {"context": {"form": [{"name": "notify", "type": "CHECKBOX", "label": "Напоминать пользователям о просроченном обучении через", "order": 1, "value": false}, {"name": "data.intervals", "type": "ARRAY", "enabled": "32.notify=true", "children": [{"name": "value", "type": "INPUT_NUMBER", "order": 1, "value": "1"}, {"help": "после срока", "name": "units", "type": "SELECT", "order": 2, "value": "w", "options": [{"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "d"}, {"label": "недель", "value": "w"}]}]}, {"name": "data.notification.subject", "type": "INPUT_TEXT", "label": "Тема", "value": "Завершите траекторию «%CONTENT_TITLE%»", "enabled": "32.notify=true"}, {"name": "data.notification.message", "type": "TEXTAREA", "label": "Текст напоминания", "value": "Здравствуйте, %USERNAME%!\n\nСрок прохождения траектории «%CONTENT_TITLE%» истек. Траекторию все еще можно пройти, не откладывайте: %COURSE_INFO_URL%", "enabled": "32.notify=true"}], "disable": false}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["notify", "data"], "properties": {"data": {"type": "object", "required": ["notification", "intervals"], "properties": {"intervals": {"type": "array", "items": {"type": "object", "required": ["units", "value"], "properties": {"units": {"enum": ["d", "w"], "type": "string"}, "value": {"type": "integer", "minimum": 1}}, "additionalProperties": false}}, "notification": {"type": "object", "required": ["subject", "message"], "properties": {"message": {"type": "string", "maxLength": 1000}, "subject": {"type": "string", "maxLength": 255}}}}, "additionalProperties": false}, "notify": {"type": "boolean"}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "fdc60df4-c856-461e-808a-f6b03b9f00da", "fields": {"created_at": "2025-01-23T12:34:07.773Z", "updated_at": "2025-01-23T12:34:07.773Z", "type": "LEARNING_TRACK", "section": "NOTIFICATION", "code": "LEARNING_TRACK_COMPLETION_NOTIFICATION", "name": "Уведомлениe о завершении", "payload": {"context": {"form": [{"name": "notify", "type": "CHECKBOX", "label": "Уведомлять администраторов, когда пользователь прошел траекторию", "order": 1, "value": false}, {"name": "notification.users", "type": "AUTOCOMPLETE", "order": 1, "route": "USERS", "value": [], "enabled": "33.notify=true", "placeholder": "Поиск"}, {"name": "notification.subject", "type": "INPUT_TEXT", "label": "Тема", "value": "Сотрудник %USERNAME% завершил материал «%CONTENT_TITLE%»", "enabled": "33.notify=true"}, {"name": "notification.message", "type": "TEXTAREA", "label": "Текст напоминания", "value": "Здравствуйте!\n\nСотрудник %USERNAME% успешно завершил материал «%CONTENT_TITLE%».\n\nВы можете ознакомиться с деталями прохождения и результатами по ссылке: %COURSE_INFO_URL%\n\nЕсли материал требует проверки или оценки — перейдите по ссылке, чтобы продолжить работу.", "enabled": "33.notify=true"}], "disable": false}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["notify"], "properties": {"notification": {"type": "object", "required": ["users", "subject", "message"], "properties": {"users": {"type": "array", "items": {"type": "object", "required": ["name", "email"], "properties": {"name": {"type": "string", "maxLength": 255}, "email": {"type": "string", "format": "email", "maxLength": 255}}, "additionalProperties": false}}, "subject": {"type": "string", "maxLength": 255}, "message": {"type": "string", "maxLength": 1000}}, "additionalProperties": false}, "notify": {"type": "boolean"}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "a99b5e23-93b2-4599-bb93-aa0bae96b730", "fields": {"created_at": "2025-04-08T10:15:10.698Z", "updated_at": "2025-04-08T10:15:10.698Z", "type": "LEARNING_TRACK", "section": "ACCESS_CONTROL", "code": "LEARNING_TRACK_ACCESS_MANAGEMENT_AUTO_ASSIGNMENT", "name": "Автоматическое назначение", "payload": {"context": {"form": [{"name": "auto_assignment", "type": "ARRAY", "order": 1, "children": [{"name": "condition", "type": "select", "label": "Когда учащийся добавлен", "value": "department", "options": [{"label": "в подразделение", "order": 1, "value": "department"}, {"label": "в подразделение (или дочернее)", "order": 2, "value": "department_wth_children"}, {"label": "в группу", "order": 3, "value": "group"}]}, {"name": "condition_value.value", "type": "FETCH_SELECT", "model": "Department", "value": null, "enabled": "34.auto_assignment.$.condition=department"}, {"name": "condition_value.value", "type": "FETCH_SELECT", "model": "Department", "value": null, "enabled": "34.auto_assignment.$.condition=department_wth_children"}, {"name": "condition_value.value", "type": "FETCH_SELECT", "model": "Group", "value": null, "enabled": "34.auto_assignment.$.condition=group"}, {"name": "condition_value.model", "type": "HIDDEN", "value": "Department", "enabled": "34.auto_assignment.$.condition=department"}, {"name": "condition_value.model", "type": "HIDDEN", "value": "Department", "enabled": "34.auto_assignment.$.condition=department_wth_children"}, {"name": "condition_value.model", "type": "HIDDEN", "value": "Group", "enabled": "34.auto_assignment.$.condition=group"}]}]}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["auto_assignment"], "properties": {"auto_assignment": {"type": "array", "items": {"type": "object", "allOf": [{"if": {"properties": {"condition": {"const": "department"}}}, "then": {"properties": {"condition_value": {"properties": {"model": {"const": "Department"}}}}}}, {"if": {"properties": {"condition": {"const": "department_wth_children"}}}, "then": {"properties": {"condition_value": {"properties": {"model": {"const": "Department"}}}}}}, {"if": {"properties": {"condition": {"const": "group"}}}, "then": {"properties": {"condition_value": {"properties": {"model": {"const": "Group"}}}}}}], "required": ["condition", "condition_value"], "properties": {"condition": {"enum": ["department", "department_wth_children", "group"], "type": "string"}, "condition_value": {"type": "object", "required": ["model", "value"], "properties": {"model": {"type": "string", "maxLength": 255}, "value": {"type": "integer", "minimum": 1}}}}}}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "10504e2e-1deb-4240-a113-88a0417788a8", "fields": {"created_at": "2025-04-08T10:54:05.050Z", "updated_at": "2025-04-16T12:39:33.917Z", "type": "LEARNING_TRACK", "section": "ACCESS_CONTROL", "code": "LEARNING_TRACK_ACCESS_MANAGEMENT_ADDING_CATALOG", "name": "Добавление в каталог", "payload": {"context": {"form": [{"name": "adding_catalog", "type": "ARRAY", "order": 1, "children": [{"name": "condition_value.value", "type": "FETCH_SELECT", "label": "Категории", "model": "Catalog", "value": null}, {"name": "condition_value.model", "type": "HIDDEN", "value": "Catalog"}, {"name": "access_by_claim", "type": "CHECKBOX", "label": "Доступ по заявке", "value": false}]}]}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["adding_catalog"], "properties": {"adding_catalog": {"type": "array", "items": {"type": "object", "required": ["condition_value"], "properties": {"access_by_claim": {"type": "boolean"}, "condition_value": {"type": "object", "required": ["model", "value"], "properties": {"model": {"type": "string", "maxLength": 255}, "value": {"type": "integer", "minimum": 1}}}}}}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "45819f96-8bd4-4141-9661-b08669545b6d", "fields": {"created_at": "2025-04-08T11:58:37.604Z", "updated_at": "2025-04-08T12:01:33.557Z", "type": "LEARNING_TRACK", "section": "ACCESS_CONTROL", "code": "LEARNING_TRACK_ACCESS_MANAGEMENT_DEFAULT_ASSIGNMENT_PARAMETERS", "name": "Параметры назначения по умолчанию", "payload": {"context": {"form": [{"name": "term", "type": "select", "label": "Срок выполнения", "value": "UNLIMITED", "options": [{"label": "Без срока", "value": "UNLIMITED"}, {"label": "На срок", "value": "DUE_PERIOD"}]}, {"name": "value", "type": "INPUT_NUMBER", "value": "1", "enabled": "36.term=DUE_PERIOD"}, {"name": "units", "type": "SELECT", "value": "WEEKS", "enabled": "36.term=DUE_PERIOD", "options": [{"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "DAYS"}, {"label": "недель", "value": "WEEKS"}, {"label": "меся<PERSON>ев", "value": "MONTHS"}, {"label": "лет", "value": "YEARS"}]}, {"name": "deny_access", "type": "select", "label": "Запретить доступ", "value": "NO_PROHIBIT", "enabled": "36.term=DUE_PERIOD", "options": [{"label": "Не запрещать", "value": "NO_PROHIBIT"}, {"label": "После окончания срока выполнения", "value": "AFTER_DEADLINE"}]}]}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["term"], "properties": {"term": {"enum": ["UNLIMITED", "DUE_PERIOD"], "type": "string"}, "units": {"enum": ["DAYS", "WEEKS", "MONTHS", "YEARS"], "type": "string"}, "value": {"type": "integer"}, "deny_access": {"enum": ["NO_PROHIBIT", "AFTER_DEADLINE"], "type": "string"}}, "additionalProperties": false, "allOf": [{"if": {"properties": {"term": {"const": "DUE_PERIOD"}}}, "then": {"required": ["value", "units", "deny_access"]}}]}}}}, {"model": "content.preference", "pk": "771c53db-2e20-4a0d-a971-387af4430956", "fields": {"created_at": "2025-01-23T12:34:07.773Z", "updated_at": "2025-01-23T12:34:07.773Z", "type": "ONLINE_QUIZ", "section": "NOTIFICATION", "code": "ONLINE_QUIZ_NOTIFICATION_OF_APPOINTMENT", "name": "Уведомление о назначении", "payload": {"context": {"form": [{"name": "notify", "type": "CHECKBOX", "label": "Уведомлять пользователей о назначении курса", "value": false}, {"name": "notification.subject", "type": "INPUT_TEXT", "label": "Тема", "value": "У вас появились новые курсы", "enabled": "37.notify=true"}, {"name": "notification.message", "type": "TEXTAREA", "label": "Текст уведомления", "value": "Добрый день!\n\nВам назначен материал «%TITLE%».\n\nСрок выполнения для материала %DUE_DATE%.\n\n\nЧтобы пройти материал, перейдите по ссылке:\n%LINK%", "enabled": "37.notify=true"}]}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["notify"], "properties": {"notification": {"type": "object", "required": ["subject", "message"], "properties": {"message": {"type": "string", "maxLength": 1000}, "subject": {"type": "string", "maxLength": 255}}}, "notify": {"type": "boolean"}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "4d5c9dd2-d903-4840-aae0-04d7ed93517b", "fields": {"created_at": "2025-01-23T12:34:07.773Z", "updated_at": "2025-01-23T12:34:07.773Z", "type": "ONLINE_QUIZ", "section": "NOTIFICATION", "code": "ONLINE_QUIZ_REMINDER_BEFORE_THE_COMPLETION_DATE", "name": "Напоминание перед сроком завершения", "payload": {"context": {"form": [{"name": "notify", "type": "CHECKBOX", "label": "Напомнить пользователям о приближении срока завершения", "value": false}, {"name": "data.intervals", "type": "ARRAY", "enabled": "38.notify=true", "children": [{"name": "value", "type": "INPUT_NUMBER", "value": 1}, {"help": "до срока завершения", "name": "units", "type": "SELECT", "value": "d", "options": [{"label": "минут", "value": "m"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "h"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "d"}]}]}, {"name": "data.notification.subject", "type": "INPUT_TEXT", "label": "Тема", "value": "Необходимо завершить материал «%CONTENT_TITLE%»", "enabled": "38.notify=true"}, {"name": "data.notification.message", "type": "TEXTAREA", "label": "Текст напоминания", "value": "Напоминаем, что приближается время завершения материала.\n\n  Материал: %CONTENT_TITLE%.\n  Завершить материал до %DUE_DATE%\n\nПерейдите по ссылке для доступа к материалу:\n%COURSE_INFO_URL%\n", "enabled": "38.notify=true"}], "disable": false}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["notify", "data"], "properties": {"data": {"type": "object", "required": ["notification", "intervals"], "properties": {"intervals": {"type": "array", "items": {"type": "object", "required": ["units", "value"], "properties": {"units": {"enum": ["m", "h", "d"], "type": "string"}, "value": {"type": "integer", "minimum": 1}}, "additionalProperties": false}}, "notification": {"type": "object", "required": ["subject", "message"], "properties": {"message": {"type": "string", "maxLength": 1000}, "subject": {"type": "string", "maxLength": 255}}}}, "additionalProperties": false}, "notify": {"type": "boolean"}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "9fd53a25-e38c-4ad3-a92d-684f93580e5f", "fields": {"created_at": "2025-01-23T12:34:07.773Z", "updated_at": "2025-01-23T12:34:07.773Z", "type": "ONLINE_QUIZ", "section": "NOTIFICATION", "code": "ONLINE_QUIZ_REMINDER_AFTER_THE_COMPLETION_DATE", "name": "Напоминание после срока завершения", "payload": {"context": {"form": [{"name": "notify", "type": "CHECKBOX", "label": "Напоминать пользователям о просроченном обучении через", "order": 1, "value": false}, {"name": "data.intervals", "type": "ARRAY", "enabled": "39.notify=true", "children": [{"name": "value", "type": "INPUT_NUMBER", "order": 1, "value": "1"}, {"help": "после срока", "name": "units", "type": "SELECT", "order": 2, "value": "w", "options": [{"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "d"}, {"label": "недель", "value": "w"}]}]}, {"name": "data.notification.subject", "type": "INPUT_TEXT", "label": "Тема", "value": "Завершите материал «%CONTENT_TITLE%»", "enabled": "39.notify=true"}, {"name": "data.notification.message", "type": "TEXTAREA", "label": "Текст напоминания", "value": "Здравствуйте, %USERNAME%!\n\nСрок прохождения материала «%CONTENT_TITLE%» истек. Материал все еще можно изучить, не откладывайте: %COURSE_INFO_URL%", "enabled": "39.notify=true"}], "disable": false}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["notify", "data"], "properties": {"data": {"type": "object", "required": ["notification", "intervals"], "properties": {"intervals": {"type": "array", "items": {"type": "object", "required": ["units", "value"], "properties": {"units": {"enum": ["d", "w"], "type": "string"}, "value": {"type": "integer", "minimum": 1}}, "additionalProperties": false}}, "notification": {"type": "object", "required": ["subject", "message"], "properties": {"message": {"type": "string", "maxLength": 1000}, "subject": {"type": "string", "maxLength": 255}}}}, "additionalProperties": false}, "notify": {"type": "boolean"}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "a51c10dd-6971-4127-8364-adfc12d1329f", "fields": {"created_at": "2025-01-23T12:34:07.773Z", "updated_at": "2025-01-23T12:34:07.773Z", "type": "ONLINE_QUIZ", "section": "NOTIFICATION", "code": "ONLINE_QUIZ_COMPLETION_NOTIFICATION", "name": "Уведомлениe о завершении", "payload": {"context": {"form": [{"name": "notify", "type": "CHECKBOX", "label": "Уведомлять администраторов, когда пользователь прошел обучение", "order": 1, "value": false}, {"name": "notification.users", "type": "AUTOCOMPLETE", "order": 1, "route": "USERS", "value": [], "enabled": "40.notify=true", "placeholder": "Поиск"}, {"name": "notification.subject", "type": "INPUT_TEXT", "label": "Тема", "value": "Сотрудник %USERNAME% завершил материал «%CONTENT_TITLE%»", "enabled": "40.notify=true"}, {"name": "notification.message", "type": "TEXTAREA", "label": "Текст напоминания", "value": "Здравствуйте!\n\nСотрудник %USERNAME% успешно завершил материал «%CONTENT_TITLE%».\n\nВы можете ознакомиться с деталями прохождения и результатами по ссылке: %COURSE_INFO_URL%\n\nЕсли материал требует проверки или оценки — перейдите по ссылке, чтобы продолжить работу.", "enabled": "40.notify=true"}], "disable": false}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["notify"], "properties": {"notification": {"type": "object", "required": ["users", "subject", "message"], "properties": {"users": {"type": "array", "items": {"type": "object", "required": ["name", "email"], "properties": {"name": {"type": "string", "maxLength": 255}, "email": {"type": "string", "format": "email", "maxLength": 255}}, "additionalProperties": false}}, "subject": {"type": "string", "maxLength": 255}, "message": {"type": "string", "maxLength": 1000}}, "additionalProperties": false}, "notify": {"type": "boolean"}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "be99ac19-8f42-4ec4-99f7-fd7a53e84bb7", "fields": {"created_at": "2025-05-29 03:37:17.520865 +00:00", "updated_at": "2025-05-29 05:47:37.255519 +00:00", "type": "LONGREAD", "section": "COMPLETION", "code": "LONGREAD_END_OF_COURSE", "name": "Завершение", "payload": {"context": {"form": [{"name": "certification", "type": "CHECKBOX", "label": "Выдать сертификат после завершения материала", "value": false}, {"name": "certificate_type", "type": "select", "label": "Сертификат", "enabled": "41.certification=true", "options": [{"label": "Стандартный", "value": "standart"}]}, {"name": "limit_type", "type": "select", "label": "На период", "enabled": "41.certification=true", "options": [{"label": "Без срока", "value": "unlimited"}, {"label": "На срок", "value": "limited"}]}, {"name": "value", "type": "INPUT_NUMBER", "order": 1, "value": "1", "enabled": "41.limit_type=limited"}, {"name": "units", "type": "SELECT", "order": 2, "value": "w", "enabled": "41.limit_type=limited", "options": [{"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "d"}, {"label": "недель", "value": "w"}, {"label": "год", "value": "y"}]}, {"url": "https://test-university.kaspi.kz/edu/api/v1/content/course/show_template", "type": "BUTTON_LINK", "label": "Посмотреть шаблон", "enabled": "41.certification=true"}]}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["certification"], "properties": {"limit_type": {"enum": ["limited", "unlimited"], "type": "string"}, "certification": {"type": "boolean"}, "certificate_type": {"enum": ["standart"], "type": "string"}}}, "additionalProperties": false}}}, {"model": "content.preference", "pk": "bd6480ac-1d47-417a-a11c-fd69a43f8184", "fields": {"created_at": "2025-01-23T12:34:07.773Z", "updated_at": "2025-01-23T12:34:07.773Z", "type": "LEARNING_PATH", "section": "NOTIFICATION", "code": "LEARNING_PATH_NOTIFICATION_OF_APPOINTMENT", "name": "Уведомление о назначении", "payload": {"context": {"form": [{"name": "notify", "type": "CHECKBOX", "label": "Уведомлять пользователей о назначении курса", "value": false}, {"name": "notification.subject", "type": "INPUT_TEXT", "label": "Тема", "value": "У вас появились новые курсы", "enabled": "42.notify=true"}, {"name": "notification.message", "type": "TEXTAREA", "label": "Текст уведомления", "value": "Добрый день!\n\nВам назначен материал «%TITLE%».\n\nСрок выполнения для материала %DUE_DATE%.\n\n\nЧтобы пройти материал, перейдите по ссылке:\n%LINK%", "enabled": "42.notify=true"}]}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["notify"], "properties": {"notification": {"type": "object", "required": ["subject", "message"], "properties": {"message": {"type": "string", "maxLength": 1000}, "subject": {"type": "string", "maxLength": 255}}}, "notify": {"type": "boolean"}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "9963f2df-b4b1-4681-815c-ce386013416b", "fields": {"created_at": "2025-01-23T12:34:07.773Z", "updated_at": "2025-01-23T12:34:07.773Z", "type": "LEARNING_PATH", "section": "NOTIFICATION", "code": "LEARNING_PATH_REMINDER_BEFORE_THE_COMPLETION_DATE", "name": "Напоминание перед сроком завершения", "payload": {"context": {"form": [{"name": "notify", "type": "CHECKBOX", "label": "Напомнить пользователям о приближении срока завершения", "value": false}, {"name": "data.intervals", "type": "ARRAY", "enabled": "43.notify=true", "children": [{"name": "value", "type": "INPUT_NUMBER", "value": 1}, {"help": "до срока завершения", "name": "units", "type": "SELECT", "value": "d", "options": [{"label": "минут", "value": "m"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "h"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "d"}]}]}, {"name": "data.notification.subject", "type": "INPUT_TEXT", "label": "Тема", "value": "Необходимо завершить материал «%CONTENT_TITLE%»", "enabled": "43.notify=true"}, {"name": "data.notification.message", "type": "TEXTAREA", "label": "Текст напоминания", "value": "Напоминаем, что приближается время завершения материала.\n\n  Материал: %CONTENT_TITLE%.\n  Завершить материал до %DUE_DATE%\n\nПерейдите по ссылке для доступа к материалу:\n%COURSE_INFO_URL%\n", "enabled": "43.notify=true"}], "disable": false}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["notify", "data"], "properties": {"data": {"type": "object", "required": ["notification", "intervals"], "properties": {"intervals": {"type": "array", "items": {"type": "object", "required": ["units", "value"], "properties": {"units": {"enum": ["m", "h", "d"], "type": "string"}, "value": {"type": "integer", "minimum": 1}}, "additionalProperties": false}}, "notification": {"type": "object", "required": ["subject", "message"], "properties": {"message": {"type": "string", "maxLength": 1000}, "subject": {"type": "string", "maxLength": 255}}}}, "additionalProperties": false}, "notify": {"type": "boolean"}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "80835dc7-99cf-410d-ba42-9cee96e07beb", "fields": {"created_at": "2025-01-23T12:34:07.773Z", "updated_at": "2025-01-23T12:34:07.773Z", "type": "LEARNING_PATH", "section": "NOTIFICATION", "code": "LEARNING_PATH_REMINDER_AFTER_THE_COMPLETION_DATE", "name": "Напоминание после срока завершения", "payload": {"context": {"form": [{"name": "notify", "type": "CHECKBOX", "label": "Напоминать пользователям о просроченном обучении через", "order": 1, "value": false}, {"name": "data.intervals", "type": "ARRAY", "enabled": "44.notify=true", "children": [{"name": "value", "type": "INPUT_NUMBER", "order": 1, "value": "1"}, {"help": "после срока", "name": "units", "type": "SELECT", "order": 2, "value": "w", "options": [{"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "d"}, {"label": "недель", "value": "w"}]}]}, {"name": "data.notification.subject", "type": "INPUT_TEXT", "label": "Тема", "value": "Завершите материал «%CONTENT_TITLE%»", "enabled": "44.notify=true"}, {"name": "data.notification.message", "type": "TEXTAREA", "label": "Текст напоминания", "value": "Здравствуйте, %USERNAME%!\n\nСрок прохождения материала «%CONTENT_TITLE%» истек. Материал все еще можно изучить, не откладывайте: %COURSE_INFO_URL%", "enabled": "44.notify=true"}], "disable": false}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["notify", "data"], "properties": {"data": {"type": "object", "required": ["notification", "intervals"], "properties": {"intervals": {"type": "array", "items": {"type": "object", "required": ["units", "value"], "properties": {"units": {"enum": ["d", "w"], "type": "string"}, "value": {"type": "integer", "minimum": 1}}, "additionalProperties": false}}, "notification": {"type": "object", "required": ["subject", "message"], "properties": {"message": {"type": "string", "maxLength": 1000}, "subject": {"type": "string", "maxLength": 255}}}}, "additionalProperties": false}, "notify": {"type": "boolean"}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "63267b3d-f99d-4343-b79f-826941b400a4", "fields": {"created_at": "2025-01-23T12:34:07.773Z", "updated_at": "2025-01-23T12:34:07.773Z", "type": "LEARNING_PATH", "section": "NOTIFICATION", "code": "LEARNING_PATH_COMPLETION_NOTIFICATION", "name": "Уведомлениe о завершении", "payload": {"context": {"form": [{"name": "notify", "type": "CHECKBOX", "label": "Уведомлять администраторов, когда пользователь прошел обучение", "order": 1, "value": false}, {"name": "notification.users", "type": "AUTOCOMPLETE", "order": 1, "route": "USERS", "value": [], "enabled": "45.notify=true", "placeholder": "Поиск"}, {"name": "notification.subject", "type": "INPUT_TEXT", "label": "Тема", "value": "Сотрудник %USERNAME% завершил материал «%CONTENT_TITLE%»", "enabled": "45.notify=true"}, {"name": "notification.message", "type": "TEXTAREA", "label": "Текст напоминания", "value": "Здравствуйте!\n\nСотрудник %USERNAME% успешно завершил материал «%CONTENT_TITLE%».\n\nВы можете ознакомиться с деталями прохождения и результатами по ссылке: %COURSE_INFO_URL%\n\nЕсли материал требует проверки или оценки — перейдите по ссылке, чтобы продолжить работу.", "enabled": "45.notify=true"}], "disable": false}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["notify", "notification"], "properties": {"notification": {"type": "object", "required": ["users", "subject", "message"], "properties": {"users": {"type": "array", "items": {"type": "object", "required": ["name", "email"], "properties": {"name": {"type": "string", "maxLength": 255}, "email": {"type": "string", "format": "email", "maxLength": 255}}, "additionalProperties": false}}, "subject": {"type": "string", "maxLength": 255}, "message": {"type": "string", "maxLength": 1000}}, "additionalProperties": false}, "notify": {"type": "boolean"}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "4e40c2f3-048e-42f8-9f0d-83def99d2580", "fields": {"created_at": "2025-01-23T12:34:07.773Z", "updated_at": "2025-01-23T12:34:07.773Z", "type": "HOMEWORK", "section": "NOTIFICATION", "code": "HOMEWORK_NOTIFICATION_OF_APPOINTMENT", "name": "Уведомление о назначении", "payload": {"context": {"form": [{"name": "notify", "type": "CHECKBOX", "label": "Уведомлять пользователей о назначении курса", "value": false}, {"name": "notification.subject", "type": "INPUT_TEXT", "label": "Тема", "value": "У вас появились новые курсы", "enabled": "46.notify=true"}, {"name": "notification.message", "type": "TEXTAREA", "label": "Текст уведомления", "value": "Добрый день!\n\nВам назначен материал «%TITLE%».\n\nСрок выполнения для материала %DUE_DATE%.\n\n\nЧтобы пройти материал, перейдите по ссылке:\n%LINK%", "enabled": "46.notify=true"}]}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["notify"], "properties": {"notification": {"type": "object", "required": ["subject", "message"], "properties": {"message": {"type": "string", "maxLength": 1000}, "subject": {"type": "string", "maxLength": 255}}}, "notify": {"type": "boolean"}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "592b92ca-ad4f-4c98-a501-30095593ddb1", "fields": {"created_at": "2025-01-23T12:34:07.773Z", "updated_at": "2025-01-23T12:34:07.773Z", "type": "HOMEWORK", "section": "NOTIFICATION", "code": "HOMEWORK_REMINDER_BEFORE_THE_COMPLETION_DATE", "name": "Напоминание перед сроком завершения", "payload": {"context": {"form": [{"name": "notify", "type": "CHECKBOX", "label": "Напомнить пользователям о приближении срока завершения", "value": false}, {"name": "data.intervals", "type": "ARRAY", "enabled": "47.notify=true", "children": [{"name": "value", "type": "INPUT_NUMBER", "value": 1}, {"help": "до срока завершения", "name": "units", "type": "SELECT", "value": "d", "options": [{"label": "минут", "value": "m"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "h"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "d"}]}]}, {"name": "data.notification.subject", "type": "INPUT_TEXT", "label": "Тема", "value": "Необходимо завершить материал «%CONTENT_TITLE%»", "enabled": "47.notify=true"}, {"name": "data.notification.message", "type": "TEXTAREA", "label": "Текст напоминания", "value": "Напоминаем, что приближается время завершения материала.\n\n  Материал: %CONTENT_TITLE%.\n  Завершить материал до %DUE_DATE%\n\nПерейдите по ссылке для доступа к материалу:\n%COURSE_INFO_URL%\n", "enabled": "47.notify=true"}], "disable": false}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["notify", "data"], "properties": {"data": {"type": "object", "required": ["notification", "intervals"], "properties": {"intervals": {"type": "array", "items": {"type": "object", "required": ["units", "value"], "properties": {"units": {"enum": ["m", "h", "d"], "type": "string"}, "value": {"type": "integer", "minimum": 1}}, "additionalProperties": false}}, "notification": {"type": "object", "required": ["subject", "message"], "properties": {"message": {"type": "string", "maxLength": 1000}, "subject": {"type": "string", "maxLength": 255}}}}, "additionalProperties": false}, "notify": {"type": "boolean"}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "2f046f05-3b20-4ebe-923a-6e1ae8ba6bdb", "fields": {"created_at": "2025-01-23T12:34:07.773Z", "updated_at": "2025-01-23T12:34:07.773Z", "type": "HOMEWORK", "section": "NOTIFICATION", "code": "HOMEWORK_REMINDER_AFTER_THE_COMPLETION_DATE", "name": "Напоминание после срока завершения", "payload": {"context": {"form": [{"name": "notify", "type": "CHECKBOX", "label": "Напоминать пользователям о просроченном обучении через", "order": 1, "value": false}, {"name": "data.intervals", "type": "ARRAY", "enabled": "48.notify=true", "children": [{"name": "value", "type": "INPUT_NUMBER", "order": 1, "value": "1"}, {"help": "после срока", "name": "units", "type": "SELECT", "order": 2, "value": "w", "options": [{"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "d"}, {"label": "недель", "value": "w"}]}]}, {"name": "data.notification.subject", "type": "INPUT_TEXT", "label": "Тема", "value": "Завершите материал «%CONTENT_TITLE%»", "enabled": "48.notify=true"}, {"name": "data.notification.message", "type": "TEXTAREA", "label": "Текст напоминания", "value": "Здравствуйте, %USERNAME%!\n\nСрок прохождения материала «%CONTENT_TITLE%» истек. Материал все еще можно изучить, не откладывайте: %COURSE_INFO_URL%", "enabled": "48.notify=true"}], "disable": false}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["notify", "data"], "properties": {"data": {"type": "object", "required": ["notification", "intervals"], "properties": {"intervals": {"type": "array", "items": {"type": "object", "required": ["units", "value"], "properties": {"units": {"enum": ["d", "w"], "type": "string"}, "value": {"type": "integer", "minimum": 1}}, "additionalProperties": false}}, "notification": {"type": "object", "required": ["subject", "message"], "properties": {"message": {"type": "string", "maxLength": 1000}, "subject": {"type": "string", "maxLength": 255}}}}, "additionalProperties": false}, "notify": {"type": "boolean"}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "2191fafa-d1a5-4e63-8693-717c448e48ac", "fields": {"created_at": "2025-01-23T12:34:07.773Z", "updated_at": "2025-01-23T12:34:07.773Z", "type": "HOMEWORK", "section": "NOTIFICATION", "code": "HOMEWORK_COMPLETION_NOTIFICATION", "name": "Уведомлениe о завершении", "payload": {"context": {"form": [{"name": "notify", "type": "CHECKBOX", "label": "Уведомлять администраторов, когда пользователь прошел обучение", "order": 1, "value": false}, {"name": "notification.users", "type": "AUTOCOMPLETE", "order": 1, "route": "USERS", "value": [], "enabled": "49.notify=true", "placeholder": "Поиск"}, {"name": "notification.subject", "type": "INPUT_TEXT", "label": "Тема", "value": "Сотрудник %USERNAME% завершил материал «%CONTENT_TITLE%»", "enabled": "49.notify=true"}, {"name": "notification.message", "type": "TEXTAREA", "label": "Текст напоминания", "value": "Здравствуйте!\n\nСотрудник %USERNAME% успешно завершил материал «%CONTENT_TITLE%».\n\nВы можете ознакомиться с деталями прохождения и результатами по ссылке: %COURSE_INFO_URL%\n\nЕсли материал требует проверки или оценки — перейдите по ссылке, чтобы продолжить работу.", "enabled": "49.notify=true"}], "disable": false}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["notify", "notification"], "properties": {"notification": {"type": "object", "required": ["users", "subject", "message"], "properties": {"users": {"type": "array", "items": {"type": "object", "required": ["name", "email"], "properties": {"name": {"type": "string", "maxLength": 255}, "email": {"type": "string", "format": "email", "maxLength": 255}}, "additionalProperties": false}}, "subject": {"type": "string", "maxLength": 255}, "message": {"type": "string", "maxLength": 1000}}, "additionalProperties": false}, "notify": {"type": "boolean"}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "24864b19-a8a1-4d17-b250-8582e3c693c5", "fields": {"created_at": "2025-01-23T12:34:07.773Z", "updated_at": "2025-01-23T12:34:07.773Z", "type": "ONLINE_QUIZ", "section": "NOTIFICATION", "code": "ONLINE_QUIZ_NOT_PASSED_NOTIFICATION", "name": "Уведомлениe о непрохождении материала", "payload": {"context": {"form": [{"name": "notify", "type": "CHECKBOX", "label": "Уведомлять администра<PERSON><PERSON><PERSON><PERSON>, если материал не пройден", "order": 1, "value": false}, {"name": "notification.users", "type": "AUTOCOMPLETE", "order": 1, "route": "USERS", "value": [], "enabled": "50.notify=true", "placeholder": "Поиск"}, {"name": "notification.subject", "type": "INPUT_TEXT", "label": "Тема", "value": "Сотрудник %USERNAME% не прошел материал «%CONTENT_TITLE%»", "enabled": "50.notify=true"}, {"name": "notification.message", "type": "TEXTAREA", "label": "Текст напоминания", "value": "Здравствуйте!\n\nСотрудник %USERNAME% не прошел материал «%CONTENT_TITLE%».\n\nВы можете ознакомиться с деталями прохождения и результатами по ссылке: %COURSE_INFO_URL%.", "enabled": "50.notify=true"}], "disable": false}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["notify"], "properties": {"notification": {"type": "object", "required": ["users", "subject", "message"], "properties": {"users": {"type": "array", "items": {"type": "object", "required": ["name", "email"], "properties": {"name": {"type": "string", "maxLength": 255}, "email": {"type": "string", "format": "email", "maxLength": 255}}, "additionalProperties": false}}, "subject": {"type": "string", "maxLength": 255}, "message": {"type": "string", "maxLength": 1000}}, "additionalProperties": false}, "notify": {"type": "boolean"}}, "additionalProperties": false}}}}, {"model": "content.preference", "pk": "8fe491f7-0edb-4de0-9aeb-09c54848d3b6", "fields": {"created_at": "2025-05-29 03:37:17.520865 +00:00", "updated_at": "2025-05-29 05:47:37.255519 +00:00", "type": "COURSE", "section": "COMPLETION", "code": "COURSE_END_OF_COURSE", "name": "Завершение", "payload": {"context": {"form": [{"name": "certification", "type": "CHECKBOX", "label": "Выдать сертификат после завершения материала", "value": false}, {"name": "certificate_type", "type": "select", "label": "Сертификат", "enabled": "51.certification=true", "options": [{"label": "Стандартный", "value": "standart"}]}, {"name": "limit_type", "type": "select", "label": "На период", "enabled": "51.certification=true", "options": [{"label": "Без срока", "value": "unlimited"}, {"label": "На срок", "value": "limited"}]}, {"name": "value", "type": "INPUT_NUMBER", "order": 1, "value": "1", "enabled": "51.limit_type=limited"}, {"name": "units", "type": "SELECT", "order": 2, "value": "w", "enabled": "51.limit_type=limited", "options": [{"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "d"}, {"label": "недель", "value": "w"}, {"label": "год", "value": "y"}]}, {"url": "https://test-university.kaspi.kz/edu/api/v1/content/course/show_template", "type": "BUTTON_LINK", "label": "Посмотреть шаблон", "enabled": "51.certification=true"}]}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["certification"], "properties": {"limit_type": {"enum": ["limited", "unlimited"], "type": "string"}, "certification": {"type": "boolean"}, "certificate_type": {"enum": ["standart"], "type": "string"}}}, "additionalProperties": false}}}, {"model": "content.preference", "pk": "201f0c33-0745-4374-8d08-c5bb07d059d1", "fields": {"created_at": "2025-05-29 03:37:17.520865 +00:00", "updated_at": "2025-05-29 05:47:37.255519 +00:00", "type": "LEARNING_PATH", "section": "COMPLETION", "code": "LEARNING_PATH_END_OF_COURSE", "name": "Завершение", "payload": {"context": {"form": [{"name": "certification", "type": "CHECKBOX", "label": "Выдать сертификат после завершения материала", "value": false}, {"name": "certificate_type", "type": "select", "label": "Сертификат", "enabled": "52.certification=true", "options": [{"label": "Стандартный", "value": "standart"}]}, {"name": "limit_type", "type": "select", "label": "На период", "enabled": "52.certification=true", "options": [{"label": "Без срока", "value": "unlimited"}, {"label": "На срок", "value": "limited"}]}, {"name": "value", "type": "INPUT_NUMBER", "order": 1, "value": "1", "enabled": "52.limit_type=limited"}, {"name": "units", "type": "SELECT", "order": 2, "value": "w", "enabled": "52.limit_type=limited", "options": [{"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "d"}, {"label": "недель", "value": "w"}, {"label": "год", "value": "y"}]}, {"url": "https://test-university.kaspi.kz/edu/api/v1/content/course/show_template", "type": "BUTTON_LINK", "label": "Посмотреть шаблон", "enabled": "52.certification=true"}]}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["certification"], "properties": {"limit_type": {"enum": ["limited", "unlimited"], "type": "string"}, "certification": {"type": "boolean"}, "certificate_type": {"enum": ["standart"], "type": "string"}}}, "additionalProperties": false}}}, {"model": "content.preference", "pk": "84a7494b-3b93-4047-9126-a3133ff296c5", "fields": {"created_at": "2025-05-29 03:37:17.520865 +00:00", "updated_at": "2025-05-29 05:47:37.255519 +00:00", "type": "ONLINE_QUIZ", "section": "COMPLETION", "code": "ONLINE_QUIZ_END_OF_COURSE", "name": "Завершение", "payload": {"context": {"form": [{"name": "certification", "type": "CHECKBOX", "label": "Выдать сертификат после завершения материала", "value": false}, {"name": "certificate_type", "type": "select", "label": "Сертификат", "enabled": "53.certification=true", "options": [{"label": "Стандартный", "value": "standart"}]}, {"name": "limit_type", "type": "select", "label": "На период", "enabled": "53.certification=true", "options": [{"label": "Без срока", "value": "unlimited"}, {"label": "На срок", "value": "limited"}]}, {"name": "value", "type": "INPUT_NUMBER", "order": 1, "value": "1", "enabled": "53.limit_type=limited"}, {"name": "units", "type": "SELECT", "order": 2, "value": "w", "enabled": "53.limit_type=limited", "options": [{"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "d"}, {"label": "недель", "value": "w"}, {"label": "год", "value": "y"}]}, {"url": "https://test-university.kaspi.kz/edu/api/v1/content/course/show_template", "type": "BUTTON_LINK", "label": "Посмотреть шаблон", "enabled": "53.certification=true"}]}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["certification"], "properties": {"limit_type": {"enum": ["limited", "unlimited"], "type": "string"}, "certification": {"type": "boolean"}, "certificate_type": {"enum": ["standart"], "type": "string"}}}, "additionalProperties": false}}}, {"model": "content.preference", "pk": "79127e48-fa94-4ce4-8479-ab084568687f", "fields": {"created_at": "2025-05-29 03:37:17.520865 +00:00", "updated_at": "2025-05-29 05:47:37.255519 +00:00", "type": "HOMEWORK", "section": "COMPLETION", "code": "HOMEWORK_END_OF_COURSE", "name": "Завершение", "payload": {"context": {"form": [{"name": "certification", "type": "CHECKBOX", "label": "Выдать сертификат после завершения материала", "value": false}, {"name": "certificate_type", "type": "select", "label": "Сертификат", "enabled": "54.certification=true", "options": [{"label": "Стандартный", "value": "standart"}]}, {"name": "limit_type", "type": "select", "label": "На период", "enabled": "54.certification=true", "options": [{"label": "Без срока", "value": "unlimited"}, {"label": "На срок", "value": "limited"}]}, {"name": "value", "type": "INPUT_NUMBER", "order": 1, "value": "1", "enabled": "54.limit_type=limited"}, {"name": "units", "type": "SELECT", "order": 2, "value": "w", "enabled": "54.limit_type=limited", "options": [{"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "d"}, {"label": "недель", "value": "w"}, {"label": "год", "value": "y"}]}, {"url": "https://test-university.kaspi.kz/edu/api/v1/content/course/show_template", "type": "BUTTON_LINK", "label": "Посмотреть шаблон", "enabled": "54.certification=true"}]}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["certification"], "properties": {"limit_type": {"enum": ["limited", "unlimited"], "type": "string"}, "certification": {"type": "boolean"}, "certificate_type": {"enum": ["standart"], "type": "string"}}}, "additionalProperties": false}}}, {"model": "content.preference", "pk": "4f64b749-006b-472c-9803-42a6a16a9de9", "fields": {"created_at": "2025-05-29 03:37:17.520865 +00:00", "updated_at": "2025-05-29 05:47:37.255519 +00:00", "type": "LINK", "section": "COMPLETION", "code": "LINK_END_OF_COURSE", "name": "Завершение", "payload": {"context": {"form": [{"name": "certification", "type": "CHECKBOX", "label": "Выдать сертификат после завершения материала", "value": false}, {"name": "certificate_type", "type": "select", "label": "Сертификат", "enabled": "55.certification=true", "options": [{"label": "Стандартный", "value": "standart"}]}, {"name": "limit_type", "type": "select", "label": "На период", "enabled": "55.certification=true", "options": [{"label": "Без срока", "value": "unlimited"}, {"label": "На срок", "value": "limited"}]}, {"name": "value", "type": "INPUT_NUMBER", "order": 1, "value": "1", "enabled": "55.limit_type=limited"}, {"name": "units", "type": "SELECT", "order": 2, "value": "w", "enabled": "55.limit_type=limited", "options": [{"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "d"}, {"label": "недель", "value": "w"}, {"label": "год", "value": "y"}]}, {"url": "https://test-university.kaspi.kz/edu/api/v1/content/course/show_template", "type": "BUTTON_LINK", "label": "Посмотреть шаблон", "enabled": "55.certification=true"}]}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["certification"], "properties": {"limit_type": {"enum": ["limited", "unlimited"], "type": "string"}, "certification": {"type": "boolean"}, "certificate_type": {"enum": ["standart"], "type": "string"}}}, "additionalProperties": false}}}, {"model": "content.preference", "pk": "7c7e5b26-46ed-44d1-b2b0-99<PERSON>b<PERSON><PERSON><PERSON><PERSON>d", "fields": {"created_at": "2025-05-29 03:37:17.520865 +00:00", "updated_at": "2025-05-29 05:47:37.255519 +00:00", "type": "LEARNING_TRACK", "section": "COMPLETION", "code": "LEARNING_TRACK_END_OF_COURSE", "name": "Завершение", "payload": {"context": {"form": [{"name": "certification", "type": "CHECKBOX", "label": "Выдать сертификат после завершения материала", "value": false}, {"name": "certificate_type", "type": "select", "label": "Сертификат", "enabled": "56.certification=true", "options": [{"label": "Стандартный", "value": "standart"}]}, {"name": "limit_type", "type": "select", "label": "На период", "enabled": "56.certification=true", "options": [{"label": "Без срока", "value": "unlimited"}, {"label": "На срок", "value": "limited"}]}, {"name": "value", "type": "INPUT_NUMBER", "order": 1, "value": "1", "enabled": "56.limit_type=limited"}, {"name": "units", "type": "SELECT", "order": 2, "value": "w", "enabled": "56.limit_type=limited", "options": [{"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "d"}, {"label": "недель", "value": "w"}, {"label": "год", "value": "y"}]}, {"url": "https://test-university.kaspi.kz/edu/api/v1/content/course/show_template", "type": "BUTTON_LINK", "label": "Посмотреть шаблон", "enabled": "56.certification=true"}]}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["certification"], "properties": {"limit_type": {"enum": ["limited", "unlimited"], "type": "string"}, "certification": {"type": "boolean"}, "certificate_type": {"enum": ["standart"], "type": "string"}}}, "additionalProperties": false}}}, {"model": "content.preference", "pk": "ece62ae8-d57c-4243-9c16-b049852b35b6", "fields": {"created_at": "2025-06-26 15:35:45.896000 +00:00", "updated_at": "2025-06-26 15:35:52.876000 +00:00", "type": "LONGREAD", "section": "COMPLETION", "code": "LONGREAD_COMPLETION_REASSIGN_COURSE", "name": "Переназначение курса", "payload": {"context": {"form": [{"name": "resource_reassignment", "type": "checkbox", "label": "Переназначить курс после завершения курса", "value": false}, {"name": "resource_reassignment_type", "type": "select", "label": "Когда переназначить курс", "enabled": "57.resource_reassignment=true", "value": "after_completion", "options": [{"label": "После завершения курса", "value": "after_completion"}, {"label": "До истечения сертификата", "value": "before_certificate_expire"}]}, {"name": "after_completion_value", "type": "input_number", "order": 1, "value": 0, "enabled": "57.resource_reassignment_type=after_completion;57.resource_reassignment=true"}, {"name": "after_completion_units", "type": "select", "order": 2, "value": "after_completion_units_days", "enabled": "57.resource_reassignment_type=after_completion;57.resource_reassignment=true", "options": [{"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "after_completion_units_days"}, {"label": "недель", "value": "after_completion_units_weeks"}, {"label": "меся<PERSON>ев", "value": "after_completion_units_months"}, {"label": "лет", "value": "after_completion_units_years"}]}, {"name": "before_certificate_expire_value", "type": "input_number", "order": 3, "value": 0, "enabled": "57.resource_reassignment_type=before_certificate_expire;57.resource_reassignment=true"}, {"name": "before_certificate_expire_units", "type": "select", "enabled": "57.resource_reassignment_type=before_certificate_expire;57.resource_reassignment=true", "value": "before_certificate_expire_days", "order": 4, "options": [{"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "before_certificate_expire_days"}, {"label": "недель", "value": "before_certificate_expire_weeks"}, {"label": "меся<PERSON>ев", "value": "before_certificate_expire_months"}, {"label": "лет", "value": "before_certificate_expire_years"}]}]}, "validation": {"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["resource_reassignment"], "properties": {"resource_reassignment": {"type": "boolean"}, "resource_reassignment_type": {"type": "string", "enum": ["after_completion", "before_certificate_expire"]}, "after_completion_value": {"type": "integer", "minimum": 0, "maximum": 3650}, "after_completion_units": {"type": "string", "enum": ["after_completion_units_days", "after_completion_units_weeks", "after_completion_units_months", "after_completion_units_years"]}, "before_certificate_expire_value": {"type": "integer", "minimum": 0, "maximum": 3650}, "before_certificate_expire_units": {"type": "string", "enum": ["before_certificate_expire_days", "before_certificate_expire_weeks", "before_certificate_expire_months", "before_certificate_expire_years"]}}}, "additionalProperties": false}}}]