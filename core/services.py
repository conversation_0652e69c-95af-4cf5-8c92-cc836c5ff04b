import boto3
from django.conf import settings
from lms.s3_storage import PublicMediaStorage


class S3Service:
    def __init__(self):
        self.s3_client = boto3.client(
            service_name='s3',
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            endpoint_url=settings.AWS_S3_ENDPOINT_URL,
        )

    def get_download_file_url(self, file_path):
        url = self.s3_client.generate_presigned_url(ClientMethod='get_object',
                                                    Params={'Bucket': settings.AWS_STORAGE_BUCKET_NAME,
                                                            'Key': '{}/{}'.format(settings.AWS_PUBLIC_MEDIA_LOCATION,
                                                                                  file_path)})
        return url

    def retrieve_file_from_s3(self, file_path):
        file_obj = self.s3_client.get_object(
            Bucket=settings.AWS_STORAGE_BUCKET_NAME,
            Key='{}/{}'.format(settings.AWS_PUBLIC_MEDIA_LOCATION, file_path)
        )
        return file_obj

    # check file exists in s3 bucket's files folder
    def check_file_exists(self, file_path):
        try:
            media_storage = PublicMediaStorage()
            return media_storage.exists(file_path)
        except Exception as e:
            return False
