from rest_framework import serializers

from qa.api.v1.serializers.question_serializers import QuestionDetailSerializer
from qa.models import Answer, Question
from users.api.v1.serializers.user_serializers import UserSerializer


class AnswerListSerializer(serializers.ModelSerializer):
    question = QuestionDetailSerializer(read_only=True)
    author = UserSerializer(read_only=True)

    class Meta:
        model = Answer
        fields = [
            "id",
            "question",
            "author",
            "text",
            "created_at",
            "updated_at",
        ]


class AnswerCreateSerializer(serializers.ModelSerializer):
    question_id = serializers.PrimaryKeyRelatedField(
        queryset=Question.objects.all(), write_only=True, source="question"
    )

    class Meta:
        model = Answer
        fields = [
            "id",
            "question_id",
            "text",
        ]

    def validate_question_id(self, question):
        if question.is_answered:
            pass
        return question


class AnswerDetailSerializer(serializers.ModelSerializer):
    class Meta:
        model = Answer
        fields = ["id", "text", "created_at", "updated_at"]
