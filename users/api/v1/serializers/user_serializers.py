from django.contrib.auth import password_validation
from django.db import transaction
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers
from rest_framework.validators import UniqueValidator

from users.api.v1.serializers.department_serializers import (
    CompanySerializer,
    DepartmentManagementSerializer,
    PositionManagementSerializer,
)
from users.models import (
    Company,
    Country,
    Department,
    Group,
    Position,
    Role,
    Status,
    User,
    UserCompany,
    UserRoleDepartment,
    UserStatus,
    UserSupervisor,
    UserSupervisorSettings,
)


class UserMinimalSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ("id", "first_name", "last_name", "photo")


class StatusSerializer(serializers.ModelSerializer):
    class Meta:
        model = Status
        fields = ("id", "name", "file")
        read_only_fields = ["id"]


class UserStatusSerializer(serializers.ModelSerializer):
    status = StatusSerializer()

    class Meta:
        model = UserStatus
        fields = ("start_at", "end_at", "status")


class CountrySerializer(serializers.ModelSerializer):
    class Meta:
        model = Country
        fields = (
            "id",
            "name",
        )


class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ("id", "username", "first_name", "last_name", "email")


class UserSupervisorSerializer(serializers.ModelSerializer):
    type = serializers.ChoiceField(choices=UserSupervisor.SUPERVISOR_CHOICES)
    supervisor = serializers.PrimaryKeyRelatedField(queryset=User.objects.all())

    class Meta:
        model = UserSupervisor
        fields = ("supervisor", "type")


class UserRequestSerializer(serializers.ModelSerializer):
    supervisors = UserSupervisorSerializer(many=True, write_only=True, required=False)
    company = serializers.PrimaryKeyRelatedField(
        queryset=Company.objects.all(), required=False, allow_null=True
    )
    department = serializers.PrimaryKeyRelatedField(
        queryset=Department.objects.all(), required=False, allow_null=True
    )
    position = serializers.PrimaryKeyRelatedField(
        queryset=Position.objects.all(), required=False, allow_null=True
    )

    class Meta:
        model = User
        fields = (
            "id",
            "first_name",
            "last_name",
            "username",
            "email",
            "phone",
            "country",
            "birth_date",
            "about",
            "company",
            "department",
            "position",
            "supervisors",
        )
        read_only_fields = ("id",)

    def update(self, instance, validated_data):
        supervisors_data = validated_data.pop("supervisors", None)
        company = validated_data.pop("company", None)
        department = validated_data.pop("department", None)
        position = validated_data.pop("position", None)

        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        with transaction.atomic():
            if supervisors_data is not None:
                instance.supervisors.clear()
                objs = [
                    UserSupervisor(
                        user=instance, supervisor=s["supervisor"], type=s["type"]
                    )
                    for s in supervisors_data
                ]
                UserSupervisor.objects.bulk_create(objs)

            if (company is None) ^ (department is None) ^ (position is None):
                raise serializers.ValidationError(
                    {
                        "company": _(
                            "Company, department and position must be all set or all omitted."
                        ),
                        "department": _(
                            "Company, department and position must be all set or all omitted."
                        ),
                        "position": _(
                            "Company, department and position must be all set or all omitted."
                        ),
                    }
                )

            if company is not None:
                UserCompany.objects.update_or_create(
                    user=instance,
                    defaults={
                        "company": company,
                        "department": department,
                        "position": position,
                    },
                )

        return instance


class UserCreateSerializer(serializers.Serializer):
    iin = serializers.CharField(
        max_length=12,
        validators=[UniqueValidator(queryset=User.objects.all())],
    )
    username = serializers.CharField(
        max_length=150,
        validators=[UniqueValidator(queryset=User.objects.all())],
    )
    email = serializers.EmailField(
        validators=[UniqueValidator(queryset=User.objects.all())],
    )
    password = serializers.CharField(write_only=True)
    first_name = serializers.CharField(max_length=150, required=False, allow_blank=True)
    last_name = serializers.CharField(max_length=150, required=False, allow_blank=True)
    middle_name = serializers.CharField(
        max_length=150, required=False, allow_blank=True
    )
    phone = serializers.CharField(max_length=20, required=False, allow_blank=True)
    birth_date = serializers.DateField(required=False, allow_null=True)

    country = serializers.PrimaryKeyRelatedField(
        queryset=Country.objects.all(), required=False, allow_null=True
    )

    company = serializers.PrimaryKeyRelatedField(
        queryset=Company.objects.all(),
        required=True,
    )
    department = serializers.PrimaryKeyRelatedField(
        queryset=Department.objects.all(),
        required=True,
    )
    position = serializers.PrimaryKeyRelatedField(
        queryset=Position.objects.all(),
        required=True,
    )

    # Роли и группы
    roles = serializers.ListField(
        child=serializers.PrimaryKeyRelatedField(queryset=Role.objects.all()),
        required=False,
        allow_empty=True,
        default=list,
        write_only=True,
    )
    departments = serializers.ListField(
        child=serializers.PrimaryKeyRelatedField(queryset=Department.objects.all()),
        required=False,
        allow_empty=True,
        default=list,
        write_only=True,
    )
    groups = serializers.ListField(
        child=serializers.PrimaryKeyRelatedField(queryset=Group.objects.all()),
        required=False,
        allow_empty=True,
        default=list,
        write_only=True,
    )

    DEFAULT_ROLE_ID = 1  # ID роли «Обучающийся»

    def validate_password(self, value):
        password_validation.validate_password(value)
        return value

    def validate(self, data):
        # Убедимся, что company/department/position указаны все вместе
        company = data.get("company")
        department = data.get("department")
        position = data.get("position")
        if not (company and department and position):
            raise serializers.ValidationError(
                {
                    "company": _("This field is required."),
                    "department": _("This field is required."),
                    "position": _("This field is required."),
                }
            )
        # Проверка ролей, требующих departments
        roles = data.get("roles", [])
        depts_for_roles = data.get("departments", [])
        for role in roles:
            if getattr(role, "has_department", False) and not depts_for_roles:
                raise serializers.ValidationError(
                    {
                        "departments": _(
                            "This field is required when a selected role requires a department."
                        )
                    }
                )
        return data

    def create(self, validated_data: dict):
        roles = validated_data.pop("roles", [])
        depts_for_roles = validated_data.pop("departments", [])
        groups = validated_data.pop("groups", [])
        company = validated_data.pop("company")
        department = validated_data.pop("department")
        position = validated_data.pop("position")
        password = validated_data.pop("password")

        with transaction.atomic():
            # 1) Создаём пользователя
            user = User.objects.create_user(**validated_data, password=password)

            # 2) Роли (либо заданные, либо дефолтная)
            if roles:
                user.roles.set(roles)
            else:
                user.roles.add(self.DEFAULT_ROLE_ID)

            # 3) Для ролей, требующих department, создаём UserRoleDepartment
            for role in roles:
                if getattr(role, "has_department", False):
                    for dept in depts_for_roles:
                        UserRoleDepartment.objects.get_or_create(
                            user=user, role=role, department=dept
                        )

            # 4) Группы
            if groups:
                user.groups.set(groups)

            # 5) Всегда создаём UserCompany, так как поля обязательны
            UserCompany.objects.create(
                user=user, company=company, department=department, position=position
            )

        return user

    def to_representation(self, instance):
        from users.api.v1.serializers.shared_serializers import UserManagementSerializer
        # Возвращаем полное представление через UserManagementSerializer
        return UserManagementSerializer(instance).data

class UserCompanySerializer(serializers.ModelSerializer):
    company = CompanySerializer(read_only=True)
    department = DepartmentManagementSerializer(read_only=True)
    position = PositionManagementSerializer(read_only=True)

    class Meta:
        model = UserCompany
        fields = ("company", "department", "position")

class UserEnrollmentSerializer(serializers.ModelSerializer):
    country = CountrySerializer(read_only=True)
    department = serializers.SerializerMethodField()
    companies = UserCompanySerializer(many=True, read_only=True)
    position = serializers.SerializerMethodField()
    status_label = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = (
            "id",
            "email",
            "first_name",
            "last_name",
            "username",
            "department",
            "companies",
            "country",
            "position",
            "date_joined",
            "last_login",
            "groups_count",
            "status_label",
        )

    def get_department(self, obj):
        aff = getattr(obj, "user_companies", None)
        if aff:
            dept = aff[0].department
            return DepartmentManagementSerializer(dept).data
        return None

    def get_position(self, obj):
        aff = getattr(obj, "user_companies", None)
        if aff:
            pos = aff[0].position
            return PositionManagementSerializer(pos).data
        return None

    def get_status_label(self, obj):
        return obj.get_status_label()


class UserAutocompleteSerializer(serializers.ModelSerializer):
    # Аннотированное поле
    name = serializers.CharField(read_only=True)
    country = CountrySerializer(read_only=True)
    companies = UserCompanySerializer(many=True, read_only=True)

    class Meta:
        model = User
        fields = (
            "id",
            "name",
            "username",
            "first_name",
            "last_name",
            "email",
            "phone",
            "photo",
            "country",
            "birth_date",
            "companies",
        )