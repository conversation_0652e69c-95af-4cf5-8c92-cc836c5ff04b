from django.urls import path

from ..views import (
    DetailUpdateView,
    EnrollmentListView,
    PreferenceListView,
    ResourceCreateView,
    ResourceListView,
    ResourcePayloadUpdateView,
    ResourceRestoreView,
    ResourceRetrieveDestroyView,
    ResourceTreeListView,
    ResourceTreeRetrieveView,
    ResourceTypeListView,
    ResourceUpdateView,
    SettingsListUpdateView,
    SettingsListView,
)

urlpatterns = [
    path('', ResourceListView.as_view()),
    path('create/', ResourceCreateView.as_view()),
    path('tree/', ResourceTreeListView.as_view()),
    path('types/', ResourceTypeListView.as_view()),
    path('<uuid:pk>/', ResourceRetrieveDestroyView.as_view()),
    path('<uuid:pk>/restore', ResourceRestoreView.as_view()),
    path('<uuid:pk>/tree/', ResourceTreeRetrieveView.as_view()),
    path('<uuid:pk>/update', ResourceUpdateView.as_view()),
    path('<uuid:pk>/settings/', SettingsListView.as_view()),
    path('<uuid:pk>/settings/update/', SettingsListUpdateView.as_view()),
    path('<uuid:pk>/payload/', ResourcePayloadUpdateView.as_view()),
    path('<str:type>/preferences/', PreferenceListView.as_view()),
    path('<uuid:pk>/details/<int:detail_id>/update/', DetailUpdateView.as_view()),
    path('<uuid:pk>/enrollments/', EnrollmentListView.as_view()),
]