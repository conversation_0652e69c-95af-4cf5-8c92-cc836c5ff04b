import io
import logging
from datetime import datetime, timedelta

from dateutil.relativedelta import relativedelta
from django.core.files.base import ContentFile
from pdfrw import PdfReader, PdfDict, PdfWriter

from content.models import Enrollment, Certification
from core.services import S3Service


logger = logging.getLogger("app")


class CertificateService:

    @staticmethod
    def mark_expired_certificates():
        expired_cert = Certification.objects.filter(expired_date__lt=datetime.now())
        if expired_cert:
            for cert in expired_cert:
                cert.status = Certification.EXPIRED
                cert.save()

    def generate(self, enrollment_id: int, settings: dict):
        enrollment = Enrollment.objects.get(id=enrollment_id)
        s3 = S3Service()
        template_obj = s3.retrieve_file_from_s3('files/templates/certificates/certificate_template.pdf')
        template_bytes = template_obj['Body'].read()

        pdf_reader = PdfReader(fdata=template_bytes)
        for page in pdf_reader.pages:
            annotations = page['/Annots']
            if annotations:
                for annotation in annotations:
                    field_name = annotation.get('/T')
                    if field_name == '(name)':
                        annotation.update(PdfDict(V=enrollment.user.full_name))
                    if field_name == '(finished)':
                        annotation.update(PdfDict(V='Завершил (-ла) курс'))
                    if field_name == '(course)':
                        annotation.update(PdfDict(V=enrollment.resource.name))
                    if field_name == '(date)':
                        annotation.update(PdfDict(V=enrollment.completed_at.strftime('%d.%m.%Y')))
                    if field_name == '(kaspi)':
                        annotation.update(PdfDict(V='Kaspi.kz'))

        output_buffer = io.BytesIO()
        PdfWriter(output_buffer, trailer=pdf_reader).write()
        output_buffer.seek(0)

        pdf_filename = f'certificate_enrollment_{enrollment.id}.pdf'
        pdf_file = ContentFile(output_buffer.read(), name=pdf_filename)

        self.get_or_create_certificate(
            enrollment,
            self.parse_expired_date(enrollment.completed_at, settings),
            pdf_filename,
            pdf_file
        )

    @staticmethod
    def get_or_create_certificate(
            enrollment: Enrollment,
            expired_date: datetime | None,
            pdf_filename: str,
            pdf_file: ContentFile
    ):
        certification, _ = Certification.objects.get_or_create(enrollment=enrollment, defaults={
            'user': enrollment.user,
            'issue_date': enrollment.completed_at,
            'expired_date': expired_date,
        })
        certification.path.save(pdf_filename, pdf_file, save=True)

    @staticmethod
    def parse_expired_date(issue_date: datetime, expired_for: dict):
        if expired_for.get('payload', {}).get('limit_type', 'unlimited') == 'unlimited':
            return None

        units = expired_for.get('payload', {}).get('units')
        value = expired_for.get('payload', {}).get('value', 0)

        if units == 'd':
            return issue_date + timedelta(days=value)
        elif units == 'm':
            return issue_date + relativedelta(months=value)
        elif units == 'y':
            return issue_date + relativedelta(years=value)
        else:
            logger.error(f"Wrong unit of date type: {units}")
            raise ValueError(f'Неверная единица времени: {units}')