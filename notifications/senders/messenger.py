from .base import BaseSender
import logging

logger = logging.getLogger('app')


class MessengerSender(BaseSender):
    def send(self):
        # Здесь обычно интеграция с мессенджером (Telegram Bot API, Facebook Messenger API)
        print(f"Sending Messenger message to {self.notification.recipient}: {self.notification.message}")
        logger.info(f"Sending Messenger ID {self.notification.pk} sent to - {self.notification.recipient}.")
