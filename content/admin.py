from importlib.resources._common import _

from django.contrib import admin
from django.db.models import <PERSON><PERSON><PERSON><PERSON>
from django_json_widget.widgets import JSONEditorWidget
from django.db import transaction

from content.forms import ResourceFrom, DueDateSettingsForm
from content.models import Resource, Participant, Detail, Tag, Settings, Preference, Catalog, CatalogResource, \
    CatalogAccessGroup, CatalogAccessDepartment, ResourceApplication, DueDateSettings, Enrollment
from content.tasks.enrollment import send_appointment_notifications

class ParticipantAdmin(admin.TabularInline):
    model = Participant
    autocomplete_fields = ('user',)
    extra = 1


@admin.register(Detail)
class DetailAdmin(admin.ModelAdmin):
    list_display = ('id', 'resource', 'instructor', 'created_at', 'updated_at')
    list_display_links = ('resource',)
    autocomplete_fields = ('instructor', 'resource')
    show_full_result_count = False


@admin.register(Tag)
class TagAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'created_at', 'updated_at')
    list_display_links = ('name',)
    show_full_result_count = False


@admin.register(Resource)
class ResourceAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'type', 'created_at', 'updated_at')
    autocomplete_fields = ('owner', 'parent')
    search_fields = ('id', 'name')
    list_display_links = ('name',)
    form = ResourceFrom
    show_full_result_count = False
    inlines = [ParticipantAdmin, ]
    formfield_overrides = {
        JSONField: {'widget': JSONEditorWidget},
    }


@admin.register(Preference)
class PreferenceAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'type', 'code', 'section', 'created_at', 'updated_at')
    list_display_links = ('name',)
    search_fields = ('id', 'name')
    show_full_result_count = False
    formfield_overrides = {
        JSONField: {'widget': JSONEditorWidget},
    }
    list_editable = ('code',)
    ordering = ('-id',)


@admin.register(Settings)
class SettingsAdmin(admin.ModelAdmin):
    list_display = ('id', 'preference', 'order', 'resource', 'created_at', 'updated_at')
    search_fields = ('id', 'preference__name')
    autocomplete_fields = ('preference', 'resource')
    list_display_links = ('preference',)
    show_full_result_count = False
    formfield_overrides = {
        JSONField: {'widget': JSONEditorWidget},
    }
    ordering = ('-id',)


class DueDateSettingsInline(admin.StackedInline):
    model = DueDateSettings
    form = DueDateSettingsForm
    extra = 1
    can_delete = False
    show_change_link = True


@admin.register(Enrollment)
class EnrollmentAdmin(admin.ModelAdmin):
    autocomplete_fields = ('user', 'resource')
    list_display = ('id', 'user', 'resource', 'access_date', 'status', 'completed_at')
    list_filter = ('status', 'access_date', 'completed_at')
    search_fields = ('user__email', 'resource__name')
    ordering = ('-id',)
    inlines = [DueDateSettingsInline]
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        (None, {
            'fields': ('user', 'resource', 'access_date', 'status', 'completed_at', 'change_reason')
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
        }),
    )

    def save_model(self, request, obj, form, change):
        is_new = obj.pk is None
        super().save_model(request, obj, form, change)
        if is_new:
            transaction.on_commit(lambda: send_appointment_notifications.delay([obj.id]))


class CatalogAccessGroupInline(admin.TabularInline):
    model = CatalogAccessGroup
    extra = 1  # количество пустых форм
    autocomplete_fields = ['group']
    fields = ('group', )


class CatalogAccessDepartmentInline(admin.TabularInline):
    model = CatalogAccessDepartment
    extra = 1
    autocomplete_fields = ['department']
    fields = ('department',)


@admin.register(Catalog)
class CatalogAdmin(admin.ModelAdmin):
    list_display = ('name', 'description', 'order', 'is_public', )
    search_fields = ('name', 'description')
    ordering = ('order',)
    inlines = [CatalogAccessGroupInline, CatalogAccessDepartmentInline]


@admin.register(CatalogResource)
class CatalogResourceAdmin(admin.ModelAdmin):
    list_display = ('catalog', 'resource', 'access_by_claim')
    list_filter = ('access_by_claim', 'catalog', 'resource')
    search_fields = ('catalog__name', 'resource__name')
    list_editable = ('access_by_claim',)
    autocomplete_fields = ('catalog', 'resource')


@admin.register(ResourceApplication)
class ResourceApplicationAdmin(admin.ModelAdmin):
    list_display = ('user', 'resource', 'status')
    list_filter = ('status',)
    search_fields = ('user__email', 'resource__name')
    autocomplete_fields = ('user', 'resource')