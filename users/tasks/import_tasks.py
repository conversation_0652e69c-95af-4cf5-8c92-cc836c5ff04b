import requests
from celery import shared_task

from users.tasks.services.import_user_is_boss import ImportUserIsBossService
from .services.import_core_service import (
    CompanyImportService,
    UserImportService,
    DepartmentImportService,
    PositionImportService,
    DepartmentPositionImportService
)


@shared_task(bind=True, autoretry_for=(requests.RequestException,), retry_backoff=60, max_retries=3)
def import_companies_task(self, dry_run=False):
    """Задача импорта компаний"""
    service = CompanyImportService(dry_run=dry_run)
    return service.import_companies()


@shared_task(bind=True)
def import_users_task(self, dry_run=False):
    """Задача импорта пользователей"""
    service = UserImportService(dry_run=dry_run)
    return service.import_users()


@shared_task(bind=True)
def import_departments_task(self, dry_run=False):
    """Задача импорта департаментов"""
    service = DepartmentImportService(dry_run=dry_run)
    return service.import_departments()


@shared_task(bind=True, autoretry_for=(requests.RequestException,), retry_backoff=60, max_retries=3)
def import_positions_task(self, dry_run=False):
    """Задача импорта позиций"""
    service = PositionImportService(dry_run=dry_run)
    return service.import_positions()


@shared_task(bind=True, autoretry_for=(requests.RequestException,), retry_backoff=60, max_retries=3)
def import_department_positions_task(self, dry_run=False):
    """Задача импорта связей департамент-позиция"""
    service = DepartmentPositionImportService(dry_run=dry_run)
    return service.import_department_positions()

@shared_task(name="import_user_is_boss")
def import_user_is_boss():
    ImportUserIsBossService().run()