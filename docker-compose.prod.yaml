services:
  api: &api
    image: ${CI_REGISTRY_IMAGE}/${CI_COMMIT_REF_SLUG}:${CI_COMMIT_SHORT_SHA}
    restart: always
    deploy:
      replicas: 2
      placement:
        max_replicas_per_node: 1
    command: /usr/src/app/entrypoint.sh
    volumes:
      - logs:/usr/src/app/logs
      - media:/usr/src/app/media
      - static:/usr/src/app/static
    environment:
      SECRET_KEY: ${PROD_SECRET_KEY}
      DEBUG: ${PROD_DEBUG}
      POSTGRES_DB: ${PROD_POSTGRES_DB_NAME}
      POSTGRES_USER: ${PROD_POSTGRES_DB_USER}
      POSTGRES_PASSWORD: ${PROD_POSTGRES_DB_PASSWORD}
      POSTGRES_HOST: ${PROD_POSTGRES_HOST}
      POSTGRES_PORT: ${PROD_POSTGRES_PORT}
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      AWS_STORAGE_BUCKET_NAME: ${AWS_STORAGE_BUCKET_NAME}
      AWS_S3_ENDPOINT_URL: ${AWS_S3_ENDPOINT_URL}
      AWS_DEFAULT_ACL: ${AWS_DEFAULT_ACL}
      AWS_S3_CUSTOM_DOMAIN: ${PROD_AWS_S3_CUSTOM_DOMAIN} # check
      AWS_STATIC_LOCATION: ${PROD_AWS_STATIC_LOCATION}
      AWS_PUBLIC_MEDIA_LOCATION: ${PROD_AWS_PUBLIC_MEDIA_LOCATION}
      AWS_PRIVATE_MEDIA_LOCATION: ${PROD_AWS_PRIVATE_MEDIA_LOCATION}
      SENTRY_DSN: ${PROD_SENTRY_DSN} # check
      SONAR_TOKEN: ${PROD_SONAR_TOKEN} # check
      REDIS_DEFAULT: ${PROD_REDIS_DEFAULT}
      REDIS_SESSION: ${PROD_REDIS_SESSION}
      CELERY_BROKER_URL: ${PROD_CELERY_BROKER_URL}
      CELERY_RESULT_BACKEND: ${PROD_CELERY_RESULT_BACKEND}
      LRS_URL: ${PROD_LRS_URL}
      LRS_VERSION: ${PROD_LRS_VERSION}
      LRS_USERNAME: ${PROD_LRS_USERNAME}
      LRS_PASSWORD: ${PROD_LRS_PASSWORD}
      FRONTEND_BASE_URL: ${PROD_FRONTEND_BASE_URL}
    ports:
      - target: 8000
        published: 8010
        protocol: tcp
        mode: host
    networks:
      - backend

  scheduler:
    <<: *api
    command: celery -A lms beat -l info
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role == manager
    ports: []
    networks:
      - backend

  worker:
    <<: *api
    command: celery -A lms worker -l INFO
    deploy:
      replicas: 2
    ports: []
    networks:
      - backend

  redis:
    image: repka.kaspi.kz:8443/redis:7.4.2
    command: redis-server --requirepass "pass"
    restart: always
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - backend
    deploy:
      placement:
        constraints:
          - node.role == manager

volumes:
  redis_data:
    driver: local
  logs:
  media:
  static:

networks:
  backend:
    driver: overlay
