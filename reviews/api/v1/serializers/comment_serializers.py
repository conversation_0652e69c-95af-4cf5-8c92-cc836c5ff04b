from rest_framework import serializers

from reviews.models import Comment
from users.api.v1.serializers.user_serializers import UserSerializer


class RecursiveCommentSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    replies = serializers.SerializerMethodField()

    class Meta:
        model = Comment
        fields = ["id", "user", "content", "created_at", "parent", "replies"]

    def get_replies(self, obj):
        return RecursiveCommentSerializer(obj.replies.all(), many=True).data


class CommentSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    parent = serializers.PrimaryKeyRelatedField(
        queryset=Comment.objects.all(), required=False, allow_null=True
    )
    replies = RecursiveCommentSerializer(many=True, read_only=True)

    class Meta:
        model = Comment
        fields = ["id", "review", "user", "content", "created_at", "parent", "replies"]
        read_only_fields = ["id", "user", "created_at", "review"]

    def validate_parent(self, parent):
        """
        Проверка согласованности parent и review.
        """
        review = self.context.get("review") or self.initial_data.get("review")
        if parent and parent.review_id != int(
            review.id if hasattr(review, "id") else review
        ):
            raise serializers.ValidationError(
                "Комментарий должен принадлежать этому же отзыву."
            )
        return parent

    def validate(self, data):
        user = self.context["request"].user
        review = self.context.get("review")
        parent = data.get("parent")

        if self.instance:
            return data

        if (
            not parent
            and Comment.objects.filter(
                user=user, review=review, parent__isnull=True
            ).exists()
        ):
            raise serializers.ValidationError(
                "Вы уже оставили основной комментарий к этому отзыву."
            )
        return data

    def create(self, validated_data):
        return super().create(validated_data)

    def update(self, instance, validated_data):
        # Прямо удаляем защищённые поля
        validated_data.pop("parent", None)
        validated_data.pop("review", None)
        return super().update(instance, validated_data)
