from django.contrib.auth import password_validation
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers

from users.api.v1.serializers.permission_serializers import (
    PermissionSerializer,
    RolePermissionsSerializer,
)
from users.models import Permission, User


class UserMeSerializer(serializers.ModelSerializer):
    roles = RolePermissionsSerializer(many=True, read_only=True)

    class Meta:
        model = User
        fields = (
            "id",
            "username",
            "iin",
            "first_name",
            "last_name",
            "middle_name",
            "email",
            "photo",
            "full_name",
            "is_superuser",
            "roles",
        )


# Расширенная версия с дополнительной информацией о правах
class UserMeDetailedSerializer(UserMeSerializer):
    """Расширенная версия с дополнительной информацией о правах"""

    all_permissions = serializers.SerializerMethodField()
    permission_codes = serializers.SerializerMethodField()
    role_codes = serializers.SerializerMethodField()

    class Meta(UserMeSerializer.Meta):
        fields = list(UserMeSerializer.Meta.fields) + [
            "all_permissions",
            "permission_codes",
            "role_codes",
        ]

    def get_all_permissions(self, obj):
        """Получить все права доступа пользователя из всех ролей"""
        permissions = Permission.objects.filter(roles__users=obj).distinct()
        return PermissionSerializer(permissions, many=True).data

    def get_permission_codes(self, obj):
        """Получить список кодов всех прав доступа"""
        return list(
            Permission.objects.filter(roles__users=obj)
            .distinct()
            .values_list("code", flat=True)
        )

    def get_role_codes(self, obj):
        """Получить список кодов всех ролей"""
        return list(obj.roles.values_list("code", flat=True))


class PasswordChangeSerializer(serializers.Serializer):
    old_password = serializers.CharField(write_only=True)
    new_password = serializers.CharField(write_only=True)
    confirm_password = serializers.CharField(write_only=True)

    def validate_old_password(self, value):
        user = self.context["request"].user
        if not user.check_password(value):
            raise serializers.ValidationError(_("The old password is incorrect"))
        return value

    def validate(self, data):
        if data["new_password"] != data["confirm_password"]:
            raise serializers.ValidationError(_("The new password must match"))
        password_validation.validate_password(
            data["new_password"], user=self.context["request"].user
        )
        return data

    def save(self, **kwargs):
        user = self.context["request"].user
        user.set_password(self.validated_data["new_password"])
        user.save()
        return user


class UserPhotoUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ["photo"]

    def validate(self, attrs):
        if not attrs.get("photo"):
            raise serializers.ValidationError(_("The photo is required"))
        return attrs
