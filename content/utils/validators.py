from django.core.exceptions import ObjectDoesNotExist
from content.utils.utils import get_model_from_string_safe


class ConditionValueValidator:
    #  Единое место, где перечислены ключи
    VALID_KEYS = ['auto_assignment', 'adding_catalog']

    def __init__(self, payload, preference_id, keys_to_check=None):
        self.payload = payload or {}
        self.preference_id = preference_id
        self.keys_to_check = keys_to_check or self.VALID_KEYS
        self.errors = {}

    def validate(self):
        for key in self.keys_to_check:
            items = self.payload.get(key, [])
            if not isinstance(items, list):
                continue  # skip if not a list

            for index, item in enumerate(items):
                cond_val = item.get("condition_value", {})
                model_name = cond_val.get("model")
                obj_id = cond_val.get("value")

                if not model_name or obj_id is None:
                    continue  # skip if incomplete

                try:
                    model = get_model_from_string_safe(model_name)
                except ValueError:
                    self.errors[f"{self.preference_id}[{index}]"] = f"Model '{model_name}' is not allowed"
                    continue

                try:
                    model.objects.get(id=obj_id)
                except ObjectDoesNotExist:
                    self.errors[f"{self.preference_id}[{index}]"] = f"{model_name} with ID {obj_id} does not exist"

        return self.errors
