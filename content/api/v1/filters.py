import django_filters

from content.models import Resource, Enrollment


class ResourceFilter(django_filters.FilterSet):
    parent_id = django_filters.ModelChoiceFilter(
        field_name='parent',
        queryset=Resource.objects.all(),
        null_label='Корневые ресурсы',
    )

    class Meta:
        model = Resource
        fields = ['parent_id', 'is_deleted']


class CourseFilter(django_filters.FilterSet):
    status = django_filters.BaseInFilter(field_name='status', lookup_expr='in')
    name = django_filters.CharFilter(
        field_name='resource__name',
        lookup_expr='icontains',
        label='Resource name (contains)'
    )

    class Meta:
        model = Enrollment
        fields = ['name', 'status']