# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-10 17:02+0500\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || "
"(n%100>=11 && n%100<=14)? 2 : 3);\n"

#: .\content\admin.py:96
msgid "Timestamps"
msgstr "Временные метки"

#: .\content\api\v1\serializers\resource_serializers.py:205
msgid "invalid format"
msgstr "недопустимый формат"

#: .\content\api\v1\serializers\resource_serializers.py:213
msgid "invalid preference key"
msgstr "неверный ключ настройки"

#: .\content\api\v1\serializers\resource_serializers.py:215
msgid "there is no scheme for form validation"
msgstr "не существует схемы для проверки формы"

#: .\content\apps.py:8 .\content\models.py:530 .\content\models.py:546
#: .\notifications\models.py:84
msgid "Content"
msgstr "Контент"

#: .\content\models.py:28
msgid "folder"
msgstr "папка"

#: .\content\models.py:29
msgid "learning track"
msgstr "траектория обучения"

#: .\content\models.py:30
msgid "learning path"
msgstr "обучающий путь"

#: .\content\models.py:31
msgid "link"
msgstr "ссылка"

#: .\content\models.py:32
msgid "homework"
msgstr "задание"

#: .\content\models.py:33
msgid "online quiz"
msgstr "онлайн тест"

#: .\content\models.py:34
msgid "longread"
msgstr "лонгрид"

#: .\content\models.py:35
msgid "chapter"
msgstr "глава"

#: .\content\models.py:36
msgid "stage"
msgstr "этап"

#: .\content\models.py:37
msgid "course"
msgstr "курс"

#: .\content\models.py:38 .\users\models.py:157
#: .\venv\Lib\site-packages\click\types.py:893
msgid "file"
msgstr "файл"

#: .\content\models.py:41 .\content\models.py:102 .\content\models.py:162
#: .\content\models.py:309 .\users\models.py:154 .\users\models.py:201
#: .\users\models.py:393 .\users\models.py:413 .\users\models.py:443
msgid "name"
msgstr "название"

#: .\content\models.py:42 .\content\models.py:310 .\users\models.py:423
#: .\users\models.py:445
msgid "description"
msgstr "описание"

#: .\content\models.py:45
msgid "owner"
msgstr "владелец"

#: .\content\models.py:50
msgid "is shared"
msgstr "является общим"

#: .\content\models.py:53 .\content\models.py:426 .\users\models.py:223
#: .\users\models.py:417
msgid "parent"
msgstr "родитель"

#: .\content\models.py:59 .\content\models.py:164 .\content\models.py:184
#: .\notifications\models.py:48 .\notifications\models.py:139
msgid "payload"
msgstr "полезная  нагрузка"

#: .\content\models.py:60 .\content\models.py:159 .\content\models.py:436
#: .\content\models.py:472 .\content\models.py:511 .\users\models.py:335
msgid "type"
msgstr "тип"

#: .\content\models.py:61 .\content\models.py:185 .\content\models.py:312
#: .\content\models.py:438 .\notifications\models.py:86
#: .\notifications\models.py:111
msgid "order"
msgstr "сортировка"

#: .\content\models.py:67 .\content\models.py:82 .\content\models.py:213
#: .\content\models.py:234 .\content\models.py:525 .\qa\models.py:22
msgid "resource"
msgstr "Ресурс"

#: .\content\models.py:68
msgid "resources"
msgstr "1. Ресурсы"

#: .\content\models.py:78
msgid "preview"
msgstr "просмотр"

#: .\content\models.py:79
msgid "editing"
msgstr "редактирование"

#: .\content\models.py:83 .\content\models.py:214 .\content\models.py:390
#: .\content\models.py:502 .\content\models.py:527
#: .\notifications\models.py:101 .\notifications\models.py:126
#: .\users\models.py:83 .\users\models.py:104 .\users\models.py:132
#: .\users\models.py:179 .\users\models.py:366 .\users\models.py:483
msgid "user"
msgstr "Пользователь"

#: .\content\models.py:84
msgid "availability"
msgstr "доступность"

#: .\content\models.py:88
msgid "You can't participate in this project."
msgstr "Вы не можете участвовать в этом проекте."

#: .\content\models.py:94
msgid "participant"
msgstr "участник"

#: .\content\models.py:95
msgid "participants"
msgstr "участники"

#: .\content\models.py:108
msgid "tag"
msgstr "тег"

#: .\content\models.py:109 .\content\models.py:126
msgid "tags"
msgstr "5. Теги"

#: .\content\models.py:116
msgid "recommended time"
msgstr "рекомендуемое время"

#: .\content\models.py:117 .\content\models.py:311
msgid "thumbnail"
msgstr "эскиз"

#: .\content\models.py:118
msgid "cover"
msgstr "обложка"

#: .\content\models.py:121
msgid "instructor"
msgstr "инструктор"

#: .\content\models.py:132 .\content\models.py:133
msgid "details"
msgstr "2. Основные"

#: .\content\models.py:149
msgid "structure"
msgstr "структура"

#: .\content\models.py:150
msgid "detail"
msgstr "основные"

#: .\content\models.py:151 .\notifications\models.py:73
msgid "notification"
msgstr "уведомление"

#: .\content\models.py:152
msgid "access control"
msgstr "управление доступом"

#: .\content\models.py:153
msgid "completion"
msgstr "завершение"

#: .\content\models.py:154 .\content\models.py:225 .\content\models.py:233
#: .\content\models.py:275 .\content\models.py:497
msgid "enrollment"
msgstr "Назначения"

#: .\content\models.py:155
msgid "report"
msgstr "отчет"

#: .\content\models.py:156
msgid "feedback"
msgstr "отзыв"

#: .\content\models.py:160
msgid "section"
msgstr "описание"

#: .\content\models.py:161 .\notifications\models.py:83
#: .\notifications\models.py:109 .\users\models.py:231 .\users\models.py:394
#: .\users\models.py:414 .\users\models.py:444
msgid "code"
msgstr "код"

#: .\content\models.py:167
msgid "validation via the jsonschema package"
msgstr "проверка с помощью пакета jsonschema"

#: .\content\models.py:174
msgid "preference"
msgstr "шаблон"

#: .\content\models.py:175
msgid "preferences"
msgstr "4. Шаблоны"

#: .\content\models.py:191 .\content\models.py:192
msgid "settings"
msgstr "3. Настройки"

#: .\content\models.py:206
msgid "enrollment_not_started"
msgstr "Не начато"

#: .\content\models.py:207
msgid "enrollment_finished_manually"
msgstr "Завершен(вручную)"

#: .\content\models.py:208
msgid "enrollment_not_finished_manually"
msgstr "Не завершен(вручную)"

#: .\content\models.py:209
msgid "enrollment_finished"
msgstr "Завершен"

#: .\content\models.py:210
msgid "enrollment_in_progress"
msgstr "В прогрессе"

#: .\content\models.py:215
msgid "enrollment_access_date"
msgstr "Дата начала обучения"

#: .\content\models.py:216
msgid "enrollment_status"
msgstr "Статус"

#: .\content\models.py:217 .\content\models.py:238
msgid "enrollment_completed_at"
msgstr "Дата завершения"

#: .\content\models.py:218
msgid "enrollment_change_reason"
msgstr "Причина изменения статуса"

#: .\content\models.py:219 .\content\models.py:236 .\content\models.py:243
msgid "enrollment_progress"
msgstr "Прогресс назначения"

#: .\content\models.py:226
msgid "enrollments"
msgstr "6. Назначения"

#: .\content\models.py:235 .\content\models.py:399 .\users\models.py:167
#: .\users\models.py:183
msgid "status"
msgstr "статус"

#: .\content\models.py:237
#, fuzzy
#| msgid "start at"
msgid "started_at"
msgstr "начните с"

#: .\content\models.py:239
msgid "lrs_statement_id"
msgstr ""

#: .\content\models.py:244
#, fuzzy
#| msgid "enrollment_progress"
msgid "enrollment_progresses"
msgstr "Прогресс назначения"

#: .\content\models.py:257
msgid "due_date_setting_unlimited"
msgstr "Без срока"

#: .\content\models.py:258
msgid "due_date_setting_default"
msgstr "Из настроек курса"

#: .\content\models.py:259
msgid "due_date_setting_due_date"
msgstr "До даты"

#: .\content\models.py:260
msgid "due_date_setting_due_period"
msgstr "На срок"

#: .\content\models.py:269
msgid "period_days"
msgstr "дней"

#: .\content\models.py:270
msgid "period_weeks"
msgstr "недель"

#: .\content\models.py:271
msgid "period_months"
msgstr "месяцев"

#: .\content\models.py:272
msgid "period_years"
msgstr "лет"

#: .\content\models.py:276
msgid "due_date_settings_type"
msgstr "Тип параметры назначения"

#: .\content\models.py:277
msgid "period"
msgstr "период"

#: .\content\models.py:278
msgid "period_unit"
msgstr "единица периода"

#: .\content\models.py:279
msgid "due_date_setting_date"
msgstr "Срок выполнения"

#: .\content\models.py:280
msgid "lock_after_due_date"
msgstr "Запретить доступ"

#: .\content\models.py:302 .\content\models.py:303
msgid "due_date_settings"
msgstr "Параметры назначения"

#: .\content\models.py:313
msgid "is public"
msgstr "публичный"

#: .\content\models.py:329 .\content\models.py:330
msgid "catalogs"
msgstr "8. Каталог"

#: .\content\models.py:339
msgid "access by claim"
msgstr "Доступ по заявке"

#: .\content\models.py:345
msgid "catalog resource"
msgstr "Добавление в каталог"

#: .\content\models.py:346
msgid "catalog resources"
msgstr "9. Связка Каталог и Ресурс"

#: .\content\models.py:382 .\notifications\models.py:19
msgid "Pending"
msgstr "В ожидании"

#: .\content\models.py:383
msgid "Approved"
msgstr "Одобренный"

#: .\content\models.py:384
msgid "Rejected"
msgstr "Отклоненный"

#: .\content\models.py:385
msgid "Canceled"
msgstr "Отменено"

#: .\content\models.py:401
msgid "processed at"
msgstr "обработано в"

#: .\content\models.py:407
msgid "resource application"
msgstr "заявка ресурса"

#: .\content\models.py:408
msgid "resource applications"
msgstr "7. Заявка ресурсов"

#: .\content\models.py:418
msgid "Stage"
msgstr "этап"

#: .\content\models.py:419
msgid "Course"
msgstr "курс"

#: .\content\models.py:444 .\content\models.py:471
msgid "outline"
msgstr "Структура"

#: .\content\models.py:445
msgid "outlines"
msgstr "Структуры"

#: .\content\models.py:455
msgid "unlimited"
msgstr "неограниченный"

#: .\content\models.py:456
msgid "relative"
msgstr ""

#: .\content\models.py:465
msgid "days"
msgstr "дней"

#: .\content\models.py:466
msgid "weeks"
msgstr "недели"

#: .\content\models.py:467
msgid "months"
msgstr "месяца"

#: .\content\models.py:468
msgid "years"
msgstr "годы"

#: .\content\models.py:473
msgid "outline_access_interval"
msgstr ""

#: .\content\models.py:474
msgid "outline_access_period"
msgstr ""

#: .\content\models.py:475
msgid "outline_access_unit"
msgstr ""

#: .\content\models.py:476
msgid "lock_after_expiration"
msgstr "блокировка после истечения срока"

#: .\content\models.py:479
msgid "outline_access_time"
msgstr "Доступ по заявке"

#: .\content\models.py:480
msgid "outline_access_times"
msgstr ""

#: .\content\models.py:491
msgid "active"
msgstr "активировать"

#: .\content\models.py:492
msgid "expired"
msgstr "истекший интервал"

#: .\content\models.py:508
msgid "certificate_issue_date"
msgstr "Дата выдачи сертификата"

#: .\content\models.py:509
msgid "certificate_expired_date"
msgstr "Дата истечения сертификата"

#: .\content\models.py:520
msgid "certificate"
msgstr "уведомление"

#: .\content\models.py:521
msgid "certificates"
msgstr "уведомления"

#: .\content\models.py:528
msgid "rating"
msgstr "рейтинг"

#: .\content\models.py:529 .\notifications\models.py:82
msgid "title"
msgstr "заголовок"

#: .\content\models.py:536
msgid "resource review"
msgstr "Отзыв ресурса"

#: .\content\models.py:537
msgid "resource reviews"
msgstr "Отзывы ресурсов"

#: .\content\models.py:552
msgid "review comment"
msgstr "Комментарий отзыва"

#: .\content\models.py:553
msgid "review comments"
msgstr "Комментарии отзыва"

#: .\core\api\v1\views.py:29
msgid "upload the file"
msgstr "загрузите файл"

#: .\core\models.py:43
msgid "file size in bytes"
msgstr "размер файла в байтах"

#: .\core\models.py:50 .\users\models.py:406
msgid "content type"
msgstr "тип контента"

#: .\core\models.py:60
msgid "media"
msgstr "медиа"

#: .\core\models.py:61
msgid "medias"
msgstr "медиа"

#: .\lms\utils.py:15
msgid "Unsupported file extension. Allowed extensions are: %(extensions)."
msgstr ""
"Неподдерживаемое расширение файла. Разрешенные расширения: %(extensions)."

#: .\lms\validators.py:12
msgid "The password must contain at least one special character: !@#$%^&*()_+-"
msgstr ""
"Пароль должен содержать хотя бы один специальный символ: !@#$%^&*()_+-="

#: .\lms\validators.py:23
msgid "Your password must contain at least one uppercase letter."
msgstr "Ваш пароль должен содержать хотя бы одну заглавную букву."

#: .\lms\validators.py:34
msgid "The password must contain at least one digit."
msgstr "Пароль должен содержать хотя бы одну цифру."

#: .\notifications\models.py:12
msgid "Email"
msgstr "Электронная почта"

#: .\notifications\models.py:13
msgid "SMS"
msgstr "СМС"

#: .\notifications\models.py:14
msgid "Messenger"
msgstr "Мессенджер"

#: .\notifications\models.py:15
msgid "Push Notification"
msgstr "уведомление"

#: .\notifications\models.py:20
msgid "Sent"
msgstr "Отправлено"

#: .\notifications\models.py:21
msgid "Failed"
msgstr "Ошибка отправки"

#: .\notifications\models.py:28 .\notifications\models.py:81
#: .\notifications\models.py:105
msgid "Channel"
msgstr "Канал"

#: .\notifications\models.py:34
msgid "Status"
msgstr "статус"

#: .\notifications\models.py:37 .\notifications\models.py:107
msgid "Recipient"
msgstr "Получатель"

#: .\notifications\models.py:43 .\notifications\models.py:134
msgid "Subject"
msgstr "Тема"

#: .\notifications\models.py:46 .\notifications\models.py:137
msgid "Message"
msgstr "Текст"

#: .\notifications\models.py:52
msgid "Scheduled time"
msgstr "Запланированное время"

#: .\notifications\models.py:57
msgid "Sent time"
msgstr "Время отправки"

#: .\notifications\models.py:62
msgid "Error message"
msgstr "Сообщение об ошибке"

#: .\notifications\models.py:64
msgid "content_type"
msgstr "тип контента"

#: .\notifications\models.py:66
msgid "object_id"
msgstr "ID обьекта"

#: .\notifications\models.py:74
msgid "notifications"
msgstr "уведомления"

#: .\notifications\models.py:80
msgid "label"
msgstr "пометка"

#: .\notifications\models.py:85 .\notifications\models.py:110
msgid "is active"
msgstr "активен"

#: .\notifications\models.py:92
msgid "notification template"
msgstr "шаблон уведомления"

#: .\notifications\models.py:93
msgid "notification templates"
msgstr "шаблоны уведомлений"

#: .\notifications\models.py:117
msgid "notification receiver"
msgstr "получатель уведомлений"

#: .\notifications\models.py:118
msgid "notification receivers"
msgstr "получатели уведомлений"

#: .\notifications\models.py:140
msgid "is read"
msgstr "прочитано"

#: .\notifications\models.py:141
msgid "read at"
msgstr "прочитано в"

#: .\notifications\models.py:147
msgid "ui notification"
msgstr "ui уведомление"

#: .\notifications\models.py:148
msgid "ui notifications"
msgstr "ui уведомления"

#: .\qa\models.py:16 .\qa\models.py:44
msgid "author"
msgstr "Автор"

#: .\qa\models.py:36
msgid "question"
msgstr "Вопрос"

#: .\users\admin.py:27
msgid "Company affiliation"
msgstr "Принадлежность к компании"

#: .\users\admin.py:28
msgid "Company affiliations"
msgstr "Принадлежности к компаниям"

#: .\users\admin.py:43 .\users\admin.py:58
msgid "Additional info"
msgstr "Дополнительная информация"

#: .\users\api\v1\serializers\group_serializers.py:43
msgid "The finish date must not be less than today"
msgstr "Дата окончания не должна быть меньше сегодняшнего дня"

#: .\users\api\v1\serializers\me_serializers.py:73
msgid "The old password is incorrect"
msgstr "Старый пароль неверный"

#: .\users\api\v1\serializers\me_serializers.py:78
msgid "The new password must match"
msgstr "Новый пароль должен совпадать"

#: .\users\api\v1\serializers\me_serializers.py:98
msgid "The photo is required"
msgstr "Фото обязательно"

#: .\users\api\v1\serializers\role_serializers.py:31
msgid "Required field"
msgstr "Обязательное поле"

#: .\users\api\v1\serializers\user_serializers.py:130
#: .\users\api\v1\serializers\user_serializers.py:133
#: .\users\api\v1\serializers\user_serializers.py:136
msgid "Company, department and position must be all set or all omitted."
msgstr ""

#: .\users\api\v1\serializers\user_serializers.py:230
#: .\users\api\v1\serializers\user_serializers.py:231
#: .\users\api\v1\serializers\user_serializers.py:232
#: .\venv\Lib\site-packages\django\forms\fields.py:95
msgid "This field is required."
msgstr ""

#: .\users\api\v1\serializers\user_serializers.py:243
msgid "This field is required when a selected role requires a department."
msgstr ""

#: .\users\api\v1\views\group_views.py:72
msgid "There are no users to delete"
msgstr "Нет пользователей для удаления"

#: .\users\api\v1\views\me_views.py:44
msgid "Password changed successfully"
msgstr "Пароль успешно изменен"

#: .\users\api\v1\views\me_views.py:53
msgid "User photo update"
msgstr "Обновление фото пользователя"

#: .\users\api\v1\views\me_views.py:66
msgid "User photo updated successfully"
msgstr "Фото пользователя успешно обновлено"

#: .\users\api\v1\views\permission_views.py:40
msgid "List of roles"
msgstr "Список ролей"

#: .\users\api\v1\views\user_views.py:66
msgid "User update"
msgstr "Обновление пользователя"

#: .\users\apps.py:8
msgid "Users"
msgstr "Пользователи"

#: .\users\models.py:19
msgid "unique identifier"
msgstr "Уникальный идентификатор"

#: .\users\models.py:20
msgid "iin"
msgstr "IIN"

#: .\users\models.py:21
msgid "email address"
msgstr "Адрес электронной почты"

#: .\users\models.py:22
msgid "phone"
msgstr "номер"

#: .\users\models.py:23
msgid "middle name"
msgstr ""

#: .\users\models.py:26 .\users\models.py:208
msgid "country"
msgstr "страна"

#: .\users\models.py:32
msgid "birth date"
msgstr "дата рождения"

#: .\users\models.py:33
msgid "about"
msgstr "О себе"

#: .\users\models.py:34
msgid "photo"
msgstr "фото"

#: .\users\models.py:37 .\users\models.py:472
msgid "roles"
msgstr "роли"

#: .\users\models.py:40 .\users\models.py:237
msgid "supervisors"
msgstr "супервизоры"

#: .\users\models.py:84
msgid "users"
msgstr "пользователи"

#: .\users\models.py:90
msgid "company_name"
msgstr "Название компании"

#: .\users\models.py:91
msgid "company_code"
msgstr "Код компании"

#: .\users\models.py:92
msgid "is_active"
msgstr "Активный"

#: .\users\models.py:98 .\users\models.py:105 .\users\models.py:309
msgid "company"
msgstr "Компания"

#: .\users\models.py:99
msgid "companies"
msgstr "Компании"

#: .\users\models.py:106 .\users\models.py:280 .\users\models.py:310
#: .\users\models.py:338 .\users\models.py:495
msgid "department"
msgstr "департамент"

#: .\users\models.py:107 .\users\models.py:300 .\users\models.py:311
msgid "position"
msgstr "позиция"

#: .\users\models.py:113
msgid "user_company"
msgstr "Принадлежность пользователя к компании"

#: .\users\models.py:114
msgid "user_companies"
msgstr "Принадлежности пользователей к компаниям"

#: .\users\models.py:125
msgid "block"
msgstr "заблокировать"

#: .\users\models.py:126
msgid "remove"
msgstr "удалить"

#: .\users\models.py:127
msgid "dismiss"
msgstr "уволить"

#: .\users\models.py:128
msgid "activate"
msgstr "активировать"

#: .\users\models.py:136
msgid "finish at"
msgstr "закончите в"

#: .\users\models.py:137
msgid "event"
msgstr "событие"

#: .\users\models.py:143
msgid "user blocking schedule"
msgstr "расписание блокировок пользователей"

#: .\users\models.py:144
msgid "user blocking schedules"
msgstr "расписание блокировок пользователей"

#: .\users\models.py:168
msgid "statuses"
msgstr "статусы"

#: .\users\models.py:184
msgid "start at"
msgstr "начните с"

#: .\users\models.py:185
msgid "end at"
msgstr "заканчивается на"

#: .\users\models.py:191
msgid "user status"
msgstr "статус пользователя"

#: .\users\models.py:192
msgid "user statuses"
msgstr "статусы пользователя"

#: .\users\models.py:209
msgid "countries"
msgstr "страны"

#: .\users\models.py:220
msgid "department_name"
msgstr "название департамента"

#: .\users\models.py:233
msgid "a unique code for importing users from a file"
msgstr "уникальный код для импорта пользователей из файла"

#: .\users\models.py:281
msgid "departments"
msgstr "департаменты"

#: .\users\models.py:292
msgid "position_name"
msgstr "позиция"

#: .\users\models.py:293
msgid "is boss"
msgstr "Руководитель"

#: .\users\models.py:301
msgid "positions"
msgstr "позиции"

#: .\users\models.py:326 .\users\models.py:360
msgid "lead"
msgstr "руководитель"

#: .\users\models.py:327 .\users\models.py:361
msgid "functional manager"
msgstr "функциональный руководитель"

#: .\users\models.py:331
msgid "supervisor"
msgstr "супервизоры пользователей"

#: .\users\models.py:344
msgid "supervisor of the Department"
msgstr "супервизор департамента"

#: .\users\models.py:345
msgid "supervisors of the Department"
msgstr "супервизоры департамента"

#: .\users\models.py:377
msgid "Type"
msgstr "Тип"

#: .\users\models.py:383
msgid "the user's supervisor"
msgstr "супервизор пользователя"

#: .\users\models.py:384
msgid "supervisors of users"
msgstr "супервизоры пользователей"

#: .\users\models.py:397 .\users\models.py:436
msgid "permission"
msgstr "разрешение"

#: .\users\models.py:407
msgid "content types"
msgstr "типы контента"

#: .\users\models.py:428
msgid "dependencies"
msgstr "зависимости"

#: .\users\models.py:437 .\users\models.py:450
msgid "permissions"
msgstr "разрешения"

#: .\users\models.py:446
msgid "is deletable"
msgstr "удаляется"

#: .\users\models.py:447
msgid "has department"
msgstr "департамент"

#: .\users\models.py:471 .\users\models.py:489
msgid "role"
msgstr "роль"

#: .\venv\Lib\site-packages\click\_termui_impl.py:600
#, python-brace-format
msgid "{editor}: Editing failed"
msgstr ""

#: .\venv\Lib\site-packages\click\_termui_impl.py:604
#, python-brace-format
msgid "{editor}: Editing failed: {e}"
msgstr ""

#: .\venv\Lib\site-packages\click\core.py:1084
#: .\venv\Lib\site-packages\click\core.py:1121
#, python-brace-format
msgid "{text} {deprecated_message}"
msgstr ""

#: .\venv\Lib\site-packages\click\core.py:1140
msgid "Options"
msgstr ""

#: .\venv\Lib\site-packages\click\core.py:1202
#, python-brace-format
msgid "Got unexpected extra argument ({args})"
msgid_plural "Got unexpected extra arguments ({args})"
msgstr[0] ""
msgstr[1] ""

#: .\venv\Lib\site-packages\click\core.py:1221
msgid "DeprecationWarning: The command {name!r} is deprecated.{extra_message}"
msgstr ""

#: .\venv\Lib\site-packages\click\core.py:1405
msgid "Aborted!"
msgstr "Прервана"

#: .\venv\Lib\site-packages\click\core.py:1779
msgid "Commands"
msgstr ""

#: .\venv\Lib\site-packages\click\core.py:1810
msgid "Missing command."
msgstr ""

#: .\venv\Lib\site-packages\click\core.py:1888
msgid "No such command {name!r}."
msgstr ""

#: .\venv\Lib\site-packages\click\core.py:2303
msgid "Value must be an iterable."
msgstr ""

#: .\venv\Lib\site-packages\click\core.py:2324
#, python-brace-format
msgid "Takes {nargs} values but 1 was given."
msgid_plural "Takes {nargs} values but {len} were given."
msgstr[0] ""
msgstr[1] ""

#: .\venv\Lib\site-packages\click\core.py:2404
msgid ""
"DeprecationWarning: The {param_type} {name!r} is deprecated.{extra_message}"
msgstr ""

#: .\venv\Lib\site-packages\click\core.py:2807
#, python-brace-format
msgid "env var: {var}"
msgstr ""

#: .\venv\Lib\site-packages\click\core.py:2810
#, python-brace-format
msgid "default: {default}"
msgstr ""

#: .\venv\Lib\site-packages\click\core.py:2870
msgid "(dynamic)"
msgstr ""

#: .\venv\Lib\site-packages\click\decorators.py:465
#, python-format
msgid "%(prog)s, version %(version)s"
msgstr ""

#: .\venv\Lib\site-packages\click\decorators.py:522
msgid "Show the version and exit."
msgstr ""

#: .\venv\Lib\site-packages\click\decorators.py:548
msgid "Show this message and exit."
msgstr ""

#: .\venv\Lib\site-packages\click\exceptions.py:50
#: .\venv\Lib\site-packages\click\exceptions.py:89
#, python-brace-format
msgid "Error: {message}"
msgstr ""

#: .\venv\Lib\site-packages\click\exceptions.py:81
#, python-brace-format
msgid "Try '{command} {option}' for help."
msgstr ""

#: .\venv\Lib\site-packages\click\exceptions.py:130
#, python-brace-format
msgid "Invalid value: {message}"
msgstr ""

#: .\venv\Lib\site-packages\click\exceptions.py:132
#, python-brace-format
msgid "Invalid value for {param_hint}: {message}"
msgstr ""

#: .\venv\Lib\site-packages\click\exceptions.py:190
msgid "Missing argument"
msgstr ""

#: .\venv\Lib\site-packages\click\exceptions.py:192
msgid "Missing option"
msgstr ""

#: .\venv\Lib\site-packages\click\exceptions.py:194
msgid "Missing parameter"
msgstr ""

#: .\venv\Lib\site-packages\click\exceptions.py:196
#, python-brace-format
msgid "Missing {param_type}"
msgstr ""

#: .\venv\Lib\site-packages\click\exceptions.py:203
#, python-brace-format
msgid "Missing parameter: {param_name}"
msgstr ""

#: .\venv\Lib\site-packages\click\exceptions.py:223
#, python-brace-format
msgid "No such option: {name}"
msgstr ""

#: .\venv\Lib\site-packages\click\exceptions.py:235
#, python-brace-format
msgid "Did you mean {possibility}?"
msgid_plural "(Possible options: {possibilities})"
msgstr[0] ""
msgstr[1] ""

#: .\venv\Lib\site-packages\click\exceptions.py:282
msgid "unknown error"
msgstr ""

#: .\venv\Lib\site-packages\click\exceptions.py:289
msgid "Could not open file {filename!r}: {message}"
msgstr ""

#: .\venv\Lib\site-packages\click\formatting.py:156
msgid "Usage:"
msgstr "Использование:"

#: .\venv\Lib\site-packages\click\parser.py:200
msgid "Argument {name!r} takes {nargs} values."
msgstr ""

#: .\venv\Lib\site-packages\click\parser.py:383
msgid "Option {name!r} does not take a value."
msgstr ""

#: .\venv\Lib\site-packages\click\parser.py:444
msgid "Option {name!r} requires an argument."
msgid_plural "Option {name!r} requires {nargs} arguments."
msgstr[0] ""
msgstr[1] ""

#: .\venv\Lib\site-packages\click\shell_completion.py:326
msgid "Shell completion is not supported for Bash versions older than 4.4."
msgstr ""

#: .\venv\Lib\site-packages\click\shell_completion.py:333
msgid "Couldn't detect Bash version, shell completion is not supported."
msgstr ""

#: .\venv\Lib\site-packages\click\termui.py:162
msgid "Repeat for confirmation"
msgstr ""

#: .\venv\Lib\site-packages\click\termui.py:178
msgid "Error: The value you entered was invalid."
msgstr ""

#: .\venv\Lib\site-packages\click\termui.py:180
#, python-brace-format
msgid "Error: {e.message}"
msgstr ""

#: .\venv\Lib\site-packages\click\termui.py:191
msgid "Error: The two entered values do not match."
msgstr ""

#: .\venv\Lib\site-packages\click\termui.py:247
msgid "Error: invalid input"
msgstr ""

#: .\venv\Lib\site-packages\click\termui.py:866
msgid "Press any key to continue..."
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:332
#, python-brace-format
msgid ""
"Choose from:\n"
"\t{choices}"
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:369
msgid "{value!r} is not {choice}."
msgid_plural "{value!r} is not one of {choices}."
msgstr[0] ""
msgstr[1] ""

#: .\venv\Lib\site-packages\click\types.py:460
msgid "{value!r} does not match the format {format}."
msgid_plural "{value!r} does not match the formats {formats}."
msgstr[0] ""
msgstr[1] ""

#: .\venv\Lib\site-packages\click\types.py:482
msgid "{value!r} is not a valid {number_type}."
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:538
#, python-brace-format
msgid "{value} is not in the range {range}."
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:679
msgid "{value!r} is not a valid boolean."
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:703
msgid "{value!r} is not a valid UUID."
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:895
msgid "directory"
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:897
msgid "path"
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:944
msgid "{name} {filename!r} does not exist."
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:953
msgid "{name} {filename!r} is a file."
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:961
msgid "{name} {filename!r} is a directory."
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:970
msgid "{name} {filename!r} is not readable."
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:979
msgid "{name} {filename!r} is not writable."
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:988
msgid "{name} {filename!r} is not executable."
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:1055
#, python-brace-format
msgid "{len_type} values are required, but {len_value} was given."
msgid_plural "{len_type} values are required, but {len_value} were given."
msgstr[0] ""
msgstr[1] ""

#: .\venv\Lib\site-packages\django\contrib\messages\apps.py:16
msgid "Messages"
msgstr ""

#: .\venv\Lib\site-packages\django\contrib\sitemaps\apps.py:8
msgid "Site Maps"
msgstr ""

#: .\venv\Lib\site-packages\django\contrib\staticfiles\apps.py:9
msgid "Static Files"
msgstr ""

#: .\venv\Lib\site-packages\django\contrib\syndication\apps.py:7
msgid "Syndication"
msgstr ""

#. Translators: String used to replace omitted page numbers in elided page
#. range generated by paginators, e.g. [1, 2, '…', 5, 6, 7, '…', 9, 10].
#: .\venv\Lib\site-packages\django\core\paginator.py:30
msgid "…"
msgstr ""

#: .\venv\Lib\site-packages\django\core\paginator.py:32
msgid "That page number is not an integer"
msgstr ""

#: .\venv\Lib\site-packages\django\core\paginator.py:33
msgid "That page number is less than 1"
msgstr ""

#: .\venv\Lib\site-packages\django\core\paginator.py:34
msgid "That page contains no results"
msgstr ""

#: .\venv\Lib\site-packages\django\core\validators.py:22
msgid "Enter a valid value."
msgstr ""

#: .\venv\Lib\site-packages\django\core\validators.py:70
msgid "Enter a valid domain name."
msgstr ""

#: .\venv\Lib\site-packages\django\core\validators.py:152
#: .\venv\Lib\site-packages\django\forms\fields.py:768
msgid "Enter a valid URL."
msgstr ""

#: .\venv\Lib\site-packages\django\core\validators.py:213
msgid "Enter a valid integer."
msgstr ""

#: .\venv\Lib\site-packages\django\core\validators.py:224
msgid "Enter a valid email address."
msgstr ""

#. Translators: "letters" means latin letters: a-z and A-Z.
#: .\venv\Lib\site-packages\django\core\validators.py:307
msgid ""
"Enter a valid “slug” consisting of letters, numbers, underscores or hyphens."
msgstr ""

#: .\venv\Lib\site-packages\django\core\validators.py:315
msgid ""
"Enter a valid “slug” consisting of Unicode letters, numbers, underscores, or "
"hyphens."
msgstr ""

#: .\venv\Lib\site-packages\django\core\validators.py:327
#: .\venv\Lib\site-packages\django\core\validators.py:336
#: .\venv\Lib\site-packages\django\core\validators.py:350
#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2215
#, python-format
msgid "Enter a valid %(protocol)s address."
msgstr ""

#: .\venv\Lib\site-packages\django\core\validators.py:329
msgid "IPv4"
msgstr ""

#: .\venv\Lib\site-packages\django\core\validators.py:338
#: .\venv\Lib\site-packages\django\utils\ipv6.py:30
msgid "IPv6"
msgstr ""

#: .\venv\Lib\site-packages\django\core\validators.py:352
msgid "IPv4 or IPv6"
msgstr ""

#: .\venv\Lib\site-packages\django\core\validators.py:393
msgid "Enter only digits separated by commas."
msgstr ""

#: .\venv\Lib\site-packages\django\core\validators.py:399
#, python-format
msgid "Ensure this value is %(limit_value)s (it is %(show_value)s)."
msgstr ""

#: .\venv\Lib\site-packages\django\core\validators.py:434
#, python-format
msgid "Ensure this value is less than or equal to %(limit_value)s."
msgstr ""

#: .\venv\Lib\site-packages\django\core\validators.py:443
#, python-format
msgid "Ensure this value is greater than or equal to %(limit_value)s."
msgstr ""

#: .\venv\Lib\site-packages\django\core\validators.py:452
#, python-format
msgid "Ensure this value is a multiple of step size %(limit_value)s."
msgstr ""

#: .\venv\Lib\site-packages\django\core\validators.py:459
#, python-format
msgid ""
"Ensure this value is a multiple of step size %(limit_value)s, starting from "
"%(offset)s, e.g. %(offset)s, %(valid_value1)s, %(valid_value2)s, and so on."
msgstr ""

#: .\venv\Lib\site-packages\django\core\validators.py:491
#, python-format
msgid ""
"Ensure this value has at least %(limit_value)d character (it has "
"%(show_value)d)."
msgid_plural ""
"Ensure this value has at least %(limit_value)d characters (it has "
"%(show_value)d)."
msgstr[0] ""
msgstr[1] ""

#: .\venv\Lib\site-packages\django\core\validators.py:509
#, python-format
msgid ""
"Ensure this value has at most %(limit_value)d character (it has "
"%(show_value)d)."
msgid_plural ""
"Ensure this value has at most %(limit_value)d characters (it has "
"%(show_value)d)."
msgstr[0] ""
msgstr[1] ""

#: .\venv\Lib\site-packages\django\core\validators.py:532
#: .\venv\Lib\site-packages\django\forms\fields.py:359
#: .\venv\Lib\site-packages\django\forms\fields.py:398
msgid "Enter a number."
msgstr ""

#: .\venv\Lib\site-packages\django\core\validators.py:534
#, python-format
msgid "Ensure that there are no more than %(max)s digit in total."
msgid_plural "Ensure that there are no more than %(max)s digits in total."
msgstr[0] ""
msgstr[1] ""

#: .\venv\Lib\site-packages\django\core\validators.py:539
#, python-format
msgid "Ensure that there are no more than %(max)s decimal place."
msgid_plural "Ensure that there are no more than %(max)s decimal places."
msgstr[0] ""
msgstr[1] ""

#: .\venv\Lib\site-packages\django\core\validators.py:544
#, python-format
msgid ""
"Ensure that there are no more than %(max)s digit before the decimal point."
msgid_plural ""
"Ensure that there are no more than %(max)s digits before the decimal point."
msgstr[0] ""
msgstr[1] ""

#: .\venv\Lib\site-packages\django\core\validators.py:615
#, python-format
msgid ""
"File extension “%(extension)s” is not allowed. Allowed extensions are: "
"%(allowed_extensions)s."
msgstr ""

#: .\venv\Lib\site-packages\django\core\validators.py:677
msgid "Null characters are not allowed."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\base.py:1571
#: .\venv\Lib\site-packages\django\forms\models.py:908
msgid "and"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\base.py:1573
#, python-format
msgid "%(model_name)s with this %(field_labels)s already exists."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\constraints.py:22
#, python-format
msgid "Constraint “%(name)s” is violated."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:134
#, python-format
msgid "Value %(value)r is not a valid choice."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:135
msgid "This field cannot be null."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:136
msgid "This field cannot be blank."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:137
#, python-format
msgid "%(model_name)s with this %(field_label)s already exists."
msgstr ""

#. Translators: The 'lookup_type' is one of 'date', 'year' or
#. 'month'. Eg: "Title must be unique for pub_date year"
#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:141
#, python-format
msgid ""
"%(field_label)s must be unique for %(date_field_label)s %(lookup_type)s."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:180
#, python-format
msgid "Field of type: %(field_type)s"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1156
#, python-format
msgid "“%(value)s” value must be either True or False."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1157
#, python-format
msgid "“%(value)s” value must be either True, False, or None."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1159
msgid "Boolean (Either True or False)"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1209
#, python-format
msgid "String (up to %(max_length)s)"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1211
msgid "String (unlimited)"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1320
msgid "Comma-separated integers"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1421
#, python-format
msgid ""
"“%(value)s” value has an invalid date format. It must be in YYYY-MM-DD "
"format."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1425
#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1560
#, python-format
msgid ""
"“%(value)s” value has the correct format (YYYY-MM-DD) but it is an invalid "
"date."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1429
msgid "Date (without time)"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1556
#, python-format
msgid ""
"“%(value)s” value has an invalid format. It must be in YYYY-MM-DD "
"HH:MM[:ss[.uuuuuu]][TZ] format."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1564
#, python-format
msgid ""
"“%(value)s” value has the correct format (YYYY-MM-DD HH:MM[:ss[.uuuuuu]]"
"[TZ]) but it is an invalid date/time."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1569
msgid "Date (with time)"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1696
#, python-format
msgid "“%(value)s” value must be a decimal number."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1698
msgid "Decimal number"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1859
#, python-format
msgid ""
"“%(value)s” value has an invalid format. It must be in [DD] "
"[[HH:]MM:]ss[.uuuuuu] format."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1863
msgid "Duration"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1915
msgid "Email address"
msgstr "Адрес электронной почты"

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1940
msgid "File path"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2018
#, python-format
msgid "“%(value)s” value must be a float."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2020
msgid "Floating point number"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2060
#, python-format
msgid "“%(value)s” value must be an integer."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2062
msgid "Integer"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2158
msgid "Big (8 byte) integer"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2175
msgid "Small integer"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2183
msgid "IPv4 address"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2214
msgid "IP address"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2305
#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2306
#, python-format
msgid "“%(value)s” value must be either None, True or False."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2308
msgid "Boolean (Either True, False or None)"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2359
msgid "Positive big integer"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2374
msgid "Positive integer"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2389
msgid "Positive small integer"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2405
#, python-format
msgid "Slug (up to %(max_length)s)"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2441
msgid "Text"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2521
#, python-format
msgid ""
"“%(value)s” value has an invalid format. It must be in HH:MM[:ss[.uuuuuu]] "
"format."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2525
#, python-format
msgid ""
"“%(value)s” value has the correct format (HH:MM[:ss[.uuuuuu]]) but it is an "
"invalid time."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2529
msgid "Time"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2637
msgid "URL"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2661
msgid "Raw binary data"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2726
#, python-format
msgid "“%(value)s” is not a valid UUID."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2728
msgid "Universally unique identifier"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\files.py:244
msgid "File"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\files.py:420
msgid "Image"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\json.py:24
msgid "A JSON object"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\json.py:26
msgid "Value must be valid JSON."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\related.py:932
#, python-format
msgid "%(model)s instance with %(field)s %(value)r does not exist."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\related.py:934
msgid "Foreign Key (type determined by related field)"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\related.py:1228
msgid "One-to-one relationship"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\related.py:1285
#, python-format
msgid "%(from)s-%(to)s relationship"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\related.py:1287
#, python-format
msgid "%(from)s-%(to)s relationships"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\related.py:1335
msgid "Many-to-many relationship"
msgstr ""

#. Translators: If found as last label character, these punctuation
#. characters will prevent the default label_suffix to be appended to the label
#: .\venv\Lib\site-packages\django\forms\boundfield.py:185
msgid ":?.!"
msgstr ""

#: .\venv\Lib\site-packages\django\forms\fields.py:308
msgid "Enter a whole number."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\fields.py:479
#: .\venv\Lib\site-packages\django\forms\fields.py:1260
msgid "Enter a valid date."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\fields.py:502
#: .\venv\Lib\site-packages\django\forms\fields.py:1261
msgid "Enter a valid time."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\fields.py:529
msgid "Enter a valid date/time."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\fields.py:563
msgid "Enter a valid duration."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\fields.py:564
#, python-brace-format
msgid "The number of days must be between {min_days} and {max_days}."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\fields.py:633
msgid "No file was submitted. Check the encoding type on the form."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\fields.py:634
msgid "No file was submitted."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\fields.py:635
msgid "The submitted file is empty."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\fields.py:637
#, python-format
msgid "Ensure this filename has at most %(max)d character (it has %(length)d)."
msgid_plural ""
"Ensure this filename has at most %(max)d characters (it has %(length)d)."
msgstr[0] ""
msgstr[1] ""

#: .\venv\Lib\site-packages\django\forms\fields.py:642
msgid "Please either submit a file or check the clear checkbox, not both."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\fields.py:710
msgid ""
"Upload a valid image. The file you uploaded was either not an image or a "
"corrupted image."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\fields.py:882
#: .\venv\Lib\site-packages\django\forms\fields.py:968
#: .\venv\Lib\site-packages\django\forms\models.py:1592
#, python-format
msgid "Select a valid choice. %(value)s is not one of the available choices."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\fields.py:970
#: .\venv\Lib\site-packages\django\forms\fields.py:1089
#: .\venv\Lib\site-packages\django\forms\models.py:1590
msgid "Enter a list of values."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\fields.py:1090
msgid "Enter a complete value."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\fields.py:1329
msgid "Enter a valid UUID."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\fields.py:1359
msgid "Enter a valid JSON."
msgstr ""

#. Translators: This is the default suffix added to form field labels
#: .\venv\Lib\site-packages\django\forms\forms.py:94
msgid ":"
msgstr ""

#: .\venv\Lib\site-packages\django\forms\forms.py:230
#, python-format
msgid "(Hidden field %(name)s) %(error)s"
msgstr ""

#: .\venv\Lib\site-packages\django\forms\formsets.py:61
#, python-format
msgid ""
"ManagementForm data is missing or has been tampered with. Missing fields: "
"%(field_names)s. You may need to file a bug report if the issue persists."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\formsets.py:65
#, python-format
msgid "Please submit at most %(num)d form."
msgid_plural "Please submit at most %(num)d forms."
msgstr[0] ""
msgstr[1] ""

#: .\venv\Lib\site-packages\django\forms\formsets.py:70
#, python-format
msgid "Please submit at least %(num)d form."
msgid_plural "Please submit at least %(num)d forms."
msgstr[0] ""
msgstr[1] ""

#: .\venv\Lib\site-packages\django\forms\formsets.py:484
#: .\venv\Lib\site-packages\django\forms\formsets.py:491
msgid "Order"
msgstr ""

#: .\venv\Lib\site-packages\django\forms\formsets.py:499
msgid "Delete"
msgstr ""

#: .\venv\Lib\site-packages\django\forms\models.py:901
#, python-format
msgid "Please correct the duplicate data for %(field)s."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\models.py:906
#, python-format
msgid "Please correct the duplicate data for %(field)s, which must be unique."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\models.py:913
#, python-format
msgid ""
"Please correct the duplicate data for %(field_name)s which must be unique "
"for the %(lookup)s in %(date_field)s."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\models.py:922
msgid "Please correct the duplicate values below."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\models.py:1359
msgid "The inline value did not match the parent instance."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\models.py:1450
msgid "Select a valid choice. That choice is not one of the available choices."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\models.py:1594
#, python-format
msgid "“%(pk)s” is not a valid value."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\utils.py:227
#, python-format
msgid ""
"%(datetime)s couldn’t be interpreted in time zone %(current_timezone)s; it "
"may be ambiguous or it may not exist."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\widgets.py:461
msgid "Clear"
msgstr ""

#: .\venv\Lib\site-packages\django\forms\widgets.py:462
msgid "Currently"
msgstr ""

#: .\venv\Lib\site-packages\django\forms\widgets.py:463
msgid "Change"
msgstr ""

#: .\venv\Lib\site-packages\django\forms\widgets.py:800
msgid "Unknown"
msgstr ""

#: .\venv\Lib\site-packages\django\forms\widgets.py:801
msgid "Yes"
msgstr ""

#: .\venv\Lib\site-packages\django\forms\widgets.py:802
msgid "No"
msgstr ""

#. Translators: Please do not add spaces around commas.
#: .\venv\Lib\site-packages\django\template\defaultfilters.py:873
msgid "yes,no,maybe"
msgstr ""

#: .\venv\Lib\site-packages\django\template\defaultfilters.py:903
#: .\venv\Lib\site-packages\django\template\defaultfilters.py:920
#, python-format
msgid "%(size)d byte"
msgid_plural "%(size)d bytes"
msgstr[0] ""
msgstr[1] ""

#: .\venv\Lib\site-packages\django\template\defaultfilters.py:922
#, python-format
msgid "%s KB"
msgstr ""

#: .\venv\Lib\site-packages\django\template\defaultfilters.py:924
#, python-format
msgid "%s MB"
msgstr ""

#: .\venv\Lib\site-packages\django\template\defaultfilters.py:926
#, python-format
msgid "%s GB"
msgstr ""

#: .\venv\Lib\site-packages\django\template\defaultfilters.py:928
#, python-format
msgid "%s TB"
msgstr ""

#: .\venv\Lib\site-packages\django\template\defaultfilters.py:930
#, python-format
msgid "%s PB"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dateformat.py:74
msgid "p.m."
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dateformat.py:75
msgid "a.m."
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dateformat.py:80
msgid "PM"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dateformat.py:81
msgid "AM"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dateformat.py:153
msgid "midnight"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dateformat.py:155
msgid "noon"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:7
msgid "Monday"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:8
msgid "Tuesday"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:9
msgid "Wednesday"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:10
msgid "Thursday"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:11
msgid "Friday"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:12
msgid "Saturday"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:13
msgid "Sunday"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:16
msgid "Mon"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:17
msgid "Tue"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:18
msgid "Wed"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:19
msgid "Thu"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:20
msgid "Fri"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:21
msgid "Sat"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:22
msgid "Sun"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:25
msgid "January"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:26
msgid "February"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:27
msgid "March"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:28
msgid "April"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:29
msgid "May"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:30
msgid "June"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:31
msgid "July"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:32
msgid "August"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:33
msgid "September"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:34
msgid "October"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:35
msgid "November"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:36
msgid "December"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:39
msgid "jan"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:40
msgid "feb"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:41
msgid "mar"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:42
msgid "apr"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:43
msgid "may"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:44
msgid "jun"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:45
msgid "jul"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:46
msgid "aug"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:47
msgid "sep"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:48
msgid "oct"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:49
msgid "nov"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:50
msgid "dec"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:53
msgctxt "abbrev. month"
msgid "Jan."
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:54
msgctxt "abbrev. month"
msgid "Feb."
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:55
msgctxt "abbrev. month"
msgid "March"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:56
msgctxt "abbrev. month"
msgid "April"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:57
msgctxt "abbrev. month"
msgid "May"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:58
msgctxt "abbrev. month"
msgid "June"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:59
msgctxt "abbrev. month"
msgid "July"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:60
msgctxt "abbrev. month"
msgid "Aug."
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:61
msgctxt "abbrev. month"
msgid "Sept."
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:62
msgctxt "abbrev. month"
msgid "Oct."
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:63
msgctxt "abbrev. month"
msgid "Nov."
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:64
msgctxt "abbrev. month"
msgid "Dec."
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:67
msgctxt "alt. month"
msgid "January"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:68
msgctxt "alt. month"
msgid "February"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:69
msgctxt "alt. month"
msgid "March"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:70
msgctxt "alt. month"
msgid "April"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:71
msgctxt "alt. month"
msgid "May"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:72
msgctxt "alt. month"
msgid "June"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:73
msgctxt "alt. month"
msgid "July"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:74
msgctxt "alt. month"
msgid "August"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:75
msgctxt "alt. month"
msgid "September"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:76
msgctxt "alt. month"
msgid "October"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:77
msgctxt "alt. month"
msgid "November"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:78
msgctxt "alt. month"
msgid "December"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\ipv6.py:8
msgid "This is not a valid IPv6 address."
msgstr ""

#: .\venv\Lib\site-packages\django\utils\text.py:75
#, python-format
msgctxt "String to return when truncating text"
msgid "%(truncated_text)s…"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\text.py:286
msgid "or"
msgstr ""

#. Translators: This string is used as a separator between list elements
#: .\venv\Lib\site-packages\django\utils\text.py:305
#: .\venv\Lib\site-packages\django\utils\timesince.py:135
msgid ", "
msgstr ""

#: .\venv\Lib\site-packages\django\utils\timesince.py:8
#, python-format
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] ""
msgstr[1] ""

#: .\venv\Lib\site-packages\django\utils\timesince.py:9
#, python-format
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] ""
msgstr[1] ""

#: .\venv\Lib\site-packages\django\utils\timesince.py:10
#, python-format
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] ""
msgstr[1] ""

#: .\venv\Lib\site-packages\django\utils\timesince.py:11
#, python-format
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] ""
msgstr[1] ""

#: .\venv\Lib\site-packages\django\utils\timesince.py:12
#, python-format
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] ""
msgstr[1] ""

#: .\venv\Lib\site-packages\django\utils\timesince.py:13
#, python-format
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] ""
msgstr[1] ""

#: .\venv\Lib\site-packages\django\views\csrf.py:29
msgid "Forbidden"
msgstr ""

#: .\venv\Lib\site-packages\django\views\csrf.py:30
msgid "CSRF verification failed. Request aborted."
msgstr ""

#: .\venv\Lib\site-packages\django\views\csrf.py:34
msgid ""
"You are seeing this message because this HTTPS site requires a “Referer "
"header” to be sent by your web browser, but none was sent. This header is "
"required for security reasons, to ensure that your browser is not being "
"hijacked by third parties."
msgstr ""

#: .\venv\Lib\site-packages\django\views\csrf.py:40
msgid ""
"If you have configured your browser to disable “Referer” headers, please re-"
"enable them, at least for this site, or for HTTPS connections, or for “same-"
"origin” requests."
msgstr ""

#: .\venv\Lib\site-packages\django\views\csrf.py:45
msgid ""
"If you are using the <meta name=\"referrer\" content=\"no-referrer\"> tag or "
"including the “Referrer-Policy: no-referrer” header, please remove them. The "
"CSRF protection requires the “Referer” header to do strict referer checking. "
"If you’re concerned about privacy, use alternatives like <a "
"rel=\"noreferrer\" …> for links to third-party sites."
msgstr ""

#: .\venv\Lib\site-packages\django\views\csrf.py:54
msgid ""
"You are seeing this message because this site requires a CSRF cookie when "
"submitting forms. This cookie is required for security reasons, to ensure "
"that your browser is not being hijacked by third parties."
msgstr ""

#: .\venv\Lib\site-packages\django\views\csrf.py:60
msgid ""
"If you have configured your browser to disable cookies, please re-enable "
"them, at least for this site, or for “same-origin” requests."
msgstr ""

#: .\venv\Lib\site-packages\django\views\csrf.py:66
msgid "More information is available with DEBUG=True."
msgstr ""

#: .\venv\Lib\site-packages\django\views\generic\dates.py:44
msgid "No year specified"
msgstr ""

#: .\venv\Lib\site-packages\django\views\generic\dates.py:64
#: .\venv\Lib\site-packages\django\views\generic\dates.py:115
#: .\venv\Lib\site-packages\django\views\generic\dates.py:214
msgid "Date out of range"
msgstr ""

#: .\venv\Lib\site-packages\django\views\generic\dates.py:94
msgid "No month specified"
msgstr ""

#: .\venv\Lib\site-packages\django\views\generic\dates.py:147
msgid "No day specified"
msgstr ""

#: .\venv\Lib\site-packages\django\views\generic\dates.py:194
msgid "No week specified"
msgstr ""

#: .\venv\Lib\site-packages\django\views\generic\dates.py:349
#: .\venv\Lib\site-packages\django\views\generic\dates.py:380
#, python-format
msgid "No %(verbose_name_plural)s available"
msgstr ""

#: .\venv\Lib\site-packages\django\views\generic\dates.py:652
#, python-format
msgid ""
"Future %(verbose_name_plural)s not available because "
"%(class_name)s.allow_future is False."
msgstr ""

#: .\venv\Lib\site-packages\django\views\generic\dates.py:692
#, python-format
msgid "Invalid date string “%(datestr)s” given format “%(format)s”"
msgstr ""

#: .\venv\Lib\site-packages\django\views\generic\detail.py:56
#, python-format
msgid "No %(verbose_name)s found matching the query"
msgstr ""

#: .\venv\Lib\site-packages\django\views\generic\list.py:70
msgid "Page is not “last”, nor can it be converted to an int."
msgstr ""

#: .\venv\Lib\site-packages\django\views\generic\list.py:77
#, python-format
msgid "Invalid page (%(page_number)s): %(message)s"
msgstr ""

#: .\venv\Lib\site-packages\django\views\generic\list.py:169
#, python-format
msgid "Empty list and “%(class_name)s.allow_empty” is False."
msgstr ""

#: .\venv\Lib\site-packages\django\views\static.py:49
msgid "Directory indexes are not allowed here."
msgstr ""

#: .\venv\Lib\site-packages\django\views\static.py:51
#, python-format
msgid "“%(path)s” does not exist"
msgstr ""

#: .\venv\Lib\site-packages\django\views\static.py:68
#: .\venv\Lib\site-packages\django\views\templates\directory_index.html:8
#: .\venv\Lib\site-packages\django\views\templates\directory_index.html:11
#, python-format
msgid "Index of %(directory)s"
msgstr ""

#: .\venv\Lib\site-packages\django\views\templates\default_urlconf.html:7
#: .\venv\Lib\site-packages\django\views\templates\default_urlconf.html:204
msgid "The install worked successfully! Congratulations!"
msgstr ""

#: .\venv\Lib\site-packages\django\views\templates\default_urlconf.html:206
#, python-format
msgid ""
"View <a href=\"https://docs.djangoproject.com/en/%(version)s/releases/\" "
"target=\"_blank\" rel=\"noopener\">release notes</a> for Django %(version)s"
msgstr ""

#: .\venv\Lib\site-packages\django\views\templates\default_urlconf.html:208
#, python-format
msgid ""
"You are seeing this page because <a href=\"https://docs.djangoproject.com/en/"
"%(version)s/ref/settings/#debug\" target=\"_blank\" "
"rel=\"noopener\">DEBUG=True</a> is in your settings file and you have not "
"configured any URLs."
msgstr ""

#: .\venv\Lib\site-packages\django\views\templates\default_urlconf.html:217
msgid "Django Documentation"
msgstr "Документация по Django"

#: .\venv\Lib\site-packages\django\views\templates\default_urlconf.html:218
msgid "Topics, references, &amp; how-to’s"
msgstr ""

#: .\venv\Lib\site-packages\django\views\templates\default_urlconf.html:226
msgid "Tutorial: A Polling App"
msgstr ""

#: .\venv\Lib\site-packages\django\views\templates\default_urlconf.html:227
msgid "Get started with Django"
msgstr ""

#: .\venv\Lib\site-packages\django\views\templates\default_urlconf.html:235
msgid "Django Community"
msgstr ""

#: .\venv\Lib\site-packages\django\views\templates\default_urlconf.html:236
msgid "Connect, get help, or contribute"
msgstr ""

#: .\venv\Lib\site-packages\django_ckeditor_5\permissions.py:18
msgid "You do not have permission to upload files."
msgstr ""

#: .\venv\Lib\site-packages\django_ckeditor_5\permissions.py:25
msgid "You must be logged in to upload files."
msgstr ""

#: .\venv\Lib\site-packages\django_ckeditor_5\validators.py:17
#, python-format
msgid "File should be at most %(max_size)s MB."
msgstr ""

#: .\venv\Lib\site-packages\django_ckeditor_5\views.py:41
msgid "Invalid form data"
msgstr "недопустимый формат"

#: .\venv\Lib\site-packages\django_ckeditor_5\widgets.py:43
msgid "Check the correct settings.CKEDITOR_5_CONFIGS "
msgstr ""

#: .\venv\Lib\site-packages\kombu\transport\qpid.py:1311
#, python-format
msgid "Attempting to connect to qpid with SASL mechanism %s"
msgstr ""

#: .\venv\Lib\site-packages\kombu\transport\qpid.py:1316
#, python-format
msgid "Connected to qpid with SASL mechanism %s"
msgstr ""

#: .\venv\Lib\site-packages\kombu\transport\qpid.py:1334
#, python-format
msgid "Unable to connect to qpid with SASL mechanism %s"
msgstr ""

#~ msgid "The owner cannot be added as a participant"
#~ msgstr "Владельца нельзя добавить как участника"

#~| msgid "settings"
#~ msgid "due date settings"
#~ msgstr "настройки"

#~| msgid "Not Started"
#~ msgid "not_started"
#~ msgstr "Не начато"

#~| msgid "Finished Manually"
#~ msgid "finished_manually"
#~ msgstr "Завершен(вручную)"

#~| msgid "finish"
#~ msgid "finished"
#~ msgstr "Завершен"

#~ msgid "date"
#~ msgstr "дата"

#~ msgid "default"
#~ msgstr "по умолчанию"

#~| msgid "User update"
#~ msgid "due date"
#~ msgstr "Обновление пользователя"

#~ msgid "due period"
#~ msgstr "срок"

#~| msgid "completion"
#~ msgid "completed_at"
#~ msgstr "завершение"

#~| msgid "Content"
#~ msgid "content"
#~ msgstr "Контент"

#~ msgid "active interval"
#~ msgstr "активный интервал"

#~ msgid "option"
#~ msgstr "завершение"

#~ msgid "The file is required for download"
#~ msgstr "Файл обязателен для загрузки"

#~ msgid "Error defining the file"
#~ msgstr "Ошибка при определении файла"

#~ msgid "upload the file (multipart/form-data)"
#~ msgstr "загрузите файл (multipart/form-data)"

#~ msgid "original file name"
#~ msgstr "оригинальное имя файла"

#~ msgid "file mime type"
#~ msgstr "файловый mime-тип"

#~ msgid "is deleted"
#~ msgstr "удаляется"
