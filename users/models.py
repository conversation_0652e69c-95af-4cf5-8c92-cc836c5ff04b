import uuid

from django.contrib.auth.base_user import AbstractBaseUser
from django.contrib.auth.models import AbstractUser, Group
from django.core.cache import cache
from django.db import models
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.utils.translation import gettext_lazy as _

from core.models import BaseUUIDModel
from lms.decorators import cleanup_files
from lms.s3_storage import PublicMediaStorage
from lms.utils import validate_file_extension


@cleanup_files(['photo'])
class User(AbstractUser):
    id = models.UUIDField(_('unique identifier'), primary_key=True, editable=False, default=uuid.uuid4)
    iin = models.CharField(_('iin'), max_length=12, null=True, blank=True)
    email = models.EmailField(_('email address'), null=True, blank=True)
    phone = models.CharField(verbose_name=_('phone'), max_length=20, null=True, blank=True)
    middle_name = models.CharField(_("middle name"), max_length=150, null=True, blank=True)
    country = models.ForeignKey(
        to='Country',
        verbose_name=_('country'),
        on_delete=models.RESTRICT,
        null=True,
        blank=True,
        related_name='users',
    )
    birth_date = models.DateField(verbose_name=_('birth date'), null=True, blank=True)
    about = models.TextField(verbose_name=_('about'), null=True, blank=True)
    photo = models.ImageField(verbose_name=_('photo'), upload_to='users/avatars/%Y/%m/%d/',
                              storage=PublicMediaStorage(),
                              null=True, blank=True)
    roles = models.ManyToManyField(to='Role', verbose_name=_('roles'), blank=True, related_name='users')
    supervisors = models.ManyToManyField(
        to='User',
        verbose_name=_('supervisors'),
        through='UserSupervisor',
        related_name='users',
    )

    @property
    def full_name(self):
        return f"{self.first_name or ''} {self.last_name or ''} {self.middle_name or ''}"

    @property
    def groups_count(self):
        cache_key = f'user_{self.id}_groups_count'
        groups_count = cache.get(cache_key)
        if groups_count is None:
            groups_count = self.groups.count()
            cache.set(cache_key, groups_count, timeout=60 * 15)
        return groups_count

    def get_status_label(self):
        return {
            (True, True): 'active',
            (False, False): 'dismissed',
            (False, True): 'inactive',
        }.get((self.is_active, self.is_staff))

    def deactivate(self):
        self.is_active = False
        self.save(update_fields=['is_active'])

    def dismiss(self):
        self.is_active = False
        self.is_staff = False
        self.save(update_fields=['is_active', 'is_staff'])

    def activate(self):
        self.is_active = True
        self.is_staff = True
        self.save(update_fields=['is_active', 'is_staff'])

    def __str__(self):
        return self.username

    class Meta:
        verbose_name = _('user')
        verbose_name_plural = _('users')
        ordering = ['id', ]
        db_table = 'users_user'


class Company(BaseUUIDModel):
    name = models.CharField(verbose_name=_('company_name'), max_length=255)
    code = models.CharField(verbose_name=_('company_code'), max_length=255, unique=True)
    is_active = models.BooleanField(verbose_name=_('is_active'), default=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = _('company')
        verbose_name_plural = _('companies')
        db_table = 'users_companies'


class UserCompany(BaseUUIDModel):
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name=_('user'), related_name='companies')
    company = models.ForeignKey(Company, on_delete=models.RESTRICT, verbose_name=_('company'))
    department = models.ForeignKey(to='Department', on_delete=models.RESTRICT, verbose_name=_('department'))
    position = models.ForeignKey(to='Position', on_delete=models.RESTRICT, verbose_name=_('position'))

    def __str__(self):
        return '%s - %s' % (self.user.iin, self.company.name)

    class Meta:
        verbose_name = _('user_company')
        verbose_name_plural = _('user_companies')
        unique_together = ('user', 'company', 'department', 'position')
        db_table = 'users_user_companies'


class UserSchedule(BaseUUIDModel):
    ACTIVATE = 'ACTIVATE'
    DEACTIVATE = 'DEACTIVATE'
    REMOVE = 'REMOVE'
    DISMISS = 'DISMISS'
    EVENT_CHOICES = (
        (DEACTIVATE, _('block')),
        (REMOVE, _('remove')),
        (DISMISS, _('dismiss')),
        (ACTIVATE, _('activate')),
    )
    user = models.ForeignKey(
        User,
        verbose_name=_('user'),
        on_delete=models.CASCADE,
        related_name='deactivate_schedule',
    )
    finish_at = models.DateField(verbose_name=_('finish at'))
    event = models.CharField(max_length=20, choices=EVENT_CHOICES, verbose_name=_('event'))

    def __str__(self):
        return f'Запланированная задача {self.user.username}  на {self.finish_at}'

    class Meta:
        verbose_name = _('user blocking schedule')
        verbose_name_plural = _('user blocking schedules')
        ordering = ['id', ]
        db_table = 'users_user_schedule'


@cleanup_files(['file'])
class Status(BaseUUIDModel):
    """
    Статусы отсутствия
    """
    name = models.CharField(verbose_name=_('name'), max_length=50)
    file = models.FileField(
        upload_to='users/statuses/%Y/%m/%d/',
        verbose_name=_('file'),
        null=True,
        blank=True,
        validators=[validate_file_extension],
    )

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = _('status')
        verbose_name_plural = _('statuses')
        ordering = ['id', ]
        db_table = 'users_status'


class UserStatus(BaseUUIDModel):
    """
    Статусы пользователей
    """
    user = models.ForeignKey(
        to=User,
        verbose_name=_('user'),
        on_delete=models.CASCADE,
        related_name='statuses',
    )
    status = models.ForeignKey(Status, verbose_name=_('status'), on_delete=models.CASCADE)
    start_at = models.DateField(verbose_name=_('start at'))
    end_at = models.DateField(verbose_name=_('end at'), null=True, blank=True)

    def __str__(self):
        return '%s - %s - %s - %s' % (self.user.id, self.status.name, self.start_at, self.end_at)

    class Meta:
        verbose_name = _('user status')
        verbose_name_plural = _('user statuses')
        ordering = ['id', ]
        db_table = 'users_user_statuses'


class Country(BaseUUIDModel):
    """
    Страны
    """
    name = models.CharField(verbose_name=_('name'), max_length=50, unique=True)

    def __str__(self):
        return self.name

    class Meta:
        ordering = ['name']
        verbose_name = _('country')
        verbose_name_plural = _('countries')
        db_table = 'users_country'


class Department(BaseUUIDModel):
    """
    Департаменты
    """
    ID1C = models.UUIDField(editable=False, null=True, blank=True)
    IDParent1C = models.UUIDField(editable=False, null=True, blank=True)
    company = models.ForeignKey(Company, on_delete=models.RESTRICT)
    name = models.CharField(verbose_name=_('department_name'), max_length=255)
    parent = models.ForeignKey(
        to='self',
        verbose_name=_('parent'),
        null=True,
        blank=True,
        related_name='children',
        on_delete=models.CASCADE,
    )
    code = models.CharField(
        max_length=255,
        verbose_name=_('code'),
        unique=True,
        help_text=_('a unique code for importing users from a file'),
    )
    supervisors = models.ManyToManyField(
        to=User,
        verbose_name=_('supervisors'),
        through='DepartmentSupervisor',
        related_name='departments',
        blank=True,
    )

    def get_descendant_ids_cte(self):
        """
        Возвращает список ID всех потомков (включая самого себя)
        текущего объекта модели с использованием рекурсивного SQL-запроса (CTE).
        """
        from django.db import connection

        with connection.cursor() as cursor:
            cursor.execute(f"""
                WITH RECURSIVE department_tree AS (
                    SELECT id FROM {self._meta.db_table} WHERE id = %s
                    UNION ALL
                    SELECT d.id
                    FROM {self._meta.db_table} d
                    INNER JOIN department_tree dt ON d.parent_id = dt.id
                )
                SELECT id FROM department_tree;
            """, [self.id])
            rows = cursor.fetchall()
        return [row[0] for row in rows]

    @property
    def users_count(self):
        """
        Кэшируем на 15 минут
        """
        cache_key = f'department_{self.id}_users_count'
        users_count = cache.get(cache_key)
        if users_count is None:
            users_count = self.user_set.count()
            cache.set(cache_key, users_count, timeout=60 * 15)
        return users_count

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = _('department')
        verbose_name_plural = _('departments')
        ordering = ['id', ]
        db_table = 'users_department'


class Position(BaseUUIDModel):
    """
    Позиций
    """
    company = models.ForeignKey(Company, on_delete=models.RESTRICT)
    ID1C = models.UUIDField(editable=False, null=True, blank=True)
    name = models.CharField(verbose_name=_('position_name'), max_length=255)
    is_boss = models.BooleanField(verbose_name=_('is boss'), default=False)

    def __str__(self):
        return self.name

    class Meta:
        ordering = ['name']
        verbose_name = _('position')
        verbose_name_plural = _('positions')
        db_table = 'users_positions'
        constraints = [
            models.UniqueConstraint(fields=['ID1C', 'company'], name='uniq_position_company')
        ]


class DepartmentPosition(BaseUUIDModel):
    company = models.ForeignKey(to=Company, verbose_name=_('company'), on_delete=models.RESTRICT)
    department = models.ForeignKey(to=Department, verbose_name=_('department'), on_delete=models.RESTRICT)
    position = models.ForeignKey(to=Position, verbose_name=_('position'), on_delete=models.RESTRICT)

    def __str__(self):
        return f"{self.department.name} - {self.position.name}"

    class Meta:
        unique_together = ('department', 'position', 'company')
        db_table = 'users_department_position'


class DepartmentSupervisor(BaseUUIDModel):
    TYPE_LEAD = 'LEAD'
    TYPE_FUNCTIONAL_LEAD = 'FUNCTIONAL_LEAD'

    SUPERVISOR_CHOICES = (
        (TYPE_LEAD, _('lead')),
        (TYPE_FUNCTIONAL_LEAD, _('functional manager')),
    )
    supervisor = models.ForeignKey(
        to=User,
        verbose_name=_('supervisor'),
        on_delete=models.CASCADE,
        related_name='supervisor_in_department',
    )
    type = models.CharField(max_length=20, verbose_name=_('type'), choices=SUPERVISOR_CHOICES)
    department = models.ForeignKey(
        to=Department,
        verbose_name=_('department'),
        on_delete=models.CASCADE,
        related_name='supervisors_in_department',
    )

    class Meta:
        verbose_name = _('supervisor of the Department')
        verbose_name_plural = _('supervisors of the Department')
        unique_together = (('supervisor', 'department', 'type'),)
        db_table = 'users_department_supervisors'


class UserSupervisor(BaseUUIDModel):
    """
    Кураторы пользователя
    LEAD - Руководитель
    FUNCTIONAL_LEAD - Наследуется от LEAD
    """
    TYPE_LEAD = 'LEAD'
    TYPE_FUNCTIONAL_LEAD = 'FUNCTIONAL_LEAD'

    SUPERVISOR_CHOICES = (
        (TYPE_LEAD, _('lead')),
        (TYPE_FUNCTIONAL_LEAD, _('functional manager')),
    )

    user = models.ForeignKey(
        to=User,
        verbose_name=_('user'),
        on_delete=models.CASCADE,
        related_name='supervisors_in_user',
    )
    supervisor = models.ForeignKey(
        to=User,
        verbose_name='supervisor',
        on_delete=models.SET_NULL,
        related_name='supervisors_in_supervisor',
        null=True,
    )
    type = models.CharField(verbose_name=_('Type'), max_length=20, choices=SUPERVISOR_CHOICES)

    def __str__(self):
        return "%s" % self.id

    class Meta:
        verbose_name = _("the user's supervisor")
        verbose_name_plural = _('supervisors of users')
        unique_together = (('user', 'supervisor', 'type'),)
        db_table = 'users_user_supervisors'


class ContentType(BaseUUIDModel):
    """
    Контенты
    """
    name = models.CharField(verbose_name=_('name'), max_length=50)
    code = models.CharField(verbose_name=_('code'), max_length=50, unique=True)
    permissions = models.ManyToManyField(
        to='Permission',
        verbose_name=_('permission'),
        blank=True,
        related_name='content_types',
    )

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = _('content type')
        verbose_name_plural = _('content types')
        ordering = ['id', ]
        db_table = 'users_content_type'


class Permission(BaseUUIDModel):
    name = models.CharField(verbose_name=_('name'), max_length=255)
    code = models.CharField(verbose_name=_('code'), max_length=255, unique=True)
    parent = models.ForeignKey(
        to='self',
        verbose_name=_('parent'),
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        related_name='children',
    )
    description = models.TextField(verbose_name=_('description'), null=True, blank=True)
    dependencies = models.ManyToManyField(
        to='self',
        symmetrical=False,
        blank=True,
        verbose_name=_('dependencies'),
        related_name='dependencies_in_permission',
    )

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = _('permission')
        verbose_name_plural = _('permissions')
        ordering = ['id', ]
        db_table = 'users_permission'


class Role(BaseUUIDModel):
    name = models.CharField(verbose_name=_('name'), max_length=255)
    code = models.CharField(verbose_name=_('code'), max_length=255, unique=True)
    description = models.TextField(verbose_name=_('description'))
    is_deletable = models.BooleanField(verbose_name=_('is deletable'), default=True)
    has_department = models.BooleanField(verbose_name=_('has department'), default=True)
    permissions = models.ManyToManyField(
        to=Permission,
        verbose_name=_('permissions'),
        blank=True,
        related_name='roles',
    )

    @property
    def users_count(self):
        """
        Кэшируем на 15 минут
        """
        cache_key = f'role_{self.id}_users_count'
        users_count = cache.get(cache_key)
        if users_count is None:
            users_count = self.users.count()
            cache.set(cache_key, users_count, timeout=60 * 15)
        return users_count

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = _('role')
        verbose_name_plural = _('roles')
        ordering = ['id', ]
        db_table = 'users_role'


class UserRoleDepartment(BaseUUIDModel):
    """
    В таблицу попадают данные в случае выбора роли 'Администратор аккаунта'.
    """
    user = models.ForeignKey(
        to=User,
        verbose_name=_('user'),
        on_delete=models.CASCADE,
        related_name='role_departments',
    )
    role = models.ForeignKey(
        to=Role,
        verbose_name=_('role'),
        on_delete=models.CASCADE,
        related_name='user_role_departments',
    )
    department = models.ForeignKey(
        to=Department,
        verbose_name=_('department'),
        on_delete=models.CASCADE,
        related_name='role_user_departments',
    )

    def __str__(self):
        return '%s - %s' % (self.role.name, self.department.name)

    class Meta:
        db_table = 'users_user_role_departments'


@receiver([post_save, post_delete], sender=User)
def update_user_related_cache(sender, instance, **kwargs):
    # Количество пользователей в департаменте
    # if instance.department:
    #     department_cache_key = f'department_{instance.department.id}_users_count'
    #     cache.delete(department_cache_key)

    # Количество пользователей в роле
    for role in instance.roles.all():
        role_cache_key = f'role_{role.id}_users_count'
        cache.delete(role_cache_key)

    # Количество группы пользователя
    groups_cache_key = f'user_{instance.id}_groups_count'
    cache.delete(groups_cache_key)


class UserSupervisorSettings(BaseUUIDModel):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='supervisor_settings')
    expiring_certificates = models.BooleanField(default=False)
    expired_certificates = models.BooleanField(default=False)
    inactive_on_portal = models.BooleanField(default=False)
    min_training_percentage = models.PositiveSmallIntegerField(default=0)
    max_training_percentage = models.PositiveSmallIntegerField(default=100)

    def __str__(self):
        return f'Настройки ({self.user})'