import logging

import ldap
from django.conf import settings
from django.contrib.auth.backends import ModelBackend

from users.models import User, Role

logger = logging.getLogger("app")


class LDAPConnector:
    def __init__(self, server_uri: str, username: str, password: str):
        """
        Инициализация с параметрами подключения к LDAP.
        """
        self.set_options()
        self.connection = None
        self.server_uri = server_uri
        self.username = username
        self.password = password

    def connect(self):
        """
        Устанавливает соединение с сервером LDAP.
        """
        try:
            self.connection = ldap.initialize(self.server_uri)
            self.connection.set_option(ldap.OPT_REFERRALS, 0)
            self.connection.set_option(ldap.OPT_PROTOCOL_VERSION, 3)
            self.connection.set_option(ldap.OPT_X_TLS, ldap.OPT_X_TLS_DEMAND)
            self.connection.set_option(ldap.OPT_X_TLS_DEMAND, True)
            self.connection.set_option(ldap.OPT_DEBUG_LEVEL, 255)
            self.connection.simple_bind_s("{}@hq.bc".format(self.username), self.password)
        except ldap.LDAPError as e:
            logger.warning(f'Не удалось подключиться к серверу LDAP: {e}')

    def disconnect(self):
        """
        Закрывает соединение с сервером LDAP.
        """
        if self.connection:
            self.connection.unbind_s()

    def get_connection(self):
        """
        Возвращает активное соединение.
        """
        if not self.connection:
            self.connect()
        return self.connection

    @staticmethod
    def set_options():
        ldap.set_option(ldap.OPT_X_TLS_REQUIRE_CERT, ldap.OPT_X_TLS_NEVER)


class LDAPSearcher:
    def __init__(self, connector: LDAPConnector, base_dn: str):
        """
        Инициализация с объектом подключения и базовым DN для поиска.
        """
        self.connector = connector
        self.base_dn = base_dn

    def search_user(self, username: str):
        """
        Ищет пользователя в LDAP по sAMAccountName.
        """
        connection = self.connector.get_connection()
        if not connection:
            return None

        search_filter = f"(sAMAccountName={username})"
        try:
            result = connection.search_s(self.base_dn, ldap.SCOPE_SUBTREE, search_filter)
            if not result:
                logger.warning(f'Пользователь Ldap не найден: {username}')
                return None
            return result[0]
        except ldap.LDAPError as e:
            logger.warning(f'Не удалось найти пользователя {username}: {e}')
            return None


class LDAPUserService:
    def __init__(self, server_uri: str, username: str, password: str, base_dn: str):
        """
        Инициализация с параметрами для подключения и поиска в LDAP.
        """
        self.connector = LDAPConnector(server_uri, username, password)
        self.searcher = LDAPSearcher(self.connector, base_dn)

    def find_user(self, username: str):
        """
        Находит пользователя по username, возвращая его атрибуты.
        """
        self.connector.connect()  # Подключение к серверу
        user = self.searcher.search_user(username)
        self.connector.disconnect()  # Закрытие подключения
        return user


class LDAPAuthenticationBackend(ModelBackend):
    """
    Аутентификации через LDAP.
    """

    def authenticate(self, request, username=None, password=None, **kwargs):
        # Параметры подключения
        server_uri = settings.AUTH_LDAP_SERVER_URI
        base_dn = settings.AUTH_LDAP_BASE_DN

        ldap_service = LDAPUserService(server_uri, username, password, base_dn)
        user_data = ldap_service.find_user(username)
        if not user_data:
            return None

        attributes = user_data[1]
        decode_field = lambda field: attributes.get(field, [b''])[0].decode('utf-8')

        email = decode_field('mail')
        first_name = decode_field('givenName')
        last_name = decode_field('sn')

        user, created = User.objects.get_or_create(
            username=username,
            defaults={
                'email': email,
                'first_name': first_name,
                'last_name': last_name,
                'is_active': True,
                'is_staff': True,
            }
        )

        if created:
            user.set_unusable_password()
            user.save(update_fields=['password'])

            user.roles.add(Role.objects.get(code='learner'))

        logger.info(
            f'Пользователь {username} успешно аутентифицирован. {"Создан" if created else "Обновлён"}.'
        )
        return user