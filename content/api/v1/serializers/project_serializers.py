from content.models import Resource, Participant
from rest_framework import serializers

from users.api.v1.serializers import UserSerializer
from users.models import User


class ProjectSerializer(serializers.ModelSerializer):
    class Meta:
        model = Resource
        fields = ("id", "name", "type")


class ProjectCreateSerializer(serializers.Serializer):
    name = serializers.CharField(required=True, max_length=255)
    type = serializers.CharField(read_only=True)
    owner = serializers.PrimaryKeyRelatedField(read_only=True)

    def create(self, validated_data):
        project = Resource.objects.create(
            name=validated_data["name"],
            type=Resource.FOLDER,
            owner=self.context.user,
        )
        return project


class ProjectRetrieveSerializer(serializers.ModelSerializer):
    type = serializers.CharField()
    owner = UserSerializer()

    class Meta:
        model = Resource
        fields = ("id", "name", "type", "owner")


class ProjectUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Resource
        fields = ("id", "name", "payload")


class ParticipantSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)

    class Meta:
        model = Participant
        fields = ("id", "availability", "user")


class ParticipantBulkCreateSerializer(serializers.Serializer):
    user_ids = serializers.ListField(
        child=serializers.PrimaryKeyRelatedField(queryset=User.objects.all()),
        allow_empty=False,
    )
    availability = serializers.ChoiceField(choices=Participant.AVAILABILITY_CHOICES)

    def validate(self, attrs):
        resource = self.context.get("resource")
        if not resource:
            raise serializers.ValidationError("Resource is required in context.")

        errors = {}
        for user in attrs["user_ids"]:
            if user == resource.owner:
                errors.setdefault("user_ids", []).append(
                    f"User {user.id} is the owner and cannot be a participant."
                )
        if errors:
            raise serializers.ValidationError(errors)

        return attrs


class ParticipantTypeUpdateSerializer(serializers.Serializer):
    availability = serializers.ChoiceField(choices=Participant.AVAILABILITY_CHOICES)

    def update(self, instance, validated_data):
        instance.availability = validated_data.get(
            "availability", instance.availability
        )
        instance.save()

        return instance
