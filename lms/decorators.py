import logging
from django.core.files.storage import default_storage
from django.db.models.signals import post_delete, pre_save

from lms.s3_storage import PublicMediaStorage

logger = logging.getLogger('app')


def cleanup_files(field_names):
    """
    Декоратор для автоматического удаления файлов при удалении записи или изменении файла.

    :param field_names: Список имён полей файлов, которые нужно обрабатывать.
    """

    def decorator(model_class):
        def delete_files_on_instance_delete(sender, instance, **kwargs):
            try:
                instance.photo.delete(save=False)
                logger.info("User with id {} photo deleted".format(instance.pk))
            except Exception as e:
                logger.error(
                    "error in delete User , the User id - {} with message - {}".format(instance.pk, str(e)))
                return False

        def delete_files_on_field_update(sender, instance, **kwargs):
            if not instance.pk:
                return False

            try:
                old_file = sender.objects.get(pk=instance.pk).photo
            except sender.DoesNotExist:
                return False

            new_file = instance.photo
            logger.info("User with id {} old file {}".format(instance.pk, old_file))
            logger.info("User with id {} new file {}".format(instance.pk, new_file))

            try:
                if bool(old_file) and not old_file.name == new_file.name:
                    media_storage = PublicMediaStorage()
                    if media_storage.exists(old_file.name):
                        media_storage.delete(old_file.name)
                        logger.info("User with id {} old file deleted".format(instance.pk))
            except Exception as e:
                logger.error("User with id {} file delete error {}".format(instance.pk, str(e)))
                return False

        post_delete.connect(delete_files_on_instance_delete, sender=model_class)
        pre_save.connect(delete_files_on_field_update, sender=model_class)

        return model_class

    return decorator
