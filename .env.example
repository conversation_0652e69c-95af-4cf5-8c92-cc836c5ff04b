DEBUG=True
SECRET_KEY="django-insecure-#g1+%osqpysb)ghmtxd9o-8v6t#kwb_d13^&*qm2irmc65^9i8"
POSTGRES_DB=lms
POSTGRES_USER=user
POSTGRES_PASSWORD=pass
POSTGRES_HOST=127.0.0.1
POSTGRES_PORT=5432
POSTGRES_ROOT_PASSWORD=root

SENTRY_DSN="https://<EMAIL>/459"
SONAR_TOKEN="sqp_8b5f23083fdb07aec02a4fe005d3b3405d69c6f3"
REDIS_DEFAULT="redis://:pass@127.0.0.1:6379/1"
REDIS_SESSION="redis://:pass@127.0.0.1:6379/2"
CELERY_BROKER_URL="redis://:pass@127.0.0.1:6379/3"
CELERY_RESULT_BACKEND="redis://:pass@127.0.0.1:6379/3"

AWS_ACCESS_KEY_ID=9N136Q7FHDEEDO61JL3V
AWS_SECRET_ACCESS_KEY=r5iUug50essQNpMgYK6ujQoaYJeOP9oqOvtqYtT1
AWS_STORAGE_BUCKET_NAME=lms
AWS_S3_ENDPOINT_URL=http://ses.hq.bc
AWS_S3_CUSTOM_DOMAIN=lms.ses.hq.bc
AWS_DEFAULT_ACL=public-read

AWS_STATIC_LOCATION=dev/static
AWS_PRIVATE_MEDIA_LOCATION=dev/media/private
AWS_PUBLIC_MEDIA_LOCATION=dev/media/public

REDIS_SESSION="redis://127.0.0.1:6379/2"
CELERY_BROKER_URL="redis://127.0.0.1:6379/3"
CELERY_RESULT_BACKEND="redis://127.0.0.1:6379/3"

LRS_URL=https://test-university.kaspi.kz/lrsql/xapi
LRS_VERSION=1.0.3
LRS_USERNAME=
LRS_PASSWORD=

FRONTEND_BASE_URL=https://test-university.kaspi.kz