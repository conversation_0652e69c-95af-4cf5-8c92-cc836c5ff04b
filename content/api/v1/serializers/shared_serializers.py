from rest_framework import serializers

from content.models import Resource, Tag, DueDateSettings
from users.api.v1.serializers import UserSerializer


class StatusDataSerializer(serializers.Serializer):
    status = serializers.CharField()
    progress = serializers.IntegerField()
    spentTimeMilli = serializers.IntegerField()


class ParentResourceSerializer(serializers.ModelSerializer):
    class Meta:
        model = Resource
        fields = ("id", "name", "type", "is_deleted")


class ResourceSerializer(serializers.ModelSerializer):
    parent = ParentResourceSerializer(read_only=True)
    owner = UserSerializer(read_only=True)

    class Meta:
        model = Resource
        fields = (
            "id",
            "name",
            "description",
            "type",
            "parent",
            "owner",
            "created_at",
            "updated_at",
            "is_deleted"
        )

class CatalogResourceSerializer(serializers.ModelSerializer):
    parent = ParentResourceSerializer(read_only=True)
    owner = UserSerializer(read_only=True)
    status = serializers.CharField(read_only=True)

    class Meta:
        model = Resource
        fields = (
            "id",
            "name",
            "description",
            "type",
            "parent",
            "owner",
            "created_at",
            "updated_at",
            "is_deleted",
            "status",
        )

class TagSerializer(serializers.ModelSerializer):
    class Meta:
        model = Tag
        fields = ("id", "name")


class DueDateSettingsSerializer(serializers.Serializer):
    type = serializers.ChoiceField(
        choices=DueDateSettings.TYPE_CHOICES,
        label="Тип дедлайна",
        help_text="UNLIMITED — без ограничений; DUE_DATE — до даты; DUE_PERIOD — на срок",
    )
    period = serializers.IntegerField(
        required=False,
        min_value=0,
        label="Срок",
        help_text='Количество единиц времени. Например, "3 недели"',
    )
    period_unit = serializers.ChoiceField(
        required=False,
        label="Единица времени",
        choices=DueDateSettings.PERIOD_UNIT_CHOICES,
        help_text="DAYS — дни, WEEKS — недели, MONTHS — месяцы, YEARS — годы",
    )
    date = serializers.DateField(
        required=False,
        allow_null=True,
        label="Конечная дата",
        help_text="Фиксированная дата дедлайна. Обязательна при типе DUE_DATE.",
    )
    lock_after_due_date = serializers.BooleanField(
        required=False,
        label="Блокировать после дедлайна",
        help_text="Запретить доступ после окончания срока. Только для типов DUE_DATE и DUE_PERIOD.",
    )

    due_date = serializers.ReadOnlyField()

    def validate(self, attrs):
        errors = {}

        if attrs.get("type") == DueDateSettings.TYPE_DUE_DATE:
            if not attrs.get("date"):
                errors["date"] = 'Поле "date" обязательно при типе SELECTED_DATE.'
            if "period" in attrs:
                errors["period"] = 'Поле "period" не допускается при типе DUE_DATE.'
            if "period_unit" in attrs:
                errors["period_unit"] = (
                    'Поле "period_unit" не допускается при типе DUE_DATE.'
                )

        elif attrs.get("type") == DueDateSettings.TYPE_DUE_PERIOD:
            if attrs.get("period") is None:
                errors["period"] = 'Поле "period" обязательно при типе DUE_PERIOD.'
            if not attrs.get("period_unit"):
                errors["period_unit"] = (
                    'Поле "period_unit" обязательно при типе DUE_PERIOD.'
                )
            if "date" in attrs:
                errors["date"] = 'Поле "date" не допускается при типе DUE_PERIOD.'

        elif attrs.get("type") in [
            DueDateSettings.TYPE_DEFAULT,
            DueDateSettings.TYPE_UNLIMITED,
        ]:
            for field in ("period", "period_unit", "date"):
                if field in attrs:
                    errors[field] = (
                        f'Поле "{field}" не допускается при типе {attrs.get("type")}.'
                    )
            if "lock_after_due_date" in attrs and attrs.get("lock_after_due_date"):
                errors["lock_after_due_date"] = (
                    'Поле "lock_after_due_date" применимо только при DUE_DATE или DUE_PERIOD.'
                )

        if errors:
            raise serializers.ValidationError(errors)

        return attrs


class ResourceEnrollmentSerializer(serializers.Serializer):
    resource_id = serializers.PrimaryKeyRelatedField(
        queryset=Resource.objects.all(), source="resource", label="ID Ресурса"
    )
    access_date = serializers.DateTimeField(
        label="Дата доступа",
        help_text="Дата и время начала доступа пользователя к ресурсу.",
    )
    due_date_settings = DueDateSettingsSerializer(label="Настройки дедлайна")