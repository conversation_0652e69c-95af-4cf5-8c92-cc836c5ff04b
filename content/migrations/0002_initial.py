# Generated by Django 5.1.2 on 2025-07-30 12:59

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
        ('content', '0001_initial'),
        ('users', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='catalogaccessdepartment',
            name='department',
            field=models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='users.department'),
        ),
        migrations.AddField(
            model_name='catalog',
            name='access_departments',
            field=models.ManyToManyField(related_name='catalog_access_departments', through='content.CatalogAccessDepartment', to='users.department'),
        ),
        migrations.AddField(
            model_name='catalogaccessgroup',
            name='catalog',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='content.catalog'),
        ),
        migrations.AddField(
            model_name='catalogaccessgroup',
            name='group',
            field=models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='auth.group'),
        ),
        migrations.AddField(
            model_name='catalog',
            name='access_groups',
            field=models.ManyToManyField(related_name='catalog_access_groups', through='content.CatalogAccessGroup', to='auth.group'),
        ),
        migrations.AddField(
            model_name='catalogresource',
            name='catalog',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='catalog_resources', to='content.catalog'),
        ),
        migrations.AddField(
            model_name='certification',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='user'),
        ),
        migrations.AddField(
            model_name='detail',
            name='instructor',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='instructor'),
        ),
        migrations.AddField(
            model_name='enrollment',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='user'),
        ),
        migrations.AddField(
            model_name='duedatesettings',
            name='enrollment',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='due_date_settings', to='content.enrollment', verbose_name='enrollment'),
        ),
        migrations.AddField(
            model_name='certification',
            name='enrollment',
            field=models.OneToOneField(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='certification', to='content.enrollment', verbose_name='enrollment'),
        ),
        migrations.AddField(
            model_name='enrollmentprogress',
            name='enrollment',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='progress_details', to='content.enrollment', verbose_name='enrollment'),
        ),
        migrations.AddField(
            model_name='outline',
            name='parent',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='children', to='content.outline', verbose_name='parent'),
        ),
        migrations.AddField(
            model_name='outlineaccesstime',
            name='outline',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='access_time', to='content.outline', verbose_name='outline'),
        ),
        migrations.AddField(
            model_name='participant',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='user'),
        ),
        migrations.AddIndex(
            model_name='preference',
            index=models.Index(fields=['type'], name='content_pre_type_e5aa29_idx'),
        ),
        migrations.AddField(
            model_name='resource',
            name='owner',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='owner'),
        ),
        migrations.AddField(
            model_name='resource',
            name='parent',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='children', to='content.resource', verbose_name='parent'),
        ),
        migrations.AddField(
            model_name='participant',
            name='resource',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='content.resource', verbose_name='resource'),
        ),
        migrations.AddField(
            model_name='outline',
            name='learning_track',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='learning_tracks', to='content.resource'),
        ),
        migrations.AddField(
            model_name='outline',
            name='resource',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='resources', to='content.resource'),
        ),
        migrations.AddField(
            model_name='enrollmentprogress',
            name='resource',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='content.resource', verbose_name='resource'),
        ),
        migrations.AddField(
            model_name='enrollment',
            name='resource',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='content.resource', verbose_name='resource'),
        ),
        migrations.AddField(
            model_name='detail',
            name='resource',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='content.resource'),
        ),
        migrations.AddField(
            model_name='catalogresource',
            name='resource',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='resource_catalogs', to='content.resource'),
        ),
        migrations.AddField(
            model_name='resourceapplication',
            name='resource',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='resource_applications', to='content.resource'),
        ),
        migrations.AddField(
            model_name='resourceapplication',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='user_applications', to=settings.AUTH_USER_MODEL, verbose_name='user'),
        ),
        migrations.AddField(
            model_name='settings',
            name='preference',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='content.preference'),
        ),
        migrations.AddField(
            model_name='settings',
            name='resource',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='content.resource'),
        ),
        migrations.AddField(
            model_name='detail',
            name='tags',
            field=models.ManyToManyField(blank=True, related_name='details', to='content.tag', verbose_name='tags'),
        ),
        migrations.AlterUniqueTogether(
            name='catalogaccessdepartment',
            unique_together={('catalog', 'department')},
        ),
        migrations.AlterUniqueTogether(
            name='catalogaccessgroup',
            unique_together={('catalog', 'group')},
        ),
        migrations.AddIndex(
            model_name='catalog',
            index=models.Index(fields=['name'], name='catalogs_name_3319ca_idx'),
        ),
        migrations.AddIndex(
            model_name='resource',
            index=models.Index(fields=['type'], name='content_res_type_4900e8_idx'),
        ),
        migrations.AddIndex(
            model_name='participant',
            index=models.Index(fields=['resource', 'user'], name='content_par_resourc_f553bd_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='enrollmentprogress',
            unique_together={('enrollment', 'resource')},
        ),
        migrations.AlterUniqueTogether(
            name='enrollment',
            unique_together={('resource', 'user')},
        ),
        migrations.AlterUniqueTogether(
            name='catalogresource',
            unique_together={('catalog', 'resource')},
        ),
        migrations.AlterUniqueTogether(
            name='resourceapplication',
            unique_together={('user', 'resource')},
        ),
        migrations.AlterUniqueTogether(
            name='settings',
            unique_together={('preference', 'resource')},
        ),
    ]
