import logging

from dateutil.relativedelta import relativedelta
from django.db import transaction
from django.db.models import QuerySet
from django.utils import timezone

from content.models import DueDateSettings, Enrollment, Preference, Settings

logger = logging.getLogger("app")


class ResourceReassignService:
    """
    Сервис для автоматического переназначения курса в двух случаях:
    1. Через {X} дней/недель/месяцев/лет после окончания курсов -> before_certificate_expire
    2. За {X} дней/недель/месяцев/лет до окончания сертификата -> after_completion

    Сервис можно использовать для регулярных курсов, которые необходимо проходить/назначать 2 или более раза.

    Использует настройки(preferences) и связанные с ними параметры (payload) для переназначения курса.
    """

    def __init__(self):
        self.preferences_ids: list[int] = []
        self.resource_settings: QuerySet[Settings] = []
        self.resource_reassignment: str = None
        self.resource_reassignment_type: str = None
        self.resource_reassignment_units: str = None
        self.resource_reassignment_value: int = None
        self.resource_id: int = None

    def run(self):
        """
        Основной метод запуска переназначения.

        Находит все активные настройки, где существуют значения:
        resource_reassignment: bool = True | False
        resource_reassignment_type: str = 'after_completion' | 'before_certificate_expire'

        И если заданы настройки переназначения "после прохождения курса" или "до истечения сертификата",
        создает новые назначения курса/лонгрида/ресурса по заданным настройкам
        """
        logger.info("[CourseReassignService]: Начато переназначение курсов")

        self._load_preferences_ids()
        self._load_resource_settings()

        for resource_setting in self.resource_settings:
            self.resource_id: int = resource_setting.resource.id
            self._load_reassignment_values(resource_setting)
            target_date = self._get_target_date()

            if (
                self.resource_reassignment_type == "after_completion"
                and self.resource_reassignment == "true"
            ):
                self._reassign_after_completion(target_date=target_date)

            elif (
                self.resource_reassignment_type == "before_certificate_expire"
                and self.resource_reassignment == "true"
            ):
                self._reassign_before_certificate_expire(target_date=target_date)

    def _load_preferences_ids(self):
        self.preferences_ids: list[int] = Preference.objects.filter(
            section=Preference.COMPLETION, code__icontains="REASSIGN"
        ).values_list("id", flat=True)
        if not self.preferences_ids:
            logger.warning(
                "[CourseReassignService]: Не найдены настройки для переназначения, задача остановлена."
            )
            return None

    def _load_resource_settings(self):
        self.resource_settings: QuerySet[Settings] = Settings.objects.filter(
            preference__id__in=self.preferences_ids
        ).select_related("resource")
        if not self.resource_settings:
            logger.warning(
                "[CourseReassignService]: Не найдены настройки ресурсов, задача остановлена."
            )
            return None

    def _load_reassignment_values(self, resource_setting: Settings):
        self._get_resource_reassignment(resource_setting)
        self._get_resource_reassignment_type(resource_setting)
        self._get_resource_reassignment_units(resource_setting)
        self._get_resource_reassignment_value(resource_setting)

    def _get_resource_reassignment(self, resource_setting: Settings):
        self.resource_reassignment: str = resource_setting.payload.get(
            "resource_reassignment", None
        )
        if not self.resource_reassignment or self.resource_reassignment == "false":
            logger.warning("[CourseReassignService]: Переназначение == false")

    def _get_resource_reassignment_type(self, resource_setting: Settings):
        """
        resource_reassignment_type: str = 'after_completion' | 'before_certificate_expire'
        """
        self.resource_reassignment_type: str = resource_setting.payload.get(
            "resource_reassignment_type", None
        )
        if not self.resource_reassignment_type:
            logger.warning(
                "[CourseReassignService]: Неизвестный тип для переназначения"
            )

    def _get_resource_reassignment_units(self, resource_setting: Settings):
        self.resource_reassignment_units: str = resource_setting.payload.get(
            f"{self.resource_reassignment_type}_units", None
        )
        if not self.resource_reassignment_units:
            logger.warning('[CourseReassignService]" Не существует значения для units')

    def _get_resource_reassignment_value(self, resource_setting: Settings):
        self.resource_reassignment_value: int = resource_setting.payload.get(
            f"{self.resource_reassignment_type}_value", None
        )
        if not self.resource_reassignment_value:
            logger.warning("[CourseReassignService]: Не существует значения для value")

    @staticmethod
    def _get_date_offset(units: str, value: int) -> timezone.timedelta | relativedelta:
        """
        Возвращает относительное время, например:
        Если units = days, value = 7

        return 7 DAYS
        """
        if units.endswith("days"):
            return timezone.timedelta(days=value)
        elif units.endswith("weeks"):
            return timezone.timedelta(weeks=value)
        elif units.endswith("months"):
            return relativedelta(months=value)
        elif units.endswith("years"):
            return relativedelta(years=value)
        else:
            logger.warning(f"[CourseReassignService]: Неверный тип для {units}")

    def _get_target_date(self):
        offset = self._get_date_offset(
            units=self.resource_reassignment_units,
            value=self.resource_reassignment_value,
        )
        target_date = timezone.localdate() - offset
        return target_date

    def _reassign_after_completion(self, target_date):
        enrollments: QuerySet[Enrollment] = Enrollment.objects.filter(
            status__in=[Enrollment.FINISHED, Enrollment.FINISHED_MANUALLY],
            resource_id=self.resource_id,
            completed_at__isnull=False,
            completed_at__date=target_date,
        ).select_related('due_date_settings')
        self._create_new_enrollments(enrollments=enrollments)

    def _reassign_before_certificate_expire(self, target_date):
        enrollments: QuerySet[Enrollment] = Enrollment.objects.filter(
            resource_id=self.resource_id,
            certification__expired_date=target_date,
        ).select_related("due_date_settings")
        self._create_new_enrollments(enrollments=enrollments)

    @staticmethod
    def _create_new_enrollments(enrollments: QuerySet[Enrollment]) -> None:
        old_settings_map: dict = {
            (e.resource_id, e.user_id): e.due_date_settings
            for e in enrollments
            if hasattr(e, "due_date_settings")
        }
        new_enrollments = [
            Enrollment(
                resource=e.resource,
                user=e.user,
                access_date=e.access_date,
                status=Enrollment.NOT_STARTED,
            )
            for e in enrollments
        ]
        with transaction.atomic():
            created_enrollments = Enrollment.objects.bulk_create(new_enrollments)
            due_date_settings = []
            for new_enrollment in created_enrollments:
                old_settings = old_settings_map.get(
                    (new_enrollment.resource_id, new_enrollment.user_id)
                )
            due_date_settings.append(
                DueDateSettings(
                    enrollment=new_enrollment,
                    type=old_settings.type if old_settings else DueDateSettings.TYPE_DEFAULT,
                    period=old_settings.period if old_settings else 0,
                    period_unit=old_settings.period_unit if old_settings else None,
                    date=old_settings.date if old_settings else None,
                    lock_after_due_date=old_settings.lock_after_due_date
                    if old_settings
                    else None,
                )
            )
            DueDateSettings.objects.bulk_create(due_date_settings)
        logger.info(
            f"[CourseReassignService]: Для Ресурса создано и переназначено {len(created_enrollments)} Enrollment"
        )
