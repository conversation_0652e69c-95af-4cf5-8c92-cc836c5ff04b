import os.path

from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers
from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response

ALLOWED_EXTENSIONS = [".jpg", ".jpeg", ".png", ".svg"]


def validate_file_extension(value):
    ext = os.path.splitext(value.name)[1].lower()
    if ext not in ALLOWED_EXTENSIONS:
        raise ValidationError(
            _("Unsupported file extension. Allowed extensions are: %(extensions).")
            % {"extensions": "".join(ALLOWED_EXTENSIONS)},
        )


class CustomPagination(PageNumberPagination):
    page_size_query_param = "page_size"

    def get_paginated_response(self, data):
        return Response(
            {
                "count": self.page.paginator.count,
                "page_size": self.get_page_size(self.request),
                "total_pages": self.page.paginator.num_pages,
                "current_page": self.page.number,
                "results": data,
            }
        )


def get_paginated_serializer(serializer_class):
    class_name = f"{serializer_class.__name__}Paginated"
    """
    Данный класс нужен только для того, чтобы корректно показывать `Response body` в Swagger.

    Принимает "serializer_class" и возвращает класс(serializers.Serializer) сериализатор с полями для пагинации и сериализации
    """

    class PaginatedSerializer(serializers.Serializer):
        count = serializers.IntegerField()
        page_size = serializers.IntegerField()
        total_pages = serializers.IntegerField()
        current_page = serializers.IntegerField()
        results = serializer_class(many=True)

        class Meta(serializer_class.Meta):
            ref_name = class_name

    return PaginatedSerializer
