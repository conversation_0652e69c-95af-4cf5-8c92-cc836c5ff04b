from django import forms

from content.models import Resource, DueDateSettings


class ResourceFrom(forms.ModelForm):
    class Meta:
        model = Resource
        fields = '__all__'


class DueDateSettingsForm(forms.ModelForm):
    lock_after_due_date = forms.TypedChoiceField(
        label='Запретить доступ',
        choices=(
            (False, 'Не запрещать'),
            (True, 'После окончания срока выполнения'),
        ),
        coerce=lambda x: x == 'True',
        widget=forms.Select
    )

    class Meta:
        model = DueDateSettings
        fields = '__all__'