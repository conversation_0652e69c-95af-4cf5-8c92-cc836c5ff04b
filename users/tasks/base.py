import logging
import requests
from typing import Dict, Any, Optional, List
from django.conf import settings
from django.db import transaction

from .exceptions import APIException, ImportException

logger = logging.getLogger("app.tasks")


class BaseImportTask:
    """Базовый класс для задач импорта"""

    def __init__(self, dry_run: bool = False):
        self.dry_run = dry_run
        self.logger = logger

        # Статистика выполнения
        self.stats = {
            'created': 0,
            'updated': 0,
            'skipped': 0,
            'errors': []
        }

    def make_api_request(self, endpoint: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """Общий метод для запросов к API"""
        try:
            response = requests.get(
                f"{settings.CORE_API_URL}{endpoint}",
                headers={'X-Api-Key': settings.CORE_API_KEY},
                params=params or {},
                timeout=30
            )
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            self.logger.exception(f"❌ Ошибка при запросе {endpoint}: {str(e)}")
            raise APIException(f"API request failed: {str(e)}")

    def log_start(self, task_name: str):
        """Логирование начала задачи"""
        self.logger.info(f"[{task_name}] 🚀 Запуск импорта. dry_run={self.dry_run}")

    def log_results(self, task_name: str):
        """Логирование результатов выполнения"""
        self.logger.info(
            f"✅ [{task_name}] Завершено: создано {self.stats['created']}, "
            f"обновлено {self.stats['updated']}, пропущено {self.stats['skipped']}"
        )

        if self.stats['errors']:
            self.logger.warning(f"⚠ [{task_name}] Ошибок: {len(self.stats['errors'])}")
            for error in self.stats['errors']:
                self.logger.warning(error)

        if self.dry_run:
            self.logger.warning(f"💡 [{task_name}] dry-run: изменения НЕ сохранены")

    def add_error(self, error_msg: str):
        """Добавление ошибки в статистику"""
        self.stats['errors'].append(error_msg)