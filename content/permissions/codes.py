from core.permissions import HasPermissionCode


class CanViewAndAssignMaterialsToUsers(HasPermissionCode):
    permission_code = 'viewing-and-assigning-materials-to-users'


class CanCheckTasks(HasPermissionCode):
    permission_code = 'checking-tasks'


class CanModerateTrainingApplicationsFromCatalog(HasPermissionCode):
    permission_code = 'moderation-of-applications-for-training-from-the-catalog'


class CanManageSurveillanceSheets(HasPermissionCode):
    permission_code = 'managing-surveillance-sheets'


class CanFillObservationSheets(HasPermissionCode):
    permission_code = 'filling-out-the-observation-sheets'


class CanViewEducationalMaterials(HasPermissionCode):
    permission_code = 'viewing-educational-materials'


class CanCreateAndEditEducationalMaterials(HasPermissionCode):
    permission_code = 'creating-and-editing-educational-materials'


class CanDeleteEducationalMaterials(HasPermissionCode):
    permission_code = 'deleting-educational-materials'


class CanCreateAndManageProjectInTrainingMaterialsSection(HasPermissionCode):
    permission_code = 'project-creation-and-management-in-the-training-materials-section'
