from django.db.models import Q
from django_filters import rest_framework as filters

from reviews.models import Review


class NumberInFilter(filters.BaseInFilter, filters.NumberFilter):
    pass

class UUIDInFilter(filters.BaseInFilter, filters.UUIDFilter):
    pass

class ReviewFilter(filters.FilterSet):
    resource_id = filters.UUIDFilter(
        field_name="resource_id", lookup_expr="exact"
    )
    user = UUIDInFilter(
        field_name="user", lookup_expr="in"
    )
    rating = NumberInFilter(
        field_name="rating", lookup_expr="in"
    )
    search = filters.CharFilter(method='filter_by_user_info')

    def filter_by_user_info(self, queryset, name, value):
        qs = queryset
        for term in value.split():
            qs = qs.filter(
                Q(user__username__icontains=term)    |
                Q(user__email__icontains=term)       |
                Q(user__first_name__icontains=term)  |
                Q(user__last_name__icontains=term)   |
                Q(user__middle_name__icontains=term)
            )
        return qs

    class Meta:
        model = Review
        fields = [
            'resource_id',
            'user',
            'rating',
            'search',
        ]
