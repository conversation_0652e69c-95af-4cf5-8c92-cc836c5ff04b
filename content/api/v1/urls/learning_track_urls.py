from django.urls import path

from content.api.v1.views import (
    LearningTrackOutlineAPIView,
    LearningTrackAddStageAPIView,
    LearningTrackDeleteStageAPIView,
    LearningTrackAddCoursesAPIView,
    LearningTrackUpdateCourseAvailabilityAPIView
)

urlpatterns = [
    path('<uuid:pk>/outline/', LearningTrackOutlineAPIView.as_view()),
    path('<uuid:pk>/add-stage/', LearningTrackAddStageAPIView.as_view()),
    path('<uuid:pk>/remove-stage/', LearningTrackDeleteStageAPIView.as_view()),
    path('<uuid:pk>/stage/<int:stage_id>/add-courses/', LearningTrackAddCoursesAPIView.as_view()),
    path('<uuid:pk>/stage/<int:stage_id>/update-courses-availabilty/',
         LearningTrackUpdateCourseAvailabilityAPIView.as_view()),
]