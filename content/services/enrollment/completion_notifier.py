import logging

from content.models import Settings, Preference, Enrollment
from lms import settings
from notifications.models import ChannelType
from notifications.services import NotificationService
from users.models import User

logger = logging.getLogger("app")


class EnrollmentCompletionNotificationService:

    def __init__(self, enrollment_id):
        self.enrollment_id = enrollment_id
        self.enrollment = None
        self.notification_service = NotificationService()
        self.preferences = {}
        self.settings = {}

    def _find_enrollment(self):
        self.enrollment = Enrollment.objects.select_related('resource', 'user').get(id=self.enrollment_id)

    def _load_preferences(self):
        """ Загружает настройки: завершения и непрохождения курса. """
        base_code = self.enrollment.resource.type
        codes = [
            f"{base_code}_COMPLETION_NOTIFICATION",
            f"{base_code}_NOT_PASSED_NOTIFICATION",
        ]
        self.preferences = {
            p.code: p
            for p in Preference.objects.filter(section=Preference.NOTIFICATION, code__in=codes)
        }

    def _load_settings(self):
        """ Загружает Settings по каждому Preference. """
        for code, pref in self.preferences.items():
            setting = Settings.objects.filter(
                resource_id=self.enrollment.resource.id,
                preference_id=pref.id
            ).first()
            if setting:
                self.settings[code] = setting

    def _get_frontend_my_team_url(self):
        return f"{settings.FRONTEND_BASE_URL}/mobile/profile/my-team"

    def send_completion_notifications(self):
        """ Создает уведомления в зависимости от настройки ресурса,
            уведомление о завершении и уведомление о не прохождении теста
        """
        self._find_enrollment()
        self._load_preferences()
        self._load_settings()

        for code, setting in self.settings.items():
            payload = setting.payload
            notification = payload.get('notification', {})
            notify = payload.get('notify', {})

            if not notify:
                continue

            try:
                users_data = notification.get('users', [])
                subject = notification.get('subject')
                message = notification.get('message')
                context = {
                    'username':        self.enrollment.user.full_name,
                    'content_title':   self.enrollment.resource.name,
                    'course_info_url': self._get_frontend_my_team_url(),
                }
                emails = [u.get('email') for u in users_data if u.get('email')]
                users = list(User.objects.filter(email__in=emails))

                if not users:
                    logger.warning(
                        f"[EnrollmentCompletionNotificationService] Не найдено ни одного пользователя по email-ам: {emails}")
                    return

                self.notification_service.create_notifications_batch(
                    channel=ChannelType.EMAIL,
                    users=users,
                    subject=subject,
                    content=message,
                    context=context,
                    content_object=self.enrollment,
                )

                logger.info(
                    f"[EnrollmentCompletionNotificationService] Notification '{code}' sent successfully")
            except Exception as e:
                logger.error(
                    f"[EnrollmentCompletionNotificationService] Error processing code={code} for enrollment={self.enrollment.id}: {e}",
                    exc_info=True)
