from django.core.files.uploadedfile import SimpleUploadedFile
from rest_framework.test import APITestCase

from users.api.v1.serializers import CountrySerializer, StatusSerializer
from users.models import Country, Status


class CountrySerializerTest(APITestCase):
    def setUp(self):
        self.country = Country.objects.create(name='Almaty')

    def test_country_serializer_valid(self):
        serializer = CountrySerializer(instance=self.country)
        self.assertTrue(serializer.data, {'id': self.country.id, 'name': self.country.name})

    def test_country_serializer_invalid(self):
        invalid_data = {'name': ''}
        serializer = CountrySerializer(data=invalid_data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('name', serializer.errors)

    def test_country_serializer_empty(self):
        serializer = CountrySerializer(data={})
        self.assertFalse(serializer.is_valid())
        self.assertIn('name', serializer.errors)


class StatusSerializerTest(APITestCase):
    def setUp(self):
        self.status = Status.objects.create(name='On vacation', file=None)

    def test_status_serializer_valid(self):
        serializer = StatusSerializer(instance=self.status)
        self.assertTrue(serializer.data, {'id': self.status.id, 'name': self.status.name, 'file': None})

    def test_status_serializer_invalid(self):
        invalid_data = {'name': '', 'file': None}
        serializer = StatusSerializer(data=invalid_data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('name', serializer.errors)

    def test_status_serializer_with_file(self):
        test_file = SimpleUploadedFile('file.jpg', b'content')
        valid_data = {'name': 'Test Status with File', 'file': test_file}
        serializer = StatusSerializer(data=valid_data)
        self.assertTrue(serializer.is_valid())
        status_instance = serializer.save()
        self.assertEqual(status_instance.name, 'Test Status with File')
        self.assertIsNotNone(status_instance.file)
        self.assertTrue(status_instance.file.name.startswith('users/statuses/'))

    def test_status_serializer_with_readonly_id(self):
        data = {'id': 999, 'name': 'Test Status with ReadOnly', 'file': None}
        serializer = StatusSerializer(instance=self.status, data=data, partial=True)
        self.assertTrue(serializer.is_valid())
        self.assertNotIn('id', serializer.validated_data)

    def test_status_serializer_empty_data(self):
        serializer = StatusSerializer(data={})
        self.assertFalse(serializer.is_valid())
        self.assertIn('name', serializer.errors)

    def test_valid_file_extension(self):
        valid_file = SimpleUploadedFile('file.jpg', b'content')
        data = {'name': 'Test Status with File', 'file': valid_file}
        serializer = StatusSerializer(data=data)
        self.assertTrue(serializer.is_valid())

    def test_valid_file_extension_uppercase(self):
        valid_file = SimpleUploadedFile('file.JPG', b'content')
        data = {'name': 'Test Status with File', 'file': valid_file}
        serializer = StatusSerializer(data=data)
        self.assertTrue(serializer.is_valid())

    def test_invalid_file_extension(self):
        invalid_file = SimpleUploadedFile('file.bpm', b'content', content_type='image/bpm')
        data = {'name': 'Test Status with File', 'file': invalid_file}
        serializer = StatusSerializer(data=data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('file', serializer.errors)
        self.assertEqual(
            serializer.errors['file'][0], 'Unsupported file extension. Allowed extensions are: .jpg, .jpeg, .png, .svg.'
        )

    def test_missing_file(self):
        data = {'name': 'Test Status with File', 'file': None}
        serializer = StatusSerializer(data=data)
        self.assertTrue(serializer.is_valid())
